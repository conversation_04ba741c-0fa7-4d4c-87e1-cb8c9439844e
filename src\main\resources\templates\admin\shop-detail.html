<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Details - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }
    </style>
</head>
<body class="admin-body">

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar(activeMenu='seller-approval')}"></div>
        </div>

        <div class="col-lg-9">
            <main class="py-4" th:if="${shop != null}">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title" th:text="'Shop Detail: ' + ${shop.shopName}"></h3>
                    <a th:href="@{/admin/seller-approvals}" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i>Back to Queue</a>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <img th:if="${shop.coverImageUrl != null and !shop.coverImageUrl.isEmpty()}"
                                 th:src="${shop.coverImageUrl}" class="card-img-top" alt="Shop Cover Image"
                                 style="height: 200px; object-fit: cover;">

                            <div class="card-header border-0 d-flex align-items-center">
                                <img th:if="${shop.logoUrl != null and !shop.logoUrl.isEmpty()}"
                                     th:src="${shop.logoUrl}" alt="Shop Logo"
                                     class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">

                                <div th:if="${shop.logoUrl == null or shop.logoUrl.isEmpty()}"
                                     class="rounded me-3 d-flex align-items-center justify-content-center bg-light"
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-store text-muted fs-2"></i>
                                </div>

                                <h4 class="mb-0">Shop Information</h4>
                            </div>
                            <div class="card-body pt-0">
                                <dl class="row">
                                    <dt class="col-sm-3 detail-label">Shop Name</dt>
                                    <dd class="col-sm-9" th:text="${shop.shopName}"></dd>

                                    <dt class="col-sm-3 detail-label">Description</dt>
                                    <dd class="col-sm-9" th:text="${shop.description}"></dd>

                                    <dt class="col-sm-3 detail-label">Address</dt>
                                    <dd class="col-sm-9" th:text="${shop.shopDetailAddress + ', ' + shop.shopWard + ', ' + shop.shopDistrict + ', ' + shop.shopProvince}"></dd>

                                    <dt class="col-sm-3 detail-label">Tax Code</dt>
                                    <dd class="col-sm-9" th:text="${shop.taxCode}"></dd>

                                    <dt class="col-sm-3 detail-label">Identification</dt>
                                    <dd class="col-sm-9">
                                        <a th:if="${shop.identificationFileUrl != null}" th:href="${shop.identificationFileUrl}" target="_blank" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-file-alt me-2"></i>View Submitted Document
                                        </a>
                                        <span th:if="${shop.identificationFileUrl == null}" class="text-muted">Not provided</span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header"><h4>Owner Information</h4></div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <img th:if="${shop.user.profilePicUrl != null and !shop.user.profilePicUrl.isEmpty()}"
                                         th:src="${shop.user.profilePicUrl}" alt="Owner Profile Picture"
                                         class="rounded-circle" style="width: 100px; height: 100px; object-fit: cover; border: 3px solid #eee;">

                                    <div th:if="${shop.user.profilePicUrl == null or shop.user.profilePicUrl.isEmpty()}"
                                         class="d-inline-flex align-items-center justify-content-center bg-light rounded-circle"
                                         style="width: 100px; height: 100px;">
                                        <i class="fas fa-user text-muted fs-2"></i>
                                    </div>
                                </div>
                                <dl>
                                    <dt class="detail-label">Full Name</dt>
                                    <dd th:text="${shop.user.fullName}"></dd>
                                    <dt class="detail-label">Contact Email</dt>
                                    <dd th:text="${shop.contactEmail}"></dd>
                                    <dt class="detail-label">Contact Phone</dt>
                                    <dd th:text="${shop.contactPhone}"></dd>
                                </dl>
                            </div>
                           </div>
                        </div>

                        <div class="card">
                            <div class="card-header"><h4>Actions</h4></div>
                            <div class="card-body text-center d-grid gap-2">
                                <form th:action="@{'/admin/seller-approvals/approve/' + ${shop.shopId}}" method="post">
                                    <input type="hidden" name="returnUrl" th:value="@{'/admin/shops/detail/' + ${shop.shopId}}">

                                    <button type="submit" class="btn btn-success w-100" ...>
                                        <i class="fas fa-check-circle me-2"></i>Approve Application
                                    </button>
                                </form>

                                <button type="button" class="btn btn-danger w-100" ...>
                                    <i class="fas fa-times-circle me-2"></i>Reject Application
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="rejectForm" method="post">
                <div class="modal-body">
                    <input type="hidden" name="returnUrl" th:value="@{'/admin/shops/detail/' + ${shop.shopId}}">

                    <p>You are about to reject... <strong id="shopNameToReject"></strong></p>
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">...</label>
                        <textarea class="form-control" id="rejectionReason" name="reason" rows="3" required></textarea>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
    const rejectModal = document.getElementById('rejectModal');
    if(rejectModal) {
        rejectModal.addEventListener('show.bs.modal', event => {
            const button = event.relatedTarget;
            const shopId = button.getAttribute('data-bs-shop-id');
            const shopName = button.getAttribute('data-bs-shop-name');

            const modalTitle = rejectModal.querySelector('.modal-title');
            const shopNameSpan = rejectModal.querySelector('#shopNameToReject');
            const form = rejectModal.querySelector('#rejectForm');

            if(modalTitle) modalTitle.textContent = 'Reject Application for ' + shopName;
            if(shopNameSpan) shopNameSpan.textContent = shopName;
            if(form) form.action = '/admin/seller-approvals/reject/' + shopId;
        });
    }
</script>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

</body>
</html>