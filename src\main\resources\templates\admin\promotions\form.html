<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${isEdit ? 'Edit Promotion' : 'Create Promotion'} + ' - ReadHub'">Create Promotion - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .form-section {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .form-section-title {
            color: #2C3E50;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 8px;
        }
        .scope-selection {
            display: none;
        }
        .scope-selection.active {
            display: block;
        }
        .select2-container {
            width: 100% !important;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        .field-disabled {
            background-color: #f8f9fa !important;
            opacity: 0.6;
            cursor: not-allowed;
        }
        .edit-restriction-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .vnd-input {
            text-align: right;
        }
    </style>
</head>
<body>
    <!-- Include Topbar -->
    <div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

    <div class="container my-4">
        <div class="row">
            <!-- Include Sidebar -->
            <div class="col-lg-3 mb-4">
                <div th:replace="~{fragments/admin-sidebar :: admin-sidebar(activeMenu='promotions')}"></div>
            </div>

            <div class="col-lg-9">
                <main class="py-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 class="section-title" th:text="${isEdit ? 'Edit Promotion' : 'Create New Promotion'}">Create New Promotion</h3>
                        <a href="/admin/promotions" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>

        <!-- Form -->
        <form th:action="${isEdit ? '/admin/promotions/' + promotion.promotionId + '/edit' : '/admin/promotions/create'}"
              method="post" th:object="${promotionForm}" novalidate>

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-info-circle"></i> Basic Information
                        </h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Promotion Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" th:field="*{name}"
                                   th:class="${#fields.hasErrors('name')} ? 'form-control is-invalid' : 'form-control'"
                                   placeholder="Enter promotion name">
                            <div th:if="${#fields.hasErrors('name')}" class="invalid-feedback" th:errors="*{name}"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="code" class="form-label">Promotion Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="code" th:field="*{code}"
                                   th:class="${#fields.hasErrors('code')} ? 'form-control is-invalid' : 'form-control'"
                                   placeholder="SAVE20" style="text-transform: uppercase;">
                            <div class="form-text">3-50 characters, uppercase letters, numbers, underscore, or dash only</div>
                            <div th:if="${#fields.hasErrors('code')}" class="invalid-feedback" th:errors="*{code}"></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" th:field="*{description}" rows="3"
                                      th:class="${#fields.hasErrors('description')} ? 'form-control is-invalid' : 'form-control'"
                                      placeholder="Describe the promotion details"></textarea>
                            <div th:if="${#fields.hasErrors('description')}" class="invalid-feedback" th:errors="*{description}"></div>
                        </div>
                    </div>
                </div>
            </div>

                    <!-- Discount Configuration -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-percentage"></i> Discount Configuration
                        </h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="promotionType" class="form-label">Promotion Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="promotionType" th:field="*{promotionType}"
                                    th:class="${#fields.hasErrors('promotionType')} ? 'form-select is-invalid' : 'form-select'">
                                <option value="">Select type</option>
                                <option th:each="type : ${promotionTypes}"
                                        th:value="${type}"
                                        th:text="${type.displayName}">Type</option>
                            </select>
                            <div th:if="${#fields.hasErrors('promotionType')}" class="invalid-feedback" th:errors="*{promotionType}"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="discountValue" class="form-label">Discount Value <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="discountValue" th:field="*{discountValue}"
                                       th:class="${#fields.hasErrors('discountValue')} ? 'form-control is-invalid' : 'form-control'"
                                       step="0.01" min="0.01" placeholder="10">
                                <span class="input-group-text" id="discount-unit">%</span>
                            </div>
                            <div th:if="${#fields.hasErrors('discountValue')}" class="invalid-feedback" th:errors="*{discountValue}"></div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="maxDiscountAmount" class="form-label">Max Discount Amount</label>
                            <input type="number" class="form-control" id="maxDiscountAmount" th:field="*{maxDiscountAmount}"
                                   th:class="${#fields.hasErrors('maxDiscountAmount')} ? 'form-control is-invalid' : 'form-control'"
                                   step="0.01" min="0" placeholder="100000">
                            <div class="form-text">Maximum discount amount for percentage promotions (in VND)</div>
                            <div th:if="${#fields.hasErrors('maxDiscountAmount')}" class="invalid-feedback" th:errors="*{maxDiscountAmount}"></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="minOrderValue" class="form-label">Minimum Order Value</label>
                            <input type="number" class="form-control" id="minOrderValue" th:field="*{minOrderValue}"
                                   th:class="${#fields.hasErrors('minOrderValue')} ? 'form-control is-invalid' : 'form-control'"
                                   step="0.01" min="0" placeholder="50000">
                            <div class="form-text">Minimum order value to use this promotion (in VND)</div>
                            <div th:if="${#fields.hasErrors('minOrderValue')}" class="invalid-feedback" th:errors="*{minOrderValue}"></div>
                        </div>
                    </div>
                </div>
            </div>

                    <!-- Scope Configuration -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-target"></i> Scope Configuration
                        </h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="scopeType" class="form-label">Scope Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="scopeType" th:field="*{scopeType}"
                                    th:class="${#fields.hasErrors('scopeType')} ? 'form-select is-invalid' : 'form-select'">
                                <option value="">Select scope</option>
                                <option th:each="scope : ${scopeTypes}"
                                        th:value="${scope}"
                                        th:text="${scope.displayName}">Scope</option>
                            </select>
                            <div th:if="${#fields.hasErrors('scopeType')}" class="invalid-feedback" th:errors="*{scopeType}"></div>
                        </div>
                    </div>
                </div>

                <!-- Category Selection -->
                <div id="categoryScope" class="scope-selection">
                    <div class="mb-3">
                        <label for="categoryIds" class="form-label">Select Categories</label>
                        <select class="form-select" id="categoryIds" th:field="*{categoryIds}" multiple>
                            <option th:each="category : ${categories}"
                                    th:value="${category.categoryId}"
                                    th:text="${category.categoryName}">Category</option>
                        </select>
                        <div class="form-text">Select one or more categories for this promotion</div>
                    </div>
                </div>

                <!-- Removed shop and product selection for simplified system -->
            </div>

                    <!-- Time Configuration -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-calendar"></i> Time Configuration
                        </h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="startDate" class="form-label">Start Date & Time <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="startDate" th:field="*{startDate}"
                                   th:class="${#fields.hasErrors('startDate')} ? 'form-control is-invalid' : 'form-control'">
                            <div th:if="${#fields.hasErrors('startDate')}" class="invalid-feedback" th:errors="*{startDate}"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="endDate" class="form-label">End Date & Time <span class="text-danger">*</span></label>
                            <input type="datetime-local" class="form-control" id="endDate" th:field="*{endDate}"
                                   th:class="${#fields.hasErrors('endDate')} ? 'form-control is-invalid' : 'form-control'">
                            <div th:if="${#fields.hasErrors('endDate')}" class="invalid-feedback" th:errors="*{endDate}"></div>
                        </div>
                    </div>
                </div>
            </div>

                    <!-- Usage Limits -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-chart-bar"></i> Usage Limits
                        </h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="usageLimitPerUser" class="form-label">Usage Limit Per User</label>
                            <input type="number" class="form-control" id="usageLimitPerUser" th:field="*{usageLimitPerUser}"
                                   th:class="${#fields.hasErrors('usageLimitPerUser')} ? 'form-control is-invalid' : 'form-control'"
                                   min="1" placeholder="1">
                            <div class="form-text">Maximum times a single user can use this promotion</div>
                            <div th:if="${#fields.hasErrors('usageLimitPerUser')}" class="invalid-feedback" th:errors="*{usageLimitPerUser}"></div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="totalUsageLimit" class="form-label">Total Usage Limit</label>
                            <input type="number" class="form-control" id="totalUsageLimit" th:field="*{totalUsageLimit}"
                                   th:class="${#fields.hasErrors('totalUsageLimit')} ? 'form-control is-invalid' : 'form-control'"
                                   min="1" placeholder="100">
                            <div class="form-text">Maximum total uses across all users</div>
                            <div th:if="${#fields.hasErrors('totalUsageLimit')}" class="invalid-feedback" th:errors="*{totalUsageLimit}"></div>
                        </div>
                    </div>
                </div>
            </div>

                    <!-- Status Configuration - Simplified -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-toggle-on"></i> Status Configuration
                        </h5>
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="isActive" th:field="*{isActive}" checked>
                                <label class="form-check-label" for="isActive">
                                    <strong>Active Promotion</strong>
                                </label>
                            </div>
                            <div class="form-text">Promotions are automatically set to active when created. Uncheck to disable.</div>
                            <!-- Hidden field for status - automatically set to ACTIVE -->
                            <input type="hidden" th:field="*{status}" th:value="ACTIVE">
                        </div>
                    </div>
                </div>
            </div>

                    <!-- Form Actions -->
                    <div class="form-section">
                        <div class="d-flex justify-content-between">
                            <a href="/admin/promotions" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    <span th:text="${isEdit ? 'Update Promotion' : 'Create Promotion'}">Create Promotion</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- VND Formatter -->
    <script th:src="@{/js/vnd-formatter.js}"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for category selection only
            $('#categoryIds').select2({
                placeholder: 'Select categories...',
                allowClear: true
            });

            // Handle scope type changes - simplified for only SITE_WIDE and CATEGORY
            $('#scopeType').change(function() {
                const scopeType = $(this).val();
                $('.scope-selection').removeClass('active');

                if (scopeType === 'CATEGORY') {
                    $('#categoryScope').addClass('active');
                }
                // SITE_WIDE doesn't need any specific selection
            });

            // Handle promotion type changes
            $('#promotionType').change(function() {
                const promotionType = $(this).val();
                const discountUnit = $('#discount-unit');

                if (promotionType === 'PERCENTAGE_DISCOUNT') {
                    discountUnit.text('%');
                    $('#discountValue').attr('max', '100');
                    $('#discountValue').attr('step', '0.01');
                    $('#discountValue').attr('placeholder', '10');
                } else {
                    discountUnit.text('VND');
                    $('#discountValue').removeAttr('max');
                    $('#discountValue').attr('step', '0.01');
                    $('#discountValue').attr('placeholder', '50000');
                }
            });

            // Convert code to uppercase
            $('#code').on('input', function() {
                $(this).val($(this).val().toUpperCase());
            });

            // Initialize scope selection based on current value
            const currentScopeType = $('#scopeType').val();
            if (currentScopeType) {
                $('#scopeType').trigger('change');
            }

            // Initialize promotion type unit
            const currentPromotionType = $('#promotionType').val();
            if (currentPromotionType) {
                $('#promotionType').trigger('change');
            }

            // Add client-side validation for promotion fields
            addPromotionValidation();

            // Check edit restrictions for existing promotions
            const promotionId = $('input[name="promotionId"]').val();
            if (promotionId) {
                checkEditRestrictions(promotionId);
            }

            // Handle form submission - convert VND formatted values to plain numbers
            $('form').on('submit', function(e) {
                console.log('Form submission started - converting VND formatted values');

                // Convert VND formatted fields to plain numbers before submission
                $('.vnd-input').each(function() {
                    const $input = $(this);
                    const formattedValue = $input.val();

                    if (formattedValue && formattedValue.trim() !== '') {
                        // Parse the formatted value to get plain number
                        const plainValue = window.VNDFormatter ?
                            VNDFormatter.parse(formattedValue) :
                            parseFloat(formattedValue.replace(/,/g, ''));

                        // Set the plain number value (or empty string if null)
                        $input.val(plainValue !== null ? plainValue : '');

                        console.log('Converted', $input.attr('id'), 'from', formattedValue, 'to', plainValue);
                    } else {
                        // Clear empty fields to ensure they're sent as empty strings
                        $input.val('');
                        console.log('Cleared empty field:', $input.attr('id'));
                    }
                });

                console.log('Form submission conversion completed');
            });

            // Removed product loading function as it's not needed in simplified system
        });

        // Function to check edit restrictions based on promotion usage
        function checkEditRestrictions(promotionId) {
            $.get('/admin/promotions/' + promotionId + '/edit-status')
                .done(function(data) {
                    if (data.hasBeenUsed) {
                        showEditRestrictions(data);
                        applyFieldRestrictions(data.editableFields);
                    }
                })
                .fail(function() {
                    console.error('Failed to get promotion edit status');
                });
        }

        // Show edit restrictions info
        function showEditRestrictions(data) {
            const infoHtml = `
                <div class="edit-restriction-info">
                    <h6><i class="fas fa-exclamation-triangle"></i> Edit Restrictions</h6>
                    <p><strong>This promotion has been used ${data.currentUsageCount} time(s).</strong></p>
                    <p>Only the following fields can be edited:</p>
                    <ul>
                        <li>End Date (to extend promotion)</li>
                        <li>Total Usage Limit (to increase limit)</li>
                        <li>Usage Limit Per User (to increase limit)</li>
                        <li>Active Status (to enable/disable)</li>
                    </ul>
                    <p class="mb-0"><small class="text-muted">Core promotion terms (code, discount value, minimum order value) cannot be changed after use.</small></p>
                </div>
            `;
            $('.card-body').first().prepend(infoHtml);
        }

        // Apply field restrictions
        function applyFieldRestrictions(editableFields) {
            const allFields = ['code', 'discountValue', 'maxDiscountAmount', 'minOrderValue', 'endDate', 'totalUsageLimit', 'usageLimitPerUser', 'isActive'];

            allFields.forEach(function(field) {
                if (!editableFields.includes(field)) {
                    const fieldElement = $('#' + field);
                    fieldElement.prop('disabled', true);
                    fieldElement.addClass('field-disabled');

                    // Add tooltip explaining why field is disabled
                    fieldElement.attr('title', 'This field cannot be edited because the promotion has been used');
                    fieldElement.tooltip();
                }
            });
        }

        // Toggle promotion active status
        function togglePromotionActive(promotionId) {
            $.post('/admin/promotions/' + promotionId + '/toggle-active')
                .done(function(data) {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + (data.error || 'Failed to toggle promotion status'));
                    }
                })
                .fail(function() {
                    alert('Failed to toggle promotion status');
                });
        }

        // Add client-side validation for promotion fields
        function addPromotionValidation() {
            // Validate percentage discount value
            $('#discountValue').on('input blur', function() {
                const promotionType = $('#promotionType').val();
                const value = parseFloat($(this).val());

                if (promotionType === 'PERCENTAGE_DISCOUNT' && value > 100) {
                    $(this).addClass('is-invalid');
                    showFieldError($(this), 'Percentage discount cannot exceed 100%');
                } else {
                    $(this).removeClass('is-invalid');
                    hideFieldError($(this));
                }
            });

            // Validate max discount amount vs min order value for percentage discounts
            $('#maxDiscountAmount, #minOrderValue, #discountValue').on('input blur', function() {
                validateDiscountLogic();
            });

            // Validate fixed amount vs min order value
            $('#discountValue, #minOrderValue').on('input blur', function() {
                const promotionType = $('#promotionType').val();
                if (promotionType === 'FIXED_AMOUNT_DISCOUNT') {
                    validateFixedAmountDiscount();
                }
            });
        }

        function validateDiscountLogic() {
            const promotionType = $('#promotionType').val();
            const discountValue = parseFloat($('#discountValue').val());
            const maxDiscountAmount = parseFloat($('#maxDiscountAmount').val());
            const minOrderValue = parseFloat($('#minOrderValue').val());

            if (promotionType === 'PERCENTAGE_DISCOUNT' &&
                !isNaN(discountValue) && !isNaN(maxDiscountAmount) && !isNaN(minOrderValue)) {

                const discountAtMinOrder = minOrderValue * (discountValue / 100);

                if (maxDiscountAmount < discountAtMinOrder) {
                    $('#maxDiscountAmount').addClass('is-invalid');
                    showFieldError($('#maxDiscountAmount'),
                        'Max discount seems too low compared to minimum order value and discount percentage');
                } else {
                    $('#maxDiscountAmount').removeClass('is-invalid');
                    hideFieldError($('#maxDiscountAmount'));
                }
            }
        }

        function validateFixedAmountDiscount() {
            const discountValue = parseFloat($('#discountValue').val());
            const minOrderValue = parseFloat($('#minOrderValue').val());

            if (!isNaN(discountValue) && !isNaN(minOrderValue) && discountValue >= minOrderValue) {
                $('#discountValue').addClass('is-invalid');
                showFieldError($('#discountValue'),
                    'Fixed discount amount should be less than minimum order value');
            } else {
                $('#discountValue').removeClass('is-invalid');
                hideFieldError($('#discountValue'));
            }
        }

        function showFieldError(field, message) {
            let errorDiv = field.parent().find('.invalid-feedback');
            if (errorDiv.length === 0) {
                errorDiv = $('<div class="invalid-feedback"></div>');
                field.parent().append(errorDiv);
            }
            errorDiv.text(message);
        }

        function hideFieldError(field) {
            field.parent().find('.invalid-feedback').remove();
        }
    </script>

    <div th:replace="~{fragments/footer :: footer}"></div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>
