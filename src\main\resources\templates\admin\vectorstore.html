<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}"/>
    <meta name="_csrf_header" th:content="${_csrf.headerName}"/>
    <title>Vector Store Management - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        .status-card {
            border-left: 4px solid #007bff;
        }
        .status-available {
            border-left-color: #28a745;
        }
        .status-unavailable {
            border-left-color: #dc3545;
        }
        .action-btn {
            margin: 5px;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 bg-dark text-white p-3">
                <h5><i class="fas fa-cogs"></i> Admin Panel</h5>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white active" href="/admin/vectorstore">
                            <i class="fas fa-database"></i> Vector Store
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-database"></i> Vector Store Management</h2>
                    <button class="btn btn-outline-secondary" onclick="refreshStatus()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>

                <!-- Status Card -->
                <div class="card status-card mb-4" id="statusCard">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Vector Store Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Status:</strong> <span id="statusText" class="badge">Loading...</span></p>
                                <p><strong>Documents:</strong> <span id="documentCount">-</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>ChromaDB URL:</strong> <code>http://localhost:8000</code></p>
                                <p><strong>Collection:</strong> <code>readhub_books</code></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary action-btn" onclick="loadBooks()">
                            <i class="fas fa-upload"></i> Load Books to Vector Store
                        </button>
                        <button class="btn btn-warning action-btn" onclick="clearVectorStore()">
                            <i class="fas fa-trash"></i> Clear Vector Store
                        </button>
                        <button class="btn btn-info action-btn" onclick="testSearch()">
                            <i class="fas fa-search"></i> Test Search
                        </button>
                    </div>
                </div>

                <!-- Log Output -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-terminal"></i> Operation Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="logOutput" class="log-output">
                            Ready to perform operations...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Utility functions
        function log(message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logOutput').innerHTML = '';
        }

        // Status functions
        function refreshStatus() {
            log('Checking Vector Store status...');
            
            fetch('/api/admin/vectorstore/status')
                .then(response => response.json())
                .then(data => {
                    updateStatusDisplay(data);
                    log(`Status: ${data.available ? 'Available' : 'Unavailable'}, Documents: ${data.documentCount}`);
                })
                .catch(error => {
                    log(`Error checking status: ${error.message}`);
                    updateStatusDisplay({available: false, documentCount: 0, message: 'Error'});
                });
        }

        function updateStatusDisplay(data) {
            const statusCard = document.getElementById('statusCard');
            const statusText = document.getElementById('statusText');
            const documentCount = document.getElementById('documentCount');

            if (data.available) {
                statusCard.className = 'card status-card status-available mb-4';
                statusText.className = 'badge bg-success';
                statusText.textContent = 'Available';
            } else {
                statusCard.className = 'card status-card status-unavailable mb-4';
                statusText.className = 'badge bg-danger';
                statusText.textContent = 'Unavailable';
            }

            documentCount.textContent = data.documentCount || 0;
        }

        // Get CSRF token
        function getCsrfToken() {
            const token = document.querySelector('meta[name="_csrf"]');
            const header = document.querySelector('meta[name="_csrf_header"]');
            return {
                token: token ? token.getAttribute('content') : '',
                header: header ? header.getAttribute('content') : 'X-CSRF-TOKEN'
            };
        }

        // Action functions
        function loadBooks() {
            log('Starting to load books into Vector Store...');

            const csrf = getCsrfToken();
            const headers = {
                'Content-Type': 'application/json',
            };
            if (csrf.token) {
                headers[csrf.header] = csrf.token;
            }

            fetch('/api/admin/vectorstore/load-books', {
                method: 'POST',
                headers: headers
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    log(`✅ Success: ${data.message}`);
                    log(`📚 Loaded ${data.documentCount} documents`);
                    refreshStatus();
                } else {
                    log(`❌ Error: ${data.message}`);
                }
            })
            .catch(error => {
                log(`❌ Network error: ${error.message}`);
            });
        }

        function clearVectorStore() {
            if (!confirm('Are you sure you want to clear all documents from Vector Store?')) {
                return;
            }

            log('Clearing Vector Store...');

            const csrf = getCsrfToken();
            const headers = {};
            if (csrf.token) {
                headers[csrf.header] = csrf.token;
            }

            fetch('/api/admin/vectorstore/clear', {
                method: 'DELETE',
                headers: headers
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    log(`✅ Success: ${data.message}`);
                    refreshStatus();
                } else {
                    log(`❌ Error: ${data.message}`);
                }
            })
            .catch(error => {
                log(`❌ Network error: ${error.message}`);
            });
        }

        function testSearch() {
            const query = prompt('Enter search query:', 'sách lập trình');
            if (!query) return;

            log(`🔍 Testing search with query: "${query}"`);
            
            // This would need a separate endpoint for testing search
            log('Search test feature will be implemented...');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            log('Vector Store Management page loaded');
            refreshStatus();
        });
    </script>
</body>
</html>
