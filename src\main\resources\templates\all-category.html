<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReadHub - Book Store</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        .search-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem 0;
            border-radius: 15px;
            margin: 0 -15px;
        }

        .search-form .input-group {
            border-radius: 50px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .search-input {
            border: none;
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            background: white;
        }

        .search-input:focus {
            box-shadow: none;
            border: none;
            background: white;
        }

        .search-btn {
            border: none;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #007bff, #0056b3);
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .search-suggestions {
            text-align: center;
        }

        @media (max-width: 768px) {
            .search-section {
                padding: 1.5rem 0;
                margin: 0 -10px;
            }

            .search-input {
                font-size: 1rem;
                padding: 0.8rem 1rem;
            }

            .search-btn {
                padding: 0.8rem 1.5rem;
            }
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main>
    <!-- Shop Page Header & Category Filters -->
    <section class="shop-page-header-section py-5">
        <div class="container">
            <div class="shop-title-area text-center text-md-start">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="shop-page-title">Book Store</h1>
                        <p class="shop-page-subtitle text-muted">Explore our vast collection of books across different categories.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb justify-content-center justify-content-md-end bg-transparent p-0 m-0">
                                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Shop</li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Enhanced Search Bar -->
            <div class="search-section mt-4">
                <div class="row justify-content-center">
                    <div class="col-lg-8 col-md-10">
                        <form id="searchForm" th:action="@{/all-category}" method="get" class="search-form">
                            <!-- Hidden inputs to preserve filters -->
                            <input type="hidden" name="page" value="0">
                            <input type="hidden" name="size" value="12">
                            <input type="hidden" name="sort" th:value="${sortField}">
                            <input type="hidden" name="direction" th:value="${sortDirection}">
                            <input type="hidden" name="category" th:each="catId : ${selectedCategoryIds}" th:value="${catId}">
                            <input type="hidden" name="publisher" th:each="pubId : ${selectedPublisherIds}" th:value="${pubId}">
                            <input type="hidden" name="priceRange" th:each="range : ${selectedPriceRanges}" th:value="${range}">
                            <input type="hidden" name="rating" th:value="${minRating}">

                            <div class="input-group input-group-lg shadow-sm">
                                <input type="text"
                                       name="search"
                                       class="form-control search-input"
                                       placeholder="Search by book title, author, ISBN, or category..."
                                       th:value="${searchQuery}"
                                       autocomplete="off">
                                <button class="btn btn-primary search-btn" type="submit">
                                    <i class="fas fa-search me-2"></i>Search
                                </button>
                            </div>

                            <!-- Search suggestions (optional enhancement) -->
                            <div class="search-suggestions mt-2" style="display: none;">
                                <small class="text-muted">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Try searching for: "Harry Potter", "Stephen King", "978-0-123456-78-9", "Fiction"
                                </small>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Shop Content: Sidebar & Products -->
    <section class="shop-content-section py-5">
        <div class="container">
            <div class="row">
                <!-- Sidebar -->
                <aside class="col-lg-3">
                    <!-- Category filter with checkboxes -->
                    <form id="filterForm" th:action="@{/all-category}" method="get">
                        <!-- Hidden inputs for maintaining pagination and sorting state -->
                        <input type="hidden" name="page" th:value="${currentPage}">
                        <input type="hidden" name="size" value="12">
                        <input type="hidden" name="sort" th:value="${sortField}">
                        <input type="hidden" name="direction" th:value="${sortDirection}">
                        
                        <!-- Apply filters button at the top for better UX -->
                        <button type="submit" class="btn btn-primary w-100 mb-4">Áp Dụng Bộ Lọc</button>
                        
                        <div class="filter-widget mb-4">
                            <h5 class="filter-widget-title">Categories</h5>
                            <div class="category-filter-list">
                                <div class="form-check" th:each="category : ${allCategories}">
                                    <input class="form-check-input filter-checkbox" 
                                           type="checkbox" 
                                           name="category" 
                                           th:value="${category.categoryId}" 
                                           th:id="'category' + ${category.categoryId}"
                                           th:checked="${selectedCategoryIds != null && selectedCategoryIds.contains(category.categoryId)}">
                                    <label class="form-check-label" th:for="'category' + ${category.categoryId}" th:text="${category.categoryName}">Category Name</label>
                                </div>
                            </div>
                        </div>

                        <!-- Publisher filter instead of Author -->
                        <div class="filter-widget mb-4">
                            <h5 class="filter-widget-title">Filter by Publisher</h5>
                            <div class="publisher-filter-list">
                                <div class="form-check" th:each="publisher : ${allPublishers}">
                                    <input class="form-check-input filter-checkbox" 
                                           type="checkbox" 
                                           name="publisher" 
                                           th:value="${publisher.publisherId}" 
                                           th:id="'publisher' + ${publisher.publisherId}"
                                           th:checked="${selectedPublisherIds != null && selectedPublisherIds.contains(publisher.publisherId)}">
                                    <label class="form-check-label" th:for="'publisher' + ${publisher.publisherId}" th:text="${publisher.publisherName}">Publisher Name</label>
                                </div>
                            </div>
                        </div>

                        <!-- Price range filter with checkboxes -->
                        <div class="filter-widget mb-4">
                            <h5 class="filter-widget-title">Filter by Price</h5>
                            <div class="price-filter-list">
                                <div class="form-check">
                                    <input class="form-check-input filter-checkbox" type="checkbox" name="priceRange" value="range1" id="priceRange1"
                                           th:checked="${selectedPriceRanges != null && selectedPriceRanges.contains('range1')}">
                                    <label class="form-check-label" for="priceRange1">Less than 50.000 VND</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input filter-checkbox" type="checkbox" name="priceRange" value="range2" id="priceRange2"
                                           th:checked="${selectedPriceRanges != null && selectedPriceRanges.contains('range2')}">
                                    <label class="form-check-label" for="priceRange2">50.000 VND - 200.000 VND</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input filter-checkbox" type="checkbox" name="priceRange" value="range3" id="priceRange3"
                                           th:checked="${selectedPriceRanges != null && selectedPriceRanges.contains('range3')}">
                                    <label class="form-check-label" for="priceRange3">200.000 VND - 500.000 VND</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input filter-checkbox" type="checkbox" name="priceRange" value="range4" id="priceRange4"
                                           th:checked="${selectedPriceRanges != null && selectedPriceRanges.contains('range4')}">
                                    <label class="form-check-label" for="priceRange4">More than 500.000 VND</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- No Apply Filters button here anymore, moved to top -->
                    </form>
                </aside>

                <!-- Product Grid -->
                <div class="col-lg-9">
                    <div class="product-grid-controls d-flex flex-wrap justify-content-between align-items-center mb-4">
                        <!-- Results count -->
                        <p class="mb-0 text-muted">
                            Showing 
                            <span th:text="${books.numberOfElements > 0 ? books.number * books.size + 1 : 0}"></span>
                            –
                            <span th:text="${books.numberOfElements > 0 ? books.number * books.size + books.numberOfElements : 0}"></span>
                            of 
                            <span th:text="${books.totalElements}"></span> results
                        </p>
                        
                        <div class="d-flex align-items-center">
                            <!-- Rating filter dropdown -->
                            <div class="rating-dropdown me-3">
                                <select class="form-select form-select-sm" id="ratingFilter" name="rating" form="filterForm">
                                    <option value="">Rating</option>
                                    <option value="5" th:selected="${minRating != null && minRating == 5}">5 ★</option>
                                    <option value="4" th:selected="${minRating != null && minRating == 4}">4+ ★</option>
                                    <option value="3" th:selected="${minRating != null && minRating == 3}">3+ ★</option>
                                    <option value="2" th:selected="${minRating != null && minRating == 2}">2+ ★</option>
                                    <option value="1" th:selected="${minRating != null && minRating == 1}">1+ ★</option>
                                </select>
                            </div>
                            
                            <!-- Sort by dropdown and direction controls (separate) -->
                            <div class="d-flex align-items-center">
                                <form id="sortForm" th:action="@{/all-category}" method="get" class="d-inline">
                                    <!-- Preserve all current filter parameters -->
                                    <input type="hidden" name="page" value="0"> <!-- Reset to first page on sort change -->
                                    <input type="hidden" name="size" value="12">
                                    <th:block th:if="${searchQuery != null}">
                                        <input type="hidden" name="search" th:value="${searchQuery}">
                                    </th:block>
                                    <th:block th:if="${selectedCategoryIds != null}" th:each="catId : ${selectedCategoryIds}">
                                        <input type="hidden" name="category" th:value="${catId}">
                                    </th:block>
                                    <th:block th:if="${selectedPublisherIds != null}" th:each="pubId : ${selectedPublisherIds}">
                                        <input type="hidden" name="publisher" th:value="${pubId}">
                                    </th:block>
                                    <th:block th:if="${selectedPriceRanges != null}" th:each="range : ${selectedPriceRanges}">
                                        <input type="hidden" name="priceRange" th:value="${range}">
                                    </th:block>
                                    <th:block th:if="${minRating != null}">
                                        <input type="hidden" name="rating" th:value="${minRating}">
                                    </th:block>
                                    
                                    <!-- Sort options as a dropdown -->
                                    <select class="form-select form-select-sm" name="sort" id="sortSelect" onchange="document.getElementById('sortForm').submit();">
                                        <option value="dateAdded" th:selected="${sortField == 'dateAdded'}">Sắp xếp theo mới nhất</option>
                                        <option value="averageRating" th:selected="${sortField == 'averageRating'}">Sắp xếp theo đánh giá</option>
                                        <option value="sellingPrice" th:selected="${sortField == 'sellingPrice'}">Sắp xếp theo giá</option>
                                        <option value="title" th:selected="${sortField == 'title'}">Sắp xếp theo tên</option>
                                    </select>

                                    <!-- Hidden direction input for form submit -->
                                    <input type="hidden" name="direction" th:value="${sortDirection}" id="sortDirection">
                                </form>
                                
                                <!-- Separate Order Direction controls in a button group -->
                                <div class="btn-group ms-2">
                                    <button class="btn btn-sm btn-outline-primary" 
                                            th:classappend="${sortDirection == 'ASC' ? 'active' : ''}" 
                                            onclick="setSortDirection('ASC')" 
                                            title="Sắp xếp tăng dần">
                                        <i class="fas fa-sort-amount-up-alt"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary" 
                                            th:classappend="${sortDirection == 'DESC' ? 'active' : ''}" 
                                            onclick="setSortDirection('DESC')" 
                                            title="Sắp xếp giảm dần">
                                        <i class="fas fa-sort-amount-down-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-xl-4 g-4">
                        <!-- No books found message -->
                        <div class="col-12 text-center" th:if="${books.empty}">
                            <div class="alert alert-info">
                                <h5>No books found matching your criteria</h5>
                                <p>Try adjusting your filters or search query</p>
                            </div>
                        </div>
                        
                        <!-- Dynamic books from database -->
                        <div class="col" th:each="book : ${books}">
                            <div class="book-card shop-book-card">
                                <div class="position-relative">
                                    <!-- Book cover image with fallback -->
                                    <a th:href="@{/product-detail(book_id=${book.bookId})}">
                                        <img th:src="${book.coverImgUrl != null ? book.coverImgUrl : '/images/book-placeholder.jpg'}" 
                                             th:alt="${book.title}" class="book-cover img-fluid">
                                    </a>
                                    <!-- Sale badge if discount is available -->
                                    <span class="badge bg-danger sale-badge" 
                                          th:if="${book.originalPrice != null && book.sellingPrice != null && book.originalPrice.compareTo(book.sellingPrice) > 0}">Sale!</span>
                                </div>
                                
                                <div class="book-info">
                                    <!-- Book title with link to details -->
                                    <h6 class="book-title">
                                        <a th:href="@{/product-detail(book_id=${book.bookId})}" th:text="${book.title}">Book Title</a>
                                    </h6>
                                    
                                    <!-- Star rating using the fragment -->
                                    <div th:replace="~{fragments/book-rating :: star-rating(${book.averageRating})}"></div>
                                    
                                    <!-- Price display using the fragment -->
                                    <div th:replace="~{fragments/book-price :: price-display(${book.originalPrice}, ${book.sellingPrice})}"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Pagination -->
                    <nav aria-label="Page navigation" class="mt-5 d-flex justify-content-center" th:if="${books.totalPages > 0}">
                        <ul class="pagination">
                            <!-- Previous page link -->
                            <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/all-category(page=${currentPage - 1}, size=12, sort=${sortField}, direction=${sortDirection}, 
                                    search=${searchQuery}, category=${selectedCategoryIds}, publisher=${selectedPublisherIds}, 
                                    priceRange=${selectedPriceRanges}, rating=${minRating})}"
                                   tabindex="-1" th:aria-disabled="${currentPage == 0}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            
                            <!-- Page number links -->
                            <li class="page-item" th:each="i : ${#numbers.sequence(0, books.totalPages - 1)}" 
                                th:classappend="${currentPage == i ? 'active' : ''}" th:if="${i < 5}">
                                <a class="page-link" th:href="@{/all-category(page=${i}, size=12, sort=${sortField}, direction=${sortDirection}, 
                                    search=${searchQuery}, category=${selectedCategoryIds}, publisher=${selectedPublisherIds}, 
                                    priceRange=${selectedPriceRanges}, rating=${minRating})}"
                                   th:text="${i + 1}"></a>
                            </li>
                            
                            <!-- Next page link -->
                            <li class="page-item" th:classappend="${currentPage + 1 >= books.totalPages ? 'disabled' : ''}">
                                <a class="page-link" th:href="@{/all-category(page=${currentPage + 1}, size=12, sort=${sortField}, direction=${sortDirection}, 
                                    search=${searchQuery}, category=${selectedCategoryIds}, publisher=${selectedPublisherIds}, 
                                    priceRange=${selectedPriceRanges}, rating=${minRating})}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </section>

</main>


<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
        crossorigin="anonymous"></script>
<!-- Custom JS -->
<script th:src="@{/js/script.js}"></script>

<!-- Page-specific JavaScript -->
<script>
    // Function to set sort direction explicitly
    function setSortDirection(direction) {
        // Get the direction input
        const directionInput = document.getElementById('sortDirection');
        // Set the direction value
        directionInput.value = direction;
        // Submit the form
        document.getElementById('sortForm').submit();
    }
    
    // Auto-submit form when rating dropdown changes
    document.addEventListener('DOMContentLoaded', function() {
        const ratingFilter = document.getElementById('ratingFilter');
        if (ratingFilter) {
            ratingFilter.addEventListener('change', function() {
                document.getElementById('filterForm').submit();
            });
        }

        // Enhanced search functionality
        const searchInput = document.querySelector('.search-input');
        const searchForm = document.getElementById('searchForm');

        if (searchInput && searchForm) {
            // Add search suggestions on focus
            searchInput.addEventListener('focus', function() {
                const suggestions = document.querySelector('.search-suggestions');
                if (suggestions) {
                    suggestions.style.display = 'block';
                }
            });

            // Hide suggestions on blur (with delay to allow clicking)
            searchInput.addEventListener('blur', function() {
                setTimeout(() => {
                    const suggestions = document.querySelector('.search-suggestions');
                    if (suggestions) {
                        suggestions.style.display = 'none';
                    }
                }, 200);
            });

            // Submit form on Enter key
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchForm.submit();
                }
            });
        }
    });
</script>
</body>
</html>