<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Management - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Page-specific styles for inventory management */
        .content-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .stock-status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .stock-in {
            background-color: #d4edda;
            color: #155724;
        }

        .stock-low {
            background-color: #fff3cd;
            color: #856404;
        }

        .stock-out {
            background-color: #f8d7da;
            color: #721c24;
        }

        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            color: #2C3E50;
        }

        .table img {
            width: 50px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }

        .filter-buttons .btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .search-container {
            position: relative;
        }

        .search-container .form-control {
            padding-right: 2.5rem;
        }

        .search-container .search-icon {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
    </style>
</head>
<body>

<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="content-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="section-title mb-0">Inventory Management</h2>
                    <div>
                        <button class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
                            <i class="fas fa-edit"></i> Bulk Update
                        </button>
                        <a th:href="@{/seller/products/add}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Product
                        </a>
                    </div>
                </div>

                <!-- Inventory Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h5>Total Products</h5>
                            <h3 th:text="${inventoryStats.totalProducts}">0</h3>
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h5>In Stock</h5>
                            <h3 th:text="${inventoryStats.inStockCount}">0</h3>
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h5>Low Stock</h5>
                            <h3 th:text="${inventoryStats.lowStockCount}">0</h3>
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h5>Out of Stock</h5>
                            <h3 th:text="${inventoryStats.outOfStockCount}">0</h3>
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="content-container">
                <form th:action="@{/seller/inventory}" method="get" class="mb-4">
                    <div class="row align-items-end">
                        <div class="col-md-6">
                            <label for="searchQuery" class="form-label">Search Products</label>
                            <div class="search-container">
                                <input type="text" class="form-control" id="searchQuery" name="search" 
                                       th:value="${param.search}" placeholder="Search by title, ISBN, or SKU...">
                                <i class="fas fa-search search-icon"></i>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label for="stockFilter" class="form-label">Stock Status</label>
                            <select class="form-select" id="stockFilter" name="stockFilter">
                                <option value="all" th:selected="${param.stockFilter == null or param.stockFilter == 'all'}">All Products</option>
                                <option value="in_stock" th:selected="${param.stockFilter == 'in_stock'}">In Stock</option>
                                <option value="low_stock" th:selected="${param.stockFilter == 'low_stock'}">Low Stock</option>
                                <option value="out_of_stock" th:selected="${param.stockFilter == 'out_of_stock'}">Out of Stock</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Inventory Table -->
            <div class="content-container">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Product</th>
                                <th>SKU</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Price</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="item : ${inventoryPage.content}">
                                <td>
                                    <img th:if="${item.coverImgUrl}" th:src="${item.coverImgUrl}" th:alt="${item.title}">
                                    <img th:unless="${item.coverImgUrl}" src="/img/book-placeholder.jpg" alt="No image">
                                </td>
                                <td>
                                    <div>
                                        <strong th:text="${item.title}">Book Title</strong>
                                        <br>
                                        <small class="text-muted" th:text="${item.authors}">Author</small>
                                        <br>
                                        <small class="text-muted">ISBN: <span th:text="${item.isbn}">123456789</span></small>
                                    </div>
                                </td>
                                <td th:text="${item.sku}">SKU123</td>
                                <td>
                                    <span class="fw-bold" th:text="${item.stockQuantity}">10</span>
                                </td>
                                <td>
                                    <span th:if="${item.stockStatus == 'IN_STOCK'}" class="stock-status-badge stock-in">In Stock</span>
                                    <span th:if="${item.stockStatus == 'LOW_STOCK'}" class="stock-status-badge stock-low">Low Stock</span>
                                    <span th:if="${item.stockStatus == 'OUT_OF_STOCK'}" class="stock-status-badge stock-out">Out of Stock</span>
                                </td>
                                <td>
                                    <div th:text="${#numbers.formatDecimal(item.sellingPrice, 0, 'COMMA', 0, 'POINT') + ' đ'}">100,000 đ</div>
                                    <small class="text-muted" th:if="${item.originalPrice != item.sellingPrice}">
                                        <del th:text="${#numbers.formatDecimal(item.originalPrice, 0, 'COMMA', 0, 'POINT') + ' đ'}">120,000 đ</del>
                                    </small>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary me-1" 
                                            onclick="openUpdateStockModal(this)"
                                            th:data-book-id="${item.bookId}"
                                            th:data-book-title="${item.title}"
                                            th:data-current-stock="${item.stockQuantity}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a th:href="@{/seller/products/{id}/edit(id=${item.bookId})}" 
                                       class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-cog"></i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <nav th:if="${inventoryPage.totalPages > 1}" aria-label="Inventory pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item" th:classappend="${inventoryPage.first} ? 'disabled'">
                            <a class="page-link" th:href="@{/seller/inventory(page=${inventoryPage.number - 1}, search=${param.search}, stockFilter=${param.stockFilter})}">Previous</a>
                        </li>
                        <li th:each="pageNum : ${#numbers.sequence(0, inventoryPage.totalPages - 1)}" 
                            class="page-item" th:classappend="${pageNum == inventoryPage.number} ? 'active'">
                            <a class="page-link" th:href="@{/seller/inventory(page=${pageNum}, search=${param.search}, stockFilter=${param.stockFilter})}" 
                               th:text="${pageNum + 1}">1</a>
                        </li>
                        <li class="page-item" th:classappend="${inventoryPage.last} ? 'disabled'">
                            <a class="page-link" th:href="@{/seller/inventory(page=${inventoryPage.number + 1}, search=${param.search}, stockFilter=${param.stockFilter})}">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Update Stock Modal -->
<div class="modal fade" id="updateStockModal" tabindex="-1" aria-labelledby="updateStockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStockModalLabel">Update Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="updateStockForm">
                    <input type="hidden" id="updateBookId" name="bookId">
                    <div class="mb-3">
                        <label for="updateBookTitle" class="form-label">Product</label>
                        <input type="text" class="form-control" id="updateBookTitle" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="updateCurrentStock" class="form-label">Current Stock</label>
                        <input type="text" class="form-control" id="updateCurrentStock" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="updateNewStock" class="form-label">New Stock Quantity</label>
                        <input type="number" class="form-control" id="updateNewStock" name="stockQuantity" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="updateReason" class="form-label">Reason</label>
                        <select class="form-select" id="updateReason" name="reason">
                            <option value="">Select reason...</option>
                            <option value="RESTOCK">Restock</option>
                            <option value="SOLD">Sold</option>
                            <option value="DAMAGED">Damaged</option>
                            <option value="RETURNED">Returned</option>
                            <option value="ADJUSTMENT">Inventory Adjustment</option>
                            <option value="OTHER">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="updateNotes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="updateNotes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateStock()">Update Stock</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Update Modal -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1" aria-labelledby="bulkUpdateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkUpdateModalLabel">Bulk Update Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Select products and update their stock quantities in bulk. You can set a global reason and notes for all updates.
                </div>
                <form id="bulkUpdateForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="bulkGlobalReason" class="form-label">Global Reason</label>
                            <select class="form-select" id="bulkGlobalReason" name="globalReason">
                                <option value="">Select reason...</option>
                                <option value="RESTOCK">Restock</option>
                                <option value="ADJUSTMENT">Inventory Adjustment</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="bulkGlobalNotes" class="form-label">Global Notes</label>
                            <input type="text" class="form-control" id="bulkGlobalNotes" name="globalNotes">
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Select</th>
                                    <th>Product</th>
                                    <th>Current Stock</th>
                                    <th>New Stock</th>
                                </tr>
                            </thead>
                            <tbody id="bulkUpdateTableBody">
                                <tr th:each="item : ${inventoryPage.content}">
                                    <td>
                                        <input type="checkbox" class="form-check-input bulk-select"
                                               th:data-book-id="${item.bookId}"
                                               th:data-book-title="${item.title}"
                                               th:data-current-stock="${item.stockQuantity}">
                                    </td>
                                    <td>
                                        <small th:text="${item.title}">Product Name</small>
                                    </td>
                                    <td th:text="${item.stockQuantity}">10</td>
                                    <td>
                                        <input type="number" class="form-control form-control-sm new-stock-input"
                                               min="0" disabled th:data-book-id="${item.bookId}">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="bulkUpdateStock()">Update Selected</button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

<script>
function openUpdateStockModal(button) {
    const bookId = button.getAttribute('data-book-id');
    const bookTitle = button.getAttribute('data-book-title');
    const currentStock = button.getAttribute('data-current-stock');

    document.getElementById('updateBookId').value = bookId;
    document.getElementById('updateBookTitle').value = bookTitle;
    document.getElementById('updateCurrentStock').value = currentStock;
    document.getElementById('updateNewStock').value = currentStock;

    new bootstrap.Modal(document.getElementById('updateStockModal')).show();
}

function updateStock() {
    const form = document.getElementById('updateStockForm');
    const formData = new FormData(form);

    fetch('/seller/inventory/update-stock', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Stock updated successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating stock.');
    });
}

// Bulk update functionality
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.bulk-select');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const bookId = this.getAttribute('data-book-id');
            const input = document.querySelector(`input.new-stock-input[data-book-id="${bookId}"]`);
            input.disabled = !this.checked;
            if (this.checked) {
                input.value = this.getAttribute('data-current-stock');
            } else {
                input.value = '';
            }
        });
    });
});

function bulkUpdateStock() {
    const selectedItems = [];
    const checkboxes = document.querySelectorAll('.bulk-select:checked');

    checkboxes.forEach(checkbox => {
        const bookId = checkbox.getAttribute('data-book-id');
        const input = document.querySelector(`input.new-stock-input[data-book-id="${bookId}"]`);
        const stockQuantity = parseInt(input.value);

        if (!isNaN(stockQuantity)) {
            selectedItems.push({
                bookId: parseInt(bookId),
                stockQuantity: stockQuantity
            });
        }
    });

    if (selectedItems.length === 0) {
        alert('Please select at least one product and enter valid stock quantities.');
        return;
    }

    const bulkData = {
        inventoryUpdates: selectedItems,
        globalReason: document.getElementById('bulkGlobalReason').value,
        globalNotes: document.getElementById('bulkGlobalNotes').value
    };

    fetch('/seller/inventory/bulk-update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(bulkData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Successfully updated ${data.successCount} out of ${data.totalCount} products!`);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating stocks.');
    });
}
</script>

</body>
</html>
