<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Management - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .table th {
            font-weight: 600;
            color: #2C3E50;
        }
        .filter-section {
            background-color: #fff;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .table-responsive {
            background-color: #fff;
            border-radius: 8px;
            padding: 1rem;
        }
        .pagination {
            margin-bottom: 0;
        }
        .truncate-text {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
        }
        .order-status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .status-processing {
            background-color: #b3e5fc;
            color: #01579b;
        }
        .status-shipped {
            background-color: #c8e6c9;
            color: #2e7d32;
        }
        .status-delivered {
            background-color: #a5d6a7;
            color: #1b5e20;
        }
        .status-cancelled {
            background-color: #ffcdd2;
            color: #b71c1c;
        }
    </style>
</head>
<body>
    <!-- Include Topbar -->
    <div th:replace="fragments/admin-topbar :: admin-topbar"></div>

    <div class="container my-4">
        <div class="row">
            <!-- Include Sidebar -->
            <div class="col-lg-3 mb-4">
                <div th:replace="fragments/admin-sidebar :: admin-sidebar(activeMenu='orders')"></div>
            </div>
            
            <div class="col-lg-9">
                <!-- Success and Error Messages -->
                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title">Order Management</h3>
                    <span class="badge bg-primary rounded-pill fs-6" th:text="${totalItems + ' Orders'}">0 Orders</span>
                </div>

                <!-- Search and Filter Section -->
                <div class="filter-section mb-4">
                    <form th:action="@{/admin/orders}" method="get" class="row g-3">
                        <!-- Search Field -->
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search by order ID or customer name..." 
                                       name="search" th:value="${search}" aria-label="Search">
                                <button class="btn btn-outline-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Status Filter -->
                        <div class="col-md-6">
                            <select class="form-select" name="status" onchange="this.form.submit()">
                                <option value="all" th:selected="${status == null || status == 'all'}">All Statuses</option>
                                <option th:each="orderStatus : ${orderStatusList}" 
                                        th:value="${orderStatus}" 
                                        th:text="${orderStatus}"
                                        th:selected="${status == orderStatus.name()}">
                                </option>
                            </select>
                        </div>
                        
                        <!-- Date Range Filter -->
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">From</span>
                                <input type="date" class="form-control" name="startDate" th:value="${startDate}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text">To</span>
                                <input type="date" class="form-control" name="endDate" th:value="${endDate}">
                                <button type="submit" class="btn btn-primary">Apply Filters</button>
                            </div>
                        </div>
                        
                        <!-- Sorting Hidden Fields -->
                        <input type="hidden" name="sort" th:value="${sort}">
                        <input type="hidden" name="direction" th:value="${direction}">
                        <input type="hidden" name="page" value="0">
                    </form>
                </div>

                <!-- Orders Table -->
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead>
                            <tr>
                                <th>
                                    <a th:href="@{/admin/orders(search=${search},status=${status},startDate=${startDate},endDate=${endDate},sort='orderId',direction=${direction == 'asc' ? 'desc' : 'asc'})}" class="text-decoration-none text-dark">
                                        Order ID 
                                        <i th:if="${sort == 'orderId'}" th:class="${direction == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                        <i th:unless="${sort == 'orderId'}" class="fas fa-sort text-muted"></i>
                                    </a>
                                </th>
                                <th>
                                    <a th:href="@{/admin/orders(search=${search},status=${status},startDate=${startDate},endDate=${endDate},sort='recipientName',direction=${direction == 'asc' ? 'desc' : 'asc'})}" class="text-decoration-none text-dark">
                                        Customer 
                                        <i th:if="${sort == 'recipientName'}" th:class="${direction == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                        <i th:unless="${sort == 'recipientName'}" class="fas fa-sort text-muted"></i>
                                    </a>
                                </th>
                                <th>
                                    <a th:href="@{/admin/orders(search=${search},status=${status},startDate=${startDate},endDate=${endDate},sort='orderDate',direction=${direction == 'asc' ? 'desc' : 'asc'})}" class="text-decoration-none text-dark">
                                        Date 
                                        <i th:if="${sort == 'orderDate'}" th:class="${direction == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                        <i th:unless="${sort == 'orderDate'}" class="fas fa-sort text-muted"></i>
                                    </a>
                                </th>
                                <th>
                                    <a th:href="@{/admin/orders(search=${search},status=${status},startDate=${startDate},endDate=${endDate},sort='totalAmount',direction=${direction == 'asc' ? 'desc' : 'asc'})}" class="text-decoration-none text-dark">
                                        Total 
                                        <i th:if="${sort == 'totalAmount'}" th:class="${direction == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                        <i th:unless="${sort == 'totalAmount'}" class="fas fa-sort text-muted"></i>
                                    </a>
                                </th>
                                <th>Status</th>
                                <th>Items</th>
                                <th width="100">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:if="${orders.empty}">
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-info-circle me-2"></i>No orders found
                                    </div>
                                </td>
                            </tr>
                            <tr th:each="order : ${orders}">
                                <td th:text="${'#' + order.orderId}">Order ID</td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span th:text="${order.customerOrder != null ? order.customerOrder.recipientName : 'N/A'}">Customer Name</span>
                                        <small class="text-muted" th:text="${order.customerOrder != null ? order.customerOrder.recipientPhone : 'N/A'}">Phone</small>
                                    </div>
                                </td>
                                <td th:text="${#temporals.format(order.orderDate, 'dd/MM/yyyy HH:mm')}">Order Date</td>
                                <td th:text="${#numbers.formatDecimal(order.totalAmount, 0, 'COMMA', 0, 'POINT')} + ' VND'">Total VND</td>
                                <td>
                                    <span th:class="'order-status status-' + ${#strings.toLowerCase(order.orderStatus)}" th:text="${order.orderStatus}">Status</span>
                                </td>
                                <td th:text="${order.orderItems.size()}">Items</td>
                                <td>
                                    <a th:href="@{/admin/orders/{id}(id=${order.orderId})}" class="btn btn-sm btn-outline-primary me-1" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-end mt-3">
                        <nav th:if="${totalPages > 1}" aria-label="Orders pagination">
                            <ul class="pagination pagination-sm">
                                <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/admin/orders(page=${currentPage - 1}, size=10, search=${search}, status=${status}, sort=${sort}, direction=${direction}, startDate=${startDate}, endDate=${endDate})}">&laquo;</a>
                                </li>
                                <li class="page-item" th:each="pageNum : ${#numbers.sequence(0, totalPages - 1)}"
                                    th:classappend="${pageNum == currentPage ? 'active' : ''}">
                                    <a class="page-link" th:href="@{/admin/orders(page=${pageNum}, size=10, search=${search}, status=${status}, sort=${sort}, direction=${direction}, startDate=${startDate}, endDate=${endDate})}"
                                       th:text="${pageNum + 1}">1</a>
                                </li>
                                <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/admin/orders(page=${currentPage + 1}, size=10, search=${search}, status=${status}, sort=${sort}, direction=${direction}, startDate=${startDate}, endDate=${endDate})}">&raquo;</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div th:replace="~{fragments/footer :: footer}"></div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html> 