/**
 * Discount Preview System Styles
 * Modern, responsive design for promotion code preview functionality
 */

/* Main promotion section */
.promotion-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8ecef;
}

/* Promotion input styling */
.promotion-input {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.promotion-input input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e8ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.promotion-input input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.promotion-input input.is-valid {
    border-color: #28a745;
}

.promotion-input input.is-invalid {
    border-color: #dc3545;
}

.btn-apply {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-apply:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-apply:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-apply.applied {
    background: #28a745;
}

/* Loading indicator */
.promo-loading {
    display: none;
    text-align: center;
    padding: 10px;
    color: #6c757d;
}

.promo-loading .spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error messages */
.promo-error {
    display: none;
    padding: 12px 16px;
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    font-size: 14px;
    margin-top: 8px;
}

/* Success messages */
.promo-success {
    display: none;
    padding: 12px 16px;
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 6px;
    font-size: 14px;
    margin-top: 8px;
}

/* Discount preview container */
.discount-preview {
    display: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    margin-top: 16px;
    animation: slideIn 0.3s ease-out;
}

/* Simplified view for cart page */
.discount-preview-content.simplified {
    padding: 0;
}

.discount-preview-content.simplified .preview-header {
    margin-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 12px;
}

.simple-summary {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

.simple-summary .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
}

.checkout-note {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.checkout-note small {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.discount-preview.fade-in {
    animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Preview header */
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.savings-badge {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.savings-amount {
    font-size: 18px;
    font-weight: 700;
}

.promotion-info {
    text-align: right;
}

.promo-name {
    display: block;
    font-weight: 600;
    font-size: 16px;
}

.promo-code {
    font-size: 12px;
    opacity: 0.8;
}

/* Order breakdown */
.order-breakdown {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.order-item {
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.order-item:last-child {
    border-bottom: none;
}

.order-item.ineligible {
    opacity: 0.6;
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.shop-name {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.savings-badge {
    background: #4CAF50;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.ineligible-badge {
    background: #ff6b6b;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Price breakdown */
.price-breakdown {
    margin-left: 24px;
}

.price-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
}

.original-price {
    text-decoration: line-through;
    opacity: 0.7;
    font-size: 14px;
}

.arrow {
    font-weight: bold;
    color: #4CAF50;
}

.discounted-price {
    font-weight: 600;
    font-size: 16px;
    color: #4CAF50;
}

.discount-info {
    font-size: 12px;
    opacity: 0.8;
}

.discount-percentage {
    background: rgba(76, 175, 80, 0.2);
    padding: 2px 8px;
    border-radius: 4px;
}

.ineligible-reason {
    font-size: 12px;
    opacity: 0.7;
    font-style: italic;
    margin-left: 24px;
}

/* Preview footer */
.preview-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 16px;
}

.total-summary {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 16px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.summary-row:last-child {
    margin-bottom: 0;
}

.discount-row {
    color: #4CAF50;
    font-weight: 600;
}

.final-row {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 8px;
    font-weight: 700;
    font-size: 18px;
}

.original-total {
    text-decoration: line-through;
    opacity: 0.7;
}

.discount-amount {
    color: #4CAF50;
    font-weight: 600;
}

.final-total {
    color: #4CAF50;
    font-weight: 700;
}

/* Remove button */
.btn-remove-promo {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 12px;
}

.btn-remove-promo:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .promotion-input {
        flex-direction: column;
    }
    
    .preview-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .promotion-info {
        text-align: center;
    }
    
    .order-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
    
    .price-row {
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
    }
    
    .arrow {
        display: none;
    }
}

/* Success animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.success-pulse {
    animation: pulse 0.6s ease-in-out;
}
