<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seller Dashboard - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        
        /* Additional styles for new components */
        .notification-card {
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        .notification-card:hover {
            transform: translateY(-3px);
        }
        .notification-card.warning {
            border-color: #f39c12;
        }
        .notification-card.danger {
            border-color: #e74c3c;
        }
        .notification-card.info {
            border-color: #3498db;
        }
        .notification-card.success {
            border-color: #2ecc71;
        }
        
        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .activity-item:hover {
            background-color: #f8f9fa;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .activity-icon.order {
            background-color: rgba(44, 62, 80, 0.1);
            color: #2C3E50;
        }
        .activity-icon.review {
            background-color: rgba(241, 196, 15, 0.1);
            color: #f39c12;
        }
        .activity-icon.message {
            background-color: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }
        .activity-icon.stock {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }
        
        .chart-container {
            position: relative;
            height: 220px;
            margin-bottom: 1rem;
        }
        
        .quick-action-btn {
            width: 100%;
            margin-bottom: 0.75rem;
            text-align: left;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .quick-action-btn i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }
        
        .low-stock-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .low-stock-item:hover {
            background-color: #f8f9fa;
        }
        .low-stock-item:last-child {
            border-bottom: none;
        }
        .stock-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .badge-danger {
            background-color: #e74c3c;
            color: white;
        }
        .badge-warning {
            background-color: #f39c12;
            color: white;
        }
        
        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        .overview-header h3 {
            margin-bottom: 0;
        }
        .period-badge {
            background-color: #ecf0f1;
            color: #2C3E50;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
<!-- Include Topbar -->
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
    <div class="row">
        <!-- Include Sidebar -->
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">Dashboard</h2>
                <span class="text-muted" th:text="${#dates.format(#dates.createNow(), 'EEEE, dd MMMM yyyy')}">Thursday, 11 July 2023</span>
            </div>

            <!-- Success message -->
            <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- Error message -->
            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- Quick Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card position-relative">
                        <h4>New Orders</h4>
                        <p th:text="${newOrdersCount}">5</p>
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card position-relative">
                        <h4>Today's Revenue</h4>
                        <p th:text="${#numbers.formatDecimal(todayRevenue, 0, 'COMMA', 0, 'POINT')} + ' đ'">1,200,000 đ</p>
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card position-relative">
                        <h4>Active Products</h4>
                        <p th:text="${activeProductsCount}">42</p>
                        <i class="fas fa-book"></i>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card position-relative">
                        <h4>Avg Order Value</h4>
                        <p th:text="${#numbers.formatDecimal(averageOrderValue, 0, 'COMMA', 0, 'POINT')} + ' đ'">3 đ</p>
                        <i class="fas fa-calculator"></i>
                    </div>
                </div>
            </div>

            <!-- Action Required Notifications -->
            <div class="account-container mb-4">
                <div class="overview-header">
                    <h3 class="section-title mb-0">Action Required</h3>
                    <span class="period-badge">Today</span>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="notification-card warning p-3 mb-3 bg-light">
                            <h5><i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Alert</h5>
                            <p class="mb-0" th:if="${lowStockProducts != null && !lowStockProducts.isEmpty()}"
                               th:text="${lowStockProducts.size()} + ' products are running low on inventory'">
                                3 products are running low on inventory
                            </p>
                            <p class="mb-0" th:unless="${lowStockProducts != null && !lowStockProducts.isEmpty()}">
                                All products have sufficient stock
                            </p>
                            <a th:if="${lowStockProducts != null && !lowStockProducts.isEmpty()}"
                               href="#lowStockSection" class="btn btn-sm btn-outline-primary mt-2">View Products</a>
                        </div>
                    </div>

<!--                    <div class="col-md-6">-->
<!--                        <div class="notification-card info p-3 mb-3 bg-light">-->
<!--                            <h5><i class="fas fa-comment text-info"></i> Average Order Value </h5>-->
<!--                            <p class="mb-0">3 customer messages need replies</p>-->
<!--                            <a th:href="@{/seller/messages}" class="btn btn-sm btn-outline-primary mt-2">Reply Now</a>-->
<!--                        </div>-->
<!--                    </div>-->


                </div>
<!--                <div class="row">-->
<!--                    <div class="col-md-6">-->
<!--                        <div class="notification-card danger p-3 mb-3 bg-light">-->
<!--                            <h5><i class="fas fa-bell text-danger"></i> Cancelled Orders</h5>-->
<!--                            <p class="mb-0">2 new cancellations need attention</p>-->
<!--                            <a th:href="@{/seller/orders/cancellations}" class="btn btn-sm btn-outline-primary mt-2">View-->
<!--                                Cancellations</a>-->
<!--                        </div>-->
<!--                    </div>-->

<!--                    <div class="col-md-6">-->
<!--                        <div class="notification-card success p-3 mb-3 bg-light">-->
<!--                            <h5><i class="fas fa-truck text-success"></i> Orders to Ship</h5>-->
<!--                            <p class="mb-0">4 orders ready for shipping</p>-->
<!--                            <a th:href="@{/seller/orders/new}" class="btn btn-sm btn-outline-primary mt-2">Process-->
<!--                                Orders</a>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
            </div>

            <!-- Weekly Performance & Quick Actions -->
            <div class="row mb-4">
                <!-- Weekly Performance -->
                <div class="col-md-12">
                    <div class="account-container h-100">
                        <div class="overview-header">
                            <h3 class="section-title mb-0">Weekly Performance</h3>
                            <a th:href="@{/seller/analytics}" class="btn btn-sm btn-outline-primary">View Full
                                Analytics</a>
                        </div>
                        <div class="chart-container">
                            <canvas id="revenueChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="account-container h-70">
                        <h3 class="section-title">Quick Actions</h3>
                        <div class="container-fluid">
                            <div class="d-flex flex-wrap justify-content-between">
                                <a th:href="@{/seller/products/add}" class="btn btn-outline-primary quick-action-btn"
                                   style="width: auto;">
                                    <i class="fas fa-plus"></i> Add New Product
                                </a>
                                <a th:href="@{/seller/orders}" class="btn btn-outline-primary quick-action-btn"
                                   style="width: auto; ">
                                    <i class="fas fa-box"></i> Process Orders
                                </a>
                                <a th:href="@{/seller/analytics}" class="btn btn-outline-primary quick-action-btn"
                                   style="width: auto;">
                                    <i class="fas fa-chart-bar"></i> Analytics Report
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Low Stock Products & Recent Activity -->
            <div class="row">
                <!-- Low Stock Products -->
                <div class="col-md-6">
                    <div class="account-container" id="lowStockSection">
                        <h3 class="section-title">Low Stock Products</h3>
                        <div th:if="${lowStockProducts == null || lowStockProducts.isEmpty()}" class="text-center py-4">
                            <i class="fas fa-check-circle text-success mb-3" style="font-size: 3rem;"></i>
                            <p class="mb-0">All products have sufficient stock</p>
                        </div>
                        <div th:unless="${lowStockProducts == null || lowStockProducts.isEmpty()}">
                            <div th:each="book : ${lowStockProducts}" class="low-stock-item d-flex align-items-center">
                                <div class="me-3" style="width: 50px; height: 50px;">
                                    <img th:if="${book.coverImgUrl != null}" th:src="${book.coverImgUrl}"
                                         class="img-fluid" alt="Book Cover">
                                    <div th:unless="${book.coverImgUrl != null}"
                                         class="bg-light d-flex align-items-center justify-content-center h-100 w-100">
                                        <i class="fas fa-book text-muted"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1" th:text="${book.title}">Book Title</h6>
                                    <p class="mb-0 small text-muted" th:text="'SKU: ' + ${book.sku}">SKU: B12345</p>
                                </div>
                                <div class="ms-3 text-end">
                                    <span th:if="${book.stockQuantity == 0}" class="badge bg-danger stock-badge">Out of Stock</span>
                                    <span th:unless="${book.stockQuantity == 0}" class="badge bg-warning stock-badge"
                                          th:text="${book.stockQuantity} + ' left'">3 left</span>
                                    <a th:href="@{'/seller/products/' + ${book.bookId} + '/edit'}"
                                       class="d-block small mt-1">Update</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="col-md-6">
                    <div class="account-container">
                        <h3 class="section-title">Recent Activity</h3>
                        <div th:if="${recentOrders == null || recentOrders.isEmpty()}" class="text-center py-4">
                            <i class="fas fa-calendar-alt text-muted mb-3" style="font-size: 3rem;"></i>
                            <p class="mb-0">No recent activity</p>
                        </div>
                        <div th:unless="${recentOrders == null || recentOrders.isEmpty()}">
                            <div th:each="order : ${recentOrders}" class="activity-item d-flex align-items-start">
                                <div class="activity-icon order">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">New Order #<span th:text="${order.order_id}">12345</span></h6>
                                    <p class="small text-muted mb-0">
                                        <span th:text="${#numbers.formatDecimal(order.total_amount, 0, 'COMMA', 0, 'POINT')} + ' VND'">120,000 VND</span>
                                        -
                                        <span th:text="${order.customer_name}">John Doe</span> -
                                        <span th:text="${#dates.format(order.order_date, 'dd MMM, HH:mm')}">10 minutes ago</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div th:if="${recentOrders == null || recentOrders.size() < 4}">
                            <div class="activity-item d-flex align-items-start">
                                <div class="activity-icon review">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">New 5-star Review</h6>
                                    <p class="small text-muted mb-0">Review on "The Art of Storytelling" - 1 hour
                                        ago</p>
                                </div>
                            </div>
                            <div class="activity-item d-flex align-items-start">
                                <div class="activity-icon message">
                                    <i class="fas fa-comment"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">New Customer Message</h6>
                                    <p class="small text-muted mb-0">Inquiry about shipping options - 2 hours ago</p>
                                </div>
                            </div>
                            <div class="activity-item d-flex align-items-start">
                                <div class="activity-icon stock">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">Low Stock Alert</h6>
                                    <p class="small text-muted mb-0">"Data Structures & Algorithms" is running low - 3
                                        hours ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

<!-- Chart Initialization -->
<script th:inline="javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Get data from Thymeleaf
        let weeklyRevenueData = /*[[${weeklyRevenueJson}]]*/ [];

        // Ensure we have an array
        if (typeof weeklyRevenueData === 'string') {
            try {
                weeklyRevenueData = JSON.parse(weeklyRevenueData);
            } catch (e) {
                console.error("Error parsing weekly revenue data:", e);
                weeklyRevenueData = [];
            }
        }

        // Generate date labels for the last 7 days
        const dateLabels = [];
        for (let i = 6; i >= 0; i--) {
            const date = moment().subtract(i, 'days');
            dateLabels.push(date.format('DD/MM'));
        }

        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart');
        if (revenueCtx) {
            const chart = new Chart(revenueCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: dateLabels,
                    datasets: [{
                        label: 'Weekly Revenue',
                        data: weeklyRevenueData,
                        borderColor: '#2C3E50',
                        backgroundColor: 'rgba(44, 62, 80, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return new Intl.NumberFormat('vi-VN').format(context.parsed.y) + ' đ';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('vi-VN', {
                                        style: 'decimal',
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    }
                }
            });
        }
    });
</script>



</body>
</html>