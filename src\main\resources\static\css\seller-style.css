/* seller-style.css - Additional seller dashboard styles */

/* These styles are specifically for seller management interface */

/* Dashboard section */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.stat-info h3 {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-info p {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0;
    color: #2C3E50;
}

/* Product cards */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.product-card {
    border-radius: 8px;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.product-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.product-image-container {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.product-info {
    padding: 1rem;
}

.product-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-title a {
    color: #2C3E50;
    text-decoration: none;
}

.product-title a:hover {
    color: #1e2b37;
}

.product-meta {
    margin-bottom: 0.5rem;
    color: #6c757d;
    font-size: 0.875rem;
}

.product-price {
    color: #2C3E50;
    font-weight: 700;
}

.product-stock {
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
}

.stock-in {
    background-color: rgba(39, 174, 96, 0.1);
    color: #27ae60;
}

.stock-low {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.stock-out {
    background-color: rgba(230, 74, 25, 0.1);
    color: #e64a19;
}

.product-actions {
    padding: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
}

/* Filter section */
.filter-section {
    margin-bottom: 1.5rem;
}

.filter-section .card-body {
    padding: 1rem;
}

.filter-header {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

/* Form styles */
.form-control.form-control-sm {
    height: calc(1.5em + 0.5rem + 2px);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

/* Table styles */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.product-table th {
    white-space: nowrap;
    padding: 0.75rem;
}

.product-table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    object-fit: contain;
}

/* Pagination */
.pagination-container {
    margin-top: 1.5rem;
}

.pagination .page-item.active .page-link {
    background-color: #2C3E50;
    border-color: #2C3E50;
}

.pagination .page-link {
    color: #2C3E50;
}

.pagination .page-link:focus {
    box-shadow: 0 0 0 0.25rem rgba(44, 62, 80, 0.25);
}

/* Content container */
.content-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
    padding: 2rem;
    margin-bottom: 2rem;
}

/* Page header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
}

/* Section title */
.section-title {
    position: relative;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #2C3E50;
    padding-bottom: 0.5rem;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 3px;
    background-color: #2C3E50;
}

/* Form grid layout */
.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 992px) {
    .form-grid {
        grid-template-columns: 1fr 2fr;
    }
}

/* Form Column */
.form-column > * {
    margin-bottom: 1.5rem;
}

.form-column > *:last-child {
    margin-bottom: 0;
}

/* Required field indicator */
.form-label .text-danger {
    font-weight: bold;
}

/* Image upload styling */
.image-upload-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem 1rem;
    text-align: center;
    transition: all 0.3s;
}

.image-upload-wrapper:hover {
    border-color: #adb5bd;
}

#imagePreview {
    max-height: 300px;
    margin-bottom: 1rem;
    border-radius: 4px;
}

.upload-btn {
    margin-top: 1rem;
} 