<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - ReadHub</title>
    <!-- CSRF Protection -->
    <meta name="_csrf" th:content="${_csrf != null ? _csrf.token : ''}"/>
    <meta name="_csrf_header" th:content="${_csrf != null ? _csrf.headerName : 'X-CSRF-TOKEN'}"/>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        .cart-item {
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
        }
        .cart-item:hover {
            background-color: rgba(0,0,0,0.02);
        }
        .cart-item.selected {
            background-color: rgba(44, 123, 229, 0.05);
        }
        .qty-input-group {
            max-width: 120px;
            margin: 0 auto;
        }
        .btn-qty {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
        }
        .subtotal-section {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }
        .checkout-btn {
            background-color: #2c7be5;
            color: white;
            font-weight: 600;
            padding: 10px 30px;
            border-radius: 5px;
            transition: all 0.3s ease;
            border: none;
        }
        .checkout-btn:hover {
            background-color: #1a68d1;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .continue-shopping-btn {
            border: 1px solid #6c757d;
            color: #6c757d;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 5px;
            transition: all 0.3s ease;
            background-color: transparent;
        }
        .continue-shopping-btn:hover {
            background-color: #6c757d;
            color: white;
        }
        .delete-selected-btn {
            border: 1px solid #dc3545;
            color: #dc3545;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 5px;
            transition: all 0.3s ease;
            background-color: transparent;
        }
        .delete-selected-btn:not(:disabled):hover {
            background-color: #dc3545;
            color: white;
        }
        .delete-selected-btn:disabled {
            border-color: #ced4da;
            color: #ced4da;
            cursor: not-allowed;
        }
        .remove-item-btn {
            width: 32px;
            height: 32px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        .remove-item-btn:hover {
            transform: scale(1.1);
            background-color: #c82333;
        }
        .empty-cart {
            background-color: #f8f9fa;
            border: 1px dashed #ced4da;
            border-radius: 10px;
            padding: 40px 20px;
        }
        .card-cart {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .card-header-cart {
            background-color: #fff;
            border-bottom: 0;
            padding: 20px 25px;
        }
        .book-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            transition: color 0.3s ease;
        }
        .book-title:hover {
            color: #2c7be5;
            text-decoration: none;
        }
        .shop-name {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .shop-name i {
            color: #2c7be5;
        }
        .shop-header-row {
            background-color: #f8f9fa;
            border-top: 2px solid #2c7be5;
        }
        .shop-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.95rem;
        }
        .shop-header .badge {
            font-size: 0.75rem;
        }
        .price-value {
            font-weight: 600;
            color: #333;
        }
        .item-total {
            font-weight: 700;
            color: #2c7be5;
        }
        .cart-summary {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .promotion-section {
            border-top: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            padding: 15px 0;
        }
        .promotion-message {
            font-size: 0.875rem;
        }
        .promotion-message.success {
            color: #198754;
        }
        .promotion-message.error {
            color: #dc3545;
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .summary-total {
            font-size: 1.2rem;
            font-weight: 700;
            color: #2c7be5;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
            margin-top: 10px;
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<!-- Thông báo lỗi ẩn từ server -->
<div id="server-error" class="d-none" th:text="${error}"></div>

<main class="py-5 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Shopping Cart</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Cart Items -->
            <div class="col-lg-8 mb-4 mb-lg-0">
                <div class="card card-cart h-100">
                    <div class="card-header card-header-cart d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Your Shopping Cart</h4>
                        <span id="cartItemCountBadge" class="badge bg-primary rounded-pill" th:if="${cartTotalQuantity > 0}"
                              th:text="${cartTotalQuantity} + ' items'">0 items</span>
                    </div>
                    <div class="card-body p-4" th:if="${cart == null || cart.items == null || cart.items.isEmpty()}">
                        <!-- Empty Cart -->
                        <div class="text-center py-5 empty-cart">
                            <i class="fas fa-shopping-cart fa-4x mb-3 text-muted"></i>
                            <h5>Your cart is currently empty</h5>
                            <p class="text-muted">Browse our catalog and find your next favorite book!</p>
                            <a th:href="@{/}" class="btn btn-primary mt-3 continue-shopping-btn">Continue Shopping</a>
                        </div>
                    </div>
                    <div class="card-body p-4" th:if="${cart != null && cart.items != null && !cart.items.isEmpty()}">
                        <form id="cartForm" th:action="@{/buyer/cart/update}" method="post">
                            <div class="table-responsive">
                                <table class="table align-middle">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" id="selectAll" class="form-check-input"></th>
                                            <th>Book</th>
                                            <th class="text-center">Price</th>
                                            <th class="text-center">Quantity</th>
                                            <th class="text-end">Total</th>
                                            <th class="text-center">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Group items by shop -->
                                        <th:block th:each="shopEntry : ${cartItemsByShop}">
                                            <!-- Shop header row -->
                                            <tr class="shop-header-row">
                                                <td colspan="6" class="shop-header">
                                                    <div class="d-flex align-items-center py-2">
                                                        <i class="fas fa-store text-primary me-2"></i>
                                                        <a th:href="@{/shops/{shopId}(shopId=${shopEntry.value[0].book.shop.shopId})}"
                                                           th:text="${shopEntry.key}"
                                                           class="text-primary text-decoration-none fw-bold">Shop Name</a>
                                                        <span class="badge bg-light text-dark ms-2" th:text="${shopEntry.value.size()} + ' item' + (${shopEntry.value.size()} > 1 ? 's' : '')">2 items</span>
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- Items for this shop -->
                                            <tr th:each="item : ${shopEntry.value}" th:data-book-id="${item.book.bookId}" class="cart-item">
                                                <td>
                                                    <input type="checkbox" name="selected" th:value="${item.book.bookId}"
                                                           class="item-checkbox form-check-input"
                                                           th:disabled="${item.book.stockQuantity == null || item.book.stockQuantity < item.quantity}"
                                                           th:title="${item.book.stockQuantity == null || item.book.stockQuantity < item.quantity ? 'Product does not have enough quantity in stock' : ''}"
                                                           th:data-stock="${item.book.stockQuantity}">
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center gap-3">
                                                        <a th:href="@{/product-detail(book_id=${item.book.bookId})}">
                                                            <img th:src="${item.book.coverImgUrl != null ? item.book.coverImgUrl : '/images/book-placeholder.jpg'}"
                                                                 alt="Cover" class="img-thumbnail rounded" style="width: 60px;height:90px;object-fit:cover;">
                                                        </a>
                                                        <div>
                                                            <a th:href="@{/product-detail(book_id=${item.book.bookId})}" class="book-title" th:text="${item.book.title}">Book Title</a>

                                                            <!-- Always show author if available -->
                                                            <p class="mb-0 small text-muted" th:if="${item.book.authors != null}">
                                                                <i class="fas fa-user-edit"></i>
                                                                <span th:text="'Author: ' + ${item.book.authors}">Tác giả: Author Name</span>
                                                            </p>

                                                            <!-- Show shop name with link -->
                                                            <p class="mb-0 small text-muted" th:if="${item.book.shop != null}">
                                                                <i class="fas fa-store"></i>
                                                                <a th:href="@{/shops/{shopId}(shopId=${item.book.shop.shopId})}"
                                                                   th:text="'Shop: ' + ${item.book.shop.shopName}"
                                                                   class="text-decoration-none">Cửa hàng: Shop Name</a>
                                                            </p>

                                                            <!-- Show low stock warning (5 or less) with author -->
                                                            <p class="mb-0 small text-warning" th:if="${item.book.stockQuantity != null && item.book.stockQuantity <= 5 && item.book.stockQuantity >= item.quantity}">
                                                                <i class="fas fa-exclamation-triangle"></i>
                                                                <span>Only [[${item.book.stockQuantity}]] products left in stock</span>
                                                            </p>

                                                            <!-- Show insufficient stock error -->
                                                            <p class="mb-0 small text-danger" th:if="${item.book.stockQuantity != null && item.book.stockQuantity < item.quantity}">
                                                                <i class="fas fa-exclamation-circle"></i>
                                                                <span>Only [[${item.book.stockQuantity}]] products left in stock</span>
                                                            </p>

                                                            <!-- Show out of stock -->
                                                            <p class="mb-0 small text-danger" th:if="${item.book.stockQuantity == null || item.book.stockQuantity == 0}">
                                                                <i class="fas fa-times-circle"></i> Product out of stock
                                                            </p>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-center price-value" th:text="${#numbers.formatDecimal(item.book.sellingPrice, 0, 'COMMA', 0, 'POINT')}">0</td>
                                                <td class="text-center">
                                                    <div class="input-group input-group-sm justify-content-center qty-input-group">
                                                        <button type="button" class="btn btn-outline-secondary btn-qty qty-btn" data-action="decrease">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                        <input type="text" name="quantity" class="form-control text-center" th:value="${item.quantity}" readonly
                                                               th:data-price="${item.book.sellingPrice}">
                                                        <button type="button" class="btn btn-outline-secondary btn-qty qty-btn" data-action="increase">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                                <td class="text-end item-total" th:text="${#numbers.formatDecimal(item.book.sellingPrice * item.quantity, 0, 'COMMA', 0, 'POINT')}">0</td>
                                                <td class="text-center">
                                                    <button type="button" class="btn btn-sm btn-danger remove-item-btn remove-item">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </th:block>
                                    </tbody>
                                </table>
                            </div>
                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <a th:href="@{/}" class="btn btn-outline-secondary continue-shopping-btn">
                                        <i class="fas fa-arrow-left me-1"></i> Continue Shopping
                                    </a>
                                    <span class="ms-2 text-muted">
                                        <i class="fas fa-info-circle me-1"></i> Select the items you want to checkout
                                    </span>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Cart Summary -->
            <div class="col-lg-4" th:if="${cart != null && cart.items != null && !cart.items.isEmpty()}">
                <div class="card card-cart">
                    <div class="card-header card-header-cart">
                        <h5 class="mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="cart-summary">
                            <div class="summary-item">
                                <span>Selected Items Total</span>
                                <span id="subtotalValue" class="fw-bold">0</span>
                            </div>

                            <!-- Promotion Code Section -->
                            <div class="promotion-section mt-3 mb-3">
                                <div class="input-group">
                                    <input type="text" id="promotionCodeInput" class="form-control" placeholder="Enter promotion code">
                                    <button type="button" id="applyPromotionBtn" class="btn btn-outline-primary">Apply</button>
                                </div>
                                <div id="promotionMessage" class="mt-2" style="display: none;"></div>
                            </div>

                            <!-- Discount Display -->
                            <div id="discountRow" class="summary-item text-success" style="display: none;">
                                <span>Discount (<span id="appliedPromoCode"></span>)</span>
                                <span id="discountValue">-0</span>
                            </div>

                            <div class="summary-item summary-total">
                                <span>Total Amount</span>
                                <span id="totalValue">0</span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-primary checkout-btn w-100" onclick="proceedToCheckout()">
                                    <i class="fas fa-shopping-cart me-1"></i> Proceed to Checkout
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- VND Formatter -->
<script th:src="@{/js/vnd-formatter.js}"></script>
<!-- Custom JavaScript -->
<script th:src="@{/js/scripts.js}"></script>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const selectAll = document.getElementById('selectAll');
        // Use a function to get the current list of checkboxes instead of static storage
        const getItemCheckboxes = () => document.querySelectorAll('.item-checkbox');
        const qtyButtons = document.querySelectorAll('.qty-btn');
        const removeButtons = document.querySelectorAll('.remove-item');
        const subtotalSpan = document.getElementById('subtotalValue');
        const totalSpan = document.getElementById('totalValue');
        const discountLabel = document.getElementById('discountLabel');
        const cartItemCountBadge = document.getElementById('cartItemCountBadge');
        const checkoutBtn = document.querySelector('.checkout-btn');
        const headerCartCountBadge = window.parent.document.getElementById('headerCartCountBadge'); // Access header badge from parent window

        let subtotalAmount = 0;
        let selectedItemsTotal = 0;
        let appliedPromotion = null; // Store applied promotion info
        
        // Format currency using VND formatter
        function formatCurrency(amount) {
            if (window.VNDFormatter) {
                return VNDFormatter.format(amount) + ' đ';
            }
            // Fallback to Intl if VNDFormatter not available
            return new Intl.NumberFormat('vi-VN', {
                style: 'decimal',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount) + ' đ';
        }

        function updateItemTotal(row) {
            const priceText = row.querySelector('.price-value').textContent.trim();
            const price = parseFloat(priceText.replace(/[^\d.]/g, ''));
            const qty = parseInt(row.querySelector('input[name="quantity"]').value) || 0;
            const totalCell = row.querySelector('.item-total');

            if (!isNaN(price) && !isNaN(qty)) {
                const itemTotal = price * qty;
                totalCell.textContent = new Intl.NumberFormat('en-US').format(itemTotal);
            }
        }
        
        function calculateUniqueItemCount() {
            // Calculate unique item count (number of different items)
            const quantityInputs = document.querySelectorAll('input[name="quantity"]');
            return quantityInputs.length;
        }

        function calculateTotalQuantity() {
            // Calculate total quantity (sum of all quantities) - for shop counts
            let totalQuantity = 0;
            const quantityInputs = document.querySelectorAll('input[name="quantity"]');

            quantityInputs.forEach(input => {
                const qty = parseInt(input.value) || 0;
                totalQuantity += qty;
            });

            return totalQuantity;
        }

        function updateAllQuantityBadges() {
            const uniqueItemCount = calculateUniqueItemCount();

            if (cartItemCountBadge) {
                cartItemCountBadge.textContent = uniqueItemCount + ' items';
                cartItemCountBadge.style.display = uniqueItemCount > 0 ? '' : 'none';
            }

            if (headerCartCountBadge) {
                headerCartCountBadge.textContent = uniqueItemCount;
                headerCartCountBadge.style.display = uniqueItemCount > 0 ? '' : 'none';
            }

            // Trigger global cart update event for synchronization across pages
            if (window.dispatchEvent) {
                window.dispatchEvent(new CustomEvent('cartUpdated', {
                    detail: { uniqueItemCount: uniqueItemCount }
                }));
            }

            // Also directly call global cart update function
            if (window.updateGlobalCartCount) {
                window.updateGlobalCartCount();
            }
        }

        function updateCartSummary() {
            let subtotal = 0;
            let selectedSubtotal = 0;

            // Only select cart item rows, not shop header rows
            document.querySelectorAll('tbody tr.cart-item').forEach(row => {
                const priceText = row.querySelector('.price-value').textContent.trim();
                const price = parseFloat(priceText.replace(/[^\d.]/g, ''));
                const qty = parseInt(row.querySelector('input[name="quantity"]').value) || 0;
                const isSelected = row.querySelector('.item-checkbox').checked;

                if (!isNaN(price)) {
                    const itemTotal = price * qty;
                    subtotal += itemTotal;

                    if (isSelected) {
                        selectedSubtotal += itemTotal;
                    }
                }
            });

            subtotalAmount = selectedSubtotal;

            // Update subtotal display
            if (subtotalSpan) subtotalSpan.textContent = formatCurrency(selectedSubtotal);

            // Calculate final total with discount
            let finalTotal = selectedSubtotal;
            let discountAmount = 0;

            if (appliedPromotion && appliedPromotion.success) {
                discountAmount = appliedPromotion.totalDiscount || 0;
                finalTotal = selectedSubtotal - discountAmount;

                // Show discount row
                const discountRow = document.getElementById('discountRow');
                const discountValue = document.getElementById('discountValue');
                const appliedPromoCode = document.getElementById('appliedPromoCode');

                if (discountRow && discountValue && appliedPromoCode) {
                    discountRow.style.display = 'flex';
                    discountValue.textContent = '-' + formatCurrency(discountAmount);
                    appliedPromoCode.textContent = appliedPromotion.promoCode;
                }
            } else {
                // Hide discount row
                const discountRow = document.getElementById('discountRow');
                if (discountRow) {
                    discountRow.style.display = 'none';
                }
            }

            // Update total display
            if (totalSpan) totalSpan.textContent = formatCurrency(finalTotal);

            // Update checkout button state
            updateCheckoutButtonState();

            // Update shop item counts
            updateShopItemCounts();

            updateAllQuantityBadges();
        }

        // Initialize the cart summary when page loads
        if (getItemCheckboxes().length > 0) {
            // Default: no items selected initially
            updateCartSummary();

            // Initialize shop item counts
            updateShopItemCounts();

            // Set checkout button to disabled initially
            if (checkoutBtn) {
                checkoutBtn.classList.add('disabled');
                checkoutBtn.href = 'javascript:void(0)';
            }
        }

        // Discount codes can be applied during checkout

        if (selectAll) {
            selectAll.addEventListener('change', () => {
                // Chỉ chọn những sản phẩm có đủ số lượng trong kho
                getItemCheckboxes().forEach(cb => {
                    if (!cb.disabled) {
                        cb.checked = selectAll.checked;
                        
                        // Update row highlighting
                        const row = cb.closest('tr');
                        if (cb.checked) {
                            row.classList.add('selected');
                        } else {
                            row.classList.remove('selected');
                        }
                    }
                });
                updateCartSummary();
            });
        }

        // Use event delegation for checkboxes instead of directly attaching events
        document.querySelector('table').addEventListener('change', function(e) {
            if (e.target.classList.contains('item-checkbox')) {
                // Checkbox item changed
                const cb = e.target;

                // Prevent immediate deselection by stopping event propagation
                e.stopPropagation();
                
                // Update "select all" checkbox state - chỉ tính những sản phẩm có thể chọn
                if (selectAll) {
                    const availableCheckboxes = Array.from(getItemCheckboxes()).filter(c => !c.disabled);
                    const allChecked = availableCheckboxes.every(c => c.checked);
                    const someChecked = availableCheckboxes.some(c => c.checked);
                    
                    selectAll.checked = allChecked;
                    selectAll.indeterminate = someChecked && !allChecked;
                }
                
                // Highlight selected rows
                const row = cb.closest('tr');
                if (cb.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
                
                // Update cart summary based on selected items
                updateCartSummary();
            }
        });

        // CSRF setup
        function withCsrf(headers = {}) {
            const csrfTokenMeta = document.querySelector('meta[name=\"_csrf\"]');
            if (csrfTokenMeta) {
                const token = csrfTokenMeta.getAttribute('content');
                const headerMeta = document.querySelector('meta[name=\"_csrf_header\"]');
                const headerName = headerMeta ? headerMeta.getAttribute('content') : 'X-CSRF-TOKEN';
                headers[headerName] = token;
            }
            return headers;
        }

        if (checkoutBtn) {
            // Store original href
            checkoutBtn.setAttribute('data-href', checkoutBtn.getAttribute('href'));
            
            // No need to add old event listener as onclick=\"proceedToCheckout()\" already exists
            // Remove old event listener to avoid conflicts
            /*
            checkoutBtn.addEventListener('click', (e) => {
                const selectedIds = Array.from(getItemCheckboxes())
                    .filter(cb => cb.checked)
                    .map(cb => cb.value);
                
                if (selectedIds.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one item to checkout');
                    return false;
                }
                
                // Modify the checkout URL to include selected items
                e.preventDefault();
                const baseUrl = checkoutBtn.getAttribute('data-href') || '/buyer/checkout';
                const redirectUrl = `${baseUrl}?items=${selectedIds.join(',')}`;
                
                // Navigate to the modified URL
                window.location.href = redirectUrl;
                return false;
            });
            */
        }

        // Debounce function to prevent rapid clicking
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Track pending requests to prevent multiple simultaneous updates
        const pendingRequests = new Set();

        // Use event delegation for quantity buttons with improved handling
        document.querySelector('table').addEventListener('click', function(e) {
            if (e.target.classList.contains('qty-btn') || e.target.closest('.qty-btn')) {
                const btn = e.target.closest('.qty-btn') || e.target;
                const row = btn.closest('tr');
                const bookId = row.dataset.bookId;
                const input = row.querySelector('input[name="quantity"]');
                let currentQty = parseInt(input.value) || 1;
                let newQty = currentQty;

                // Prevent multiple simultaneous requests for the same item
                if (pendingRequests.has(bookId)) {
                    return;
                }

                // Calculate new quantity
                if (btn.dataset.action === 'increase') {
                    newQty = currentQty + 1;
                } else if (btn.dataset.action === 'decrease' && currentQty > 1) {
                    newQty = currentQty - 1;
                }

                // Only proceed if quantity actually changed
                if (newQty !== currentQty) {
                    // Disable all quantity buttons for this item
                    const allBtns = row.querySelectorAll('.qty-btn');
                    allBtns.forEach(b => b.disabled = true);

                    // Add visual feedback
                    input.style.opacity = '0.6';

                    pendingRequests.add(bookId);

                    fetch('/buyer/cart/update-qty', {
                        method: 'POST',
                        headers: withCsrf({'Content-Type': 'application/json'}),
                        body: JSON.stringify({bookId: parseInt(bookId), quantity: newQty})
                    })
                    .then(response => {
                        return response.json().then(data => {
                            if (response.ok) {
                                input.value = newQty;
                                updateItemTotal(row);
                                updateCartSummary();
                                updateAllQuantityBadges();
                                if (data.message) {
                                    showToast('success', data.message);
                                }
                            } else {
                                // Revert to original value on error
                                input.value = currentQty;
                                showToast('error', data.message || 'Unable to update quantity.');
                            }
                        });
                    })
                    .catch(error => {
                        console.error('Error updating quantity:', error);
                        // Revert to original value on error
                        input.value = currentQty;
                        showToast('error', 'An error occurred while updating the quantity.');
                    })
                    .finally(() => {
                        // Re-enable buttons and remove visual feedback
                        allBtns.forEach(b => b.disabled = false);
                        input.style.opacity = '1';
                        pendingRequests.delete(bookId);
                    });
                }
            }
        });

        // Function to update shop item counts
        function updateShopItemCounts() {
            const shopHeaders = document.querySelectorAll('.shop-header-row');
            shopHeaders.forEach(header => {
                // Find the next sibling rows until we hit another shop header or end of table
                let nextRow = header.nextElementSibling;
                let itemCount = 0;
                let totalQuantity = 0;

                while (nextRow && !nextRow.classList.contains('shop-header-row')) {
                    if (nextRow.classList.contains('cart-item')) {
                        itemCount++;
                        const quantityInput = nextRow.querySelector('input[name="quantity"]');
                        if (quantityInput) {
                            totalQuantity += parseInt(quantityInput.value) || 0;
                        }
                    }
                    nextRow = nextRow.nextElementSibling;
                }

                // Update the shop item count badge
                const badge = header.querySelector('.badge');
                if (badge) {
                    badge.textContent = `${itemCount} item${itemCount !== 1 ? 's' : ''} (${totalQuantity} total)`;
                }
            });
        }

        // Function to check and remove empty shop headers
        function removeEmptyShopHeaders() {
            const shopHeaders = document.querySelectorAll('.shop-header-row');
            shopHeaders.forEach(header => {
                // Find the next sibling rows until we hit another shop header or end of table
                let nextRow = header.nextElementSibling;
                let hasItems = false;

                while (nextRow && !nextRow.classList.contains('shop-header-row')) {
                    if (nextRow.classList.contains('cart-item')) {
                        hasItems = true;
                        break;
                    }
                    nextRow = nextRow.nextElementSibling;
                }

                // If no items found for this shop, remove the header
                if (!hasItems) {
                    header.remove();
                }
            });
        }

        // Use event delegation for remove buttons
        document.querySelector('table').addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-item') || e.target.closest('.remove-item-btn')) {
                const btn = e.target.closest('.remove-item-btn') || e.target;
                const row = btn.closest('tr');
                const bookId = row.dataset.bookId;

                if (confirm('Are you sure you want to remove this item?')) {
                    fetch(`/buyer/cart/remove/${bookId}`, {
                        method: 'POST',
                        headers: withCsrf({'Content-Type': 'application/json'})
                    }).then(response => {
                        if (response.ok) {
                            // Remove the row with animation
                            row.style.opacity = '0';
                            setTimeout(() => {
                                row.remove();

                                // Update shop item counts before removing empty headers
                                updateShopItemCounts();

                                // Remove empty shop headers
                                removeEmptyShopHeaders();

                                // If no cart items left, reload to show empty cart
                                if (document.querySelectorAll('tbody tr.cart-item').length === 0) {
                                    location.reload();
                                } else {
                                    updateCartSummary();
                                    updateAllQuantityBadges();
                                }
                            }, 300);
                        } else {
                            alert('Could not remove item.');
                        }
                    }).catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while removing the item.');
                    });
                }
            }
        });

        // Update checkout button state
        function updateCheckoutButtonState() {
            const hasSelectedItems = Array.from(getItemCheckboxes())
                .filter(cb => !cb.disabled) // Chỉ tính những sản phẩm có thể chọn
                .some(cb => cb.checked);
            console.log("Has selected items:", hasSelectedItems);
            
            if (checkoutBtn) {
                checkoutBtn.disabled = !hasSelectedItems;
                
                if (hasSelectedItems) {
                    checkoutBtn.classList.remove('disabled');
                    checkoutBtn.removeAttribute('title');
                } else {
                    checkoutBtn.classList.add('disabled');
                    checkoutBtn.setAttribute('title', 'Please select at least one product to checkout');
                }
                
                console.log("Checkout button state updated. Disabled:", checkoutBtn.disabled);
            }
        }

        // Apply promotion function
        function applyPromotion() {
            const promoCode = document.getElementById('promotionCodeInput').value.trim();
            const messageDiv = document.getElementById('promotionMessage');
            const applyBtn = document.getElementById('applyPromotionBtn');

            if (!promoCode) {
                showPromotionMessage('Please enter a promotion code', 'error');
                return;
            }

            // Get selected items for promotion calculation
            const selectedItems = [];
            document.querySelectorAll('tbody tr.cart-item').forEach(row => {
                const checkbox = row.querySelector('.item-checkbox');
                if (checkbox && checkbox.checked) {
                    const bookId = parseInt(checkbox.value);
                    const qty = parseInt(row.querySelector('input[name="quantity"]').value) || 0;
                    const priceText = row.querySelector('.price-value').textContent.trim();
                    const price = parseFloat(priceText.replace(/[^\d.]/g, ''));

                    selectedItems.push({
                        bookId: bookId,
                        quantity: qty,
                        price: price,
                        subtotal: price * qty
                    });
                }
            });

            if (selectedItems.length === 0) {
                showPromotionMessage('Please select items to apply promotion', 'error');
                return;
            }

            // Disable button during request
            applyBtn.disabled = true;
            applyBtn.textContent = 'Applying...';

            // Call promotion API
            fetch('/buyer/cart/apply-promotion', {
                method: 'POST',
                headers: withCsrf({
                    'Content-Type': 'application/json'
                }),
                body: JSON.stringify({
                    promotionCode: promoCode,
                    selectedItems: selectedItems
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    appliedPromotion = data;
                    showPromotionMessage(`Promotion applied! Discount: ${formatCurrency(data.totalDiscount)}`, 'success');
                    updateCartSummary();
                } else {
                    appliedPromotion = null;
                    showPromotionMessage(data.message || 'Failed to apply promotion', 'error');
                    updateCartSummary();
                }
            })
            .catch(error => {
                console.error('Error applying promotion:', error);
                appliedPromotion = null;
                showPromotionMessage('Error applying promotion. Please try again.', 'error');
                updateCartSummary();
            })
            .finally(() => {
                applyBtn.disabled = false;
                applyBtn.textContent = 'Apply';
            });
        }

        function showPromotionMessage(message, type) {
            const messageDiv = document.getElementById('promotionMessage');
            if (messageDiv) {
                messageDiv.textContent = message;
                messageDiv.className = `mt-2 promotion-message ${type}`;
                messageDiv.style.display = 'block';

                // Hide message after 5 seconds
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 5000);
            }
        }

        // Proceed to checkout function
        window.proceedToCheckout = function() {
            console.group("Checkout Process");
            console.log("proceedToCheckout function called");
            
            const selectedIds = Array.from(getItemCheckboxes())
                .filter(cb => !cb.disabled && cb.checked) // Chỉ lấy những sản phẩm có thể chọn và đã được chọn
                .map(cb => cb.value);

            console.log("Selected items for checkout:", selectedIds);

            if (selectedIds.length === 0) {
                console.log("No items selected, showing alert");
                console.groupEnd();
                showToast('error', 'Please select at least one product to checkout');
                return;
            }

            try {
                // Store promotion info in session before checkout
                if (appliedPromotion && appliedPromotion.success) {
                    // Send promotion info to server to store in session
                    fetch('/buyer/cart/store-promotion', {
                        method: 'POST',
                        headers: withCsrf({
                            'Content-Type': 'application/json'
                        }),
                        body: JSON.stringify(appliedPromotion)
                    })
                    .then(() => {
                        // Proceed to checkout after storing promotion
                        proceedToCheckoutWithItems(selectedIds);
                    })
                    .catch(error => {
                        console.error('Error storing promotion:', error);
                        // Proceed anyway without promotion
                        proceedToCheckoutWithItems(selectedIds);
                    });
                } else {
                    // No promotion, proceed directly
                    proceedToCheckoutWithItems(selectedIds);
                }
            } catch (error) {
                console.error("Error during checkout:", error);
                console.groupEnd();
                showToast('error', 'An error occurred while processing the payment. Please try again.');
            }
        };

        function proceedToCheckoutWithItems(selectedIds) {
            // Create checkout URL with fixed path
            const checkoutUrl = new URL('/buyer/checkout', window.location.origin);
            // Add items parameter to URL
            checkoutUrl.searchParams.append('items', selectedIds.join(','));

            console.log("Redirecting to:", checkoutUrl.toString());
            console.groupEnd();

            // Redirect to checkout page
            window.location.href = checkoutUrl.toString();
        };

        // Initialize checkout button state
        updateCheckoutButtonState();

        // Add event listeners for promotion
        const applyPromotionBtn = document.getElementById('applyPromotionBtn');
        const promotionCodeInput = document.getElementById('promotionCodeInput');

        if (applyPromotionBtn) {
            applyPromotionBtn.addEventListener('click', applyPromotion);
        }

        if (promotionCodeInput) {
            promotionCodeInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    applyPromotion();
                }
            });
        }

        // Debug function to check the state of important elements
        function debugCartState() {
            console.group("Cart Debug Info");
            console.log("Checkout button:", checkoutBtn);
            console.log("Checkout button disabled:", checkoutBtn ? checkoutBtn.disabled : "N/A");
            console.log("Item checkboxes count:", getItemCheckboxes().length);
            console.log("Selected items:", Array.from(getItemCheckboxes()).filter(cb => cb.checked).length);
            console.log("Selected item IDs:", Array.from(getItemCheckboxes()).filter(cb => cb.checked).map(cb => cb.value));
            console.groupEnd();
        }
        
        // Call debug function on page load and after any state change
        debugCartState();
        
        // Add debug call to updateCartSummary
        const originalUpdateCartSummary = updateCartSummary;
        updateCartSummary = function() {
            originalUpdateCartSummary();
            debugCartState();
        };

        // Kiểm tra thông báo lỗi từ server
        const errorElement = document.getElementById('server-error');
        if (errorElement && errorElement.textContent.trim() !== '') {
            // Hiển thị lỗi dưới dạng toast
            showToast('error', errorElement.textContent);
        }

        // Hiển thị thông báo kiểu toast
        function showToast(type, message) {
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                // Tạo container cho toast nếu chưa tồn tại
                const container = document.createElement('div');
                container.id = 'toast-container';
                container.style.position = 'fixed';
                container.style.bottom = '20px';
                container.style.right = '20px';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
                toastContainer = container;
            }
            
            const toast = document.createElement('div');
            toast.className = `toast ${type === 'error' ? 'bg-danger' : 'bg-success'} text-white`;
            toast.style.minWidth = '250px';
            toast.innerHTML = `
                <div class="toast-body">
                    ${message}
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 5000
            });
            bsToast.show();
            
            // Xóa toast sau khi ẩn
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }
    });
</script>

<!-- Chat Widget - Buyers Only -->
<div th:replace="~{fragments/chat-widget :: buyer-chat-widget}"></div>

</body>
</html>