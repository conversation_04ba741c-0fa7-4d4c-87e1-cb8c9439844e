<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Management - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Additional styles for new components */
        .notification-card {
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        .notification-card:hover {
            transform: translateY(-3px);
        }
        .notification-card.warning {
            border-color: #f39c12;
        }
        .notification-card.danger {
            border-color: #e74c3c;
        }
        .notification-card.info {
            border-color: #3498db;
        }
        .notification-card.success {
            border-color: #2ecc71;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .activity-item:hover {
            background-color: #f8f9fa;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .activity-icon.order {
            background-color: rgba(44, 62, 80, 0.1);
            color: #2C3E50;
        }
        .activity-icon.review {
            background-color: rgba(241, 196, 15, 0.1);
            color: #f39c12;
        }
        .activity-icon.message {
            background-color: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }
        .activity-icon.stock {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .chart-container {
            position: relative;
            height: 220px;
            margin-bottom: 1rem;
        }

        .quick-action-btn {
            width: 100%;
            margin-bottom: 0.75rem;
            text-align: left;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .quick-action-btn i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .low-stock-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .low-stock-item:hover {
            background-color: #f8f9fa;
        }
        .low-stock-item:last-child {
            border-bottom: none;
        }
        .stock-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .badge-danger {
            background-color: #e74c3c;
            color: white;
        }
        .badge-warning {
            background-color: #f39c12;
            color: white;
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        .overview-header h3 {
            margin-bottom: 0;
        }
        .period-badge {
            background-color: #ecf0f1;
            color: #2C3E50;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
<!-- Include Topbar -->
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
  <div class="row">
    <div class="col-lg-3 mb-4">
      <div th:replace="~{fragments/seller-sidebar :: seller-sidebar}"></div>
    </div>

    <div class="col-lg-9">
      <main class="py-4">
        <h3 class="section-title">Data Management Tools</h3>

        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
          <span th:text="${successMessage}"></span>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
          <span th:text="${errorMessage}"></span>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <div th:if="${warningMessage}" class="alert alert-warning alert-dismissible fade show" role="alert">
          <span th:text="${warningMessage}"></span>
          <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="card h-100">
              <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-upload me-2"></i> Import Data</h5>
              </div>
              <div class="card-body">
                <p class="card-text">Bulk import user or product data from CSV files. Ensure your CSV files are correctly formatted.</p>

                <h6 class="mt-4">Import Products (CSV)</h6>
                <form th:action="@{/seller/data-management/import/products}" method="post" enctype="multipart/form-data">
                  <div class="mb-3">
                    <label for="productCsvFile" class="form-label">Select Product CSV:</label>
                    <input type="file" class="form-control" id="productCsvFile" name="file" accept=".csv" required>
                  </div>
                  <button type="submit" class="btn btn-primary"><i class="fas fa-file-import me-2"></i> Import Products</button>
                </form>
              </div>
            </div>
          </div>

          <div class="col-md-6 mb-4">
            <div class="card h-100">
              <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-download me-2"></i> Export Data</h5>
              </div>
              <div class="card-body">
                <p class="card-text">Export various data for offline reporting, analysis, or backup.</p>
                <div class="d-grid gap-2">
                  <a th:href="@{/seller/data-management/export/products}" class="btn btn-outline-success"><i class="fas fa-file-export me-2"></i> Export Products to CSV</a>
                  <a th:href="@{/seller/data-management/export/orders}" class="btn btn-outline-success"><i class="fas fa-file-export me-2"></i> Export Orders to CSV</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>
<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script>
  // Function to add validation to a file input
  function validateFileInput(inputId) {
    const fileInput = document.getElementById(inputId);
    if (fileInput) {
      fileInput.addEventListener('change', function() {
        const fileName = this.value;
        // Check if a file is selected and if it has the .csv extension
        if (fileName && !fileName.toLowerCase().endsWith('.csv')) {
          alert('Error: Please select a valid .csv file.');
          // Clear the file input
          this.value = '';
        }
      });
    }
  }

  // Apply the validation to both of your file inputs
  validateFileInput('userCsvFile');
  validateFileInput('productCsvFile');
</script>
</body>
</html>