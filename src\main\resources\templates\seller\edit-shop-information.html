<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Shop Information - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
    </style>
</head>
<body>
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
            <li class="breadcrumb-item"><a th:href="@{/seller/dashboard}">Seller Dashboard</a></li>
            <li class="breadcrumb-item"><a th:href="@{/seller/shop-information}">Shop Information</a></li>
            <li class="breadcrumb-item active">Edit</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-3 mb-4">
            <div th:replace="~{fragments/seller-sidebar :: seller-sidebar}"></div>
        </div>

        <div class="col-md-9">
            <div class="account-container">
                <h3 class="section-title">Edit Shop Information</h3>

                <form th:action="@{/seller/shop-information/save}" th:object="${shop}" method="post" enctype="multipart/form-data">
                    <input type="hidden" th:field="*{shopId}" />
                    <input type="hidden" th:field="*{taxCode}" />
                    <input type="hidden" th:field="*{identificationFileUrl}" />

                    <div class="mb-3">
                        <label for="shopName" class="form-label fw-bold">Shop Name</label>
                        <input type="text" class="form-control" id="shopName" th:field="*{shopName}" required>
                        <div class="invalid-feedback" th:if="${#fields.hasErrors('shopName')}" th:errors="*{shopName}"></div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label fw-bold">Shop Description</label>
                        <textarea class="form-control" id="description" th:field="*{description}" rows="4"></textarea>
                    </div>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Shop Logo</label>
                            <div class="mb-2">
                                <img th:if="*{logoUrl != null}" th:src="*{logoUrl}" alt="Current Logo" class="img-thumbnail" id="logoPreview" style="max-width: 120px;">
                                <img th:if="*{logoUrl == null}" src="https://placehold.co/120x120/e2e8f0/64748b?text=No+Logo" alt="No Logo" class="img-thumbnail" id="logoPreview" style="max-width: 120px;">
                            </div>
                            <input type="file" class="form-control" id="logoFile" name="logoFile" accept="image/*">
                            <div class="invalid-feedback" id="logoFileError"></div>
                            <small class="text-muted">Leave empty to keep the current logo. Max size: 5MB</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Cover Image</label>
                            <div class="mb-2">
                                <img th:if="*{coverImageUrl != null}" th:src="*{coverImageUrl}" alt="Current Cover" class="img-thumbnail" id="coverImagePreview" style="max-width: 200px;">
                                <img th:if="*{coverImageUrl == null}" src="https://placehold.co/200x120/e2e8f0/64748b?text=No+Cover" alt="No Cover" class="img-thumbnail" id="coverImagePreview" style="max-width: 200px;">
                            </div>
                            <input type="file" class="form-control" id="coverImageFile" name="coverImageFile" accept="image/*">
                            <div class="invalid-feedback" id="coverImageFileError"></div>
                            <small class="text-muted">Leave empty to keep the current cover image. Max size: 5MB</small>
                        </div>
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Shop Address</h5>
                    <div class="mb-3">
                        <label for="shopDetailAddress" class="form-label fw-bold">Detail Address (Street, Number)</label>
                        <input type="text" class="form-control" id="shopDetailAddress" th:field="*{shopDetailAddress}" required>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="province" class="form-label fw-bold">Province / City</label>
                            <select class="form-select" id="province" required></select>
                            <input type="hidden" id="provinceName" th:field="*{shopProvince}">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="district" class="form-label fw-bold">District</label>
                            <select class="form-select" id="district" required disabled></select>
                            <input type="hidden" id="districtName" th:field="*{shopDistrict}">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="ward" class="form-label fw-bold">Ward / Commune</label>
                            <select class="form-select" id="ward" required disabled></select>
                            <input type="hidden" id="wardName" th:field="*{shopWard}">
                        </div>
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Contact Information</h5>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contactEmail" class="form-label fw-bold">Contact Email</label>
                            <input type="email" class="form-control" id="contactEmail" th:field="*{contactEmail}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="contactPhone" class="form-label fw-bold">Contact Phone</label>
                            <input type="tel" class="form-control" id="contactPhone" th:field="*{contactPhone}" required>
                        </div>
                    </div>

                    <div class="mt-4 text-end">
                        <a th:href="@{/seller/shop-information}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<!-- jQuery -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<!-- Axios -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.26.1/axios.min.js"></script>
<script th:inline="javascript">
    /*<![CDATA[*/
    $(document).ready(function() {
        // File upload validation with preview
        function validateFileUpload(inputId, errorId, previewId, maxSizeMB = 5) {
            const fileInput = document.getElementById(inputId);
            const errorDiv = document.getElementById(errorId);
            const previewImg = document.getElementById(previewId);

            if (fileInput) {
                fileInput.addEventListener('change', function(event) {
                    const file = event.target.files[0];
                    errorDiv.textContent = '';
                    fileInput.classList.remove('is-invalid');

                    if (file) {
                        // Check file size
                        const maxSize = maxSizeMB * 1024 * 1024; // Convert to bytes
                        if (file.size > maxSize) {
                            errorDiv.textContent = `File size must be less than ${maxSizeMB}MB`;
                            fileInput.classList.add('is-invalid');
                            fileInput.value = '';
                            return;
                        }

                        // Check file type
                        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                        if (!validTypes.includes(file.type)) {
                            errorDiv.textContent = 'Please select a valid image file (JPEG, PNG, GIF, WEBP)';
                            fileInput.classList.add('is-invalid');
                            fileInput.value = '';
                            return;
                        }

                        // Show preview
                        if (previewImg && file) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                previewImg.src = e.target.result;
                            };
                            reader.readAsDataURL(file);
                        }

                        console.log(`Valid file selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
                    }
                });
            }
        }

        // Initialize file upload validation with preview
        validateFileUpload('logoFile', 'logoFileError', 'logoPreview');
        validateFileUpload('coverImageFile', 'coverImageFileError', 'coverImagePreview');

        // Form submission validation
        $('form').on('submit', function(e) {
            let hasErrors = false;

            // Check if any file inputs have validation errors
            const logoInput = document.getElementById('logoFile');
            const coverInput = document.getElementById('coverImageFile');

            if (logoInput && logoInput.classList.contains('is-invalid')) {
                hasErrors = true;
            }

            if (coverInput && coverInput.classList.contains('is-invalid')) {
                hasErrors = true;
            }

            if (hasErrors) {
                e.preventDefault();
                alert('Please fix the file upload errors before submitting.');
                return false;
            }

            // Show loading state
            const submitBtn = $(this).find('button[type="submit"]');
            submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i> Saving...');
        });

        // Lấy các giá trị địa chỉ đã lưu từ model của Thymeleaf
        const savedProvince = /*[[${shop.shopProvince}]]*/ null;
        const savedDistrict = /*[[${shop.shopDistrict}]]*/ null;
        const savedWard = /*[[${shop.shopWard}]]*/ null;

        // Debug logging
        console.log('Saved Province:', savedProvince);
        console.log('Saved District:', savedDistrict);
        console.log('Saved Ward:', savedWard);

        const provinceSelect = $('#province');
        const districtSelect = $('#district');
        const wardSelect = $('#ward');

        const provinceNameInput = $('#provinceName');
        const districtNameInput = $('#districtName');
        const wardNameInput = $('#wardName');

        const host = "/api/location"; // API endpoint

        function renderOptions(selectElement, data, placeholder, savedValue) {
            selectElement.html(`<option value="" selected disabled>${placeholder}</option>`);
            $.each(data, function(index, item) {
                // Tạo option và thêm thuộc tính 'selected' nếu nó khớp với giá trị đã lưu
                const isSelected = item.name === savedValue;
                selectElement.append(`<option value="${item.code}" ${isSelected ? 'selected' : ''}>${item.name}</option>`);
            });
        }

        // --- LOGIC XỬ LÝ CHO TRANG EDIT ---

        // 1. Tải danh sách Tỉnh/Thành phố
        axios.get(host + "/provinces")
            .then(function(response) {
                console.log('Provinces loaded:', response.data);
                renderOptions(provinceSelect, response.data, "Select Province/City", savedProvince);

                // Nếu có tỉnh đã lưu, tự động trigger sự kiện change để tải quận/huyện
                if (savedProvince) {
                    console.log('Triggering province change for:', savedProvince);
                    provinceSelect.trigger('change');
                }
            })
            .catch(function(error) {
                console.error("Error fetching provinces:", error);
                provinceSelect.html('<option value="">Error loading provinces</option>');
            });

        // 2. Bắt sự kiện thay đổi Tỉnh/Thành phố
        provinceSelect.change(function() {
            const provinceCode = $(this).val();
            const provinceName = $(this).find('option:selected').text();
            if (provinceName !== "Select Province/City") {
                provinceNameInput.val(provinceName);
            }

            if (provinceCode) {
                axios.get(host + `/provinces/${provinceCode}?depth=2`)
                    .then(function(response) {
                        renderOptions(districtSelect, response.data.districts, "Select District", savedDistrict);
                        districtSelect.prop('disabled', false);

                        // Nếu có quận đã lưu, tự động trigger sự kiện change để tải phường/xã
                        if (savedDistrict) {
                            districtSelect.trigger('change');
                        }
                    })
                    .catch(function(error) { console.error("Error fetching districts:", error); });
            } else {
                districtSelect.html('').prop('disabled', true);
                wardSelect.html('').prop('disabled', true);
            }
        });

        // 3. Bắt sự kiện thay đổi Quận/Huyện
        districtSelect.change(function() {
            const districtCode = $(this).val();
            const districtName = $(this).find('option:selected').text();
            if (districtName !== "Select District") {
                districtNameInput.val(districtName);
            }

            if (districtCode) {
                axios.get(host + `/districts/${districtCode}?depth=2`)
                    .then(function(response) {
                        renderOptions(wardSelect, response.data.wards, "Select Ward/Commune", savedWard);
                        wardSelect.prop('disabled', false);
                    })
                    .catch(function(error) { console.error("Error fetching wards:", error); });
            } else {
                wardSelect.html('').prop('disabled', true);
            }
        });

        // 4. Bắt sự kiện thay đổi Phường/Xã
        wardSelect.change(function() {
            const wardName = $(this).find('option:selected').text();
            if (wardName !== "Select Ward/Commune") {
                wardNameInput.val(wardName);
            }
        });
    });
    /*]]>*/
</script>
</body>
</html>