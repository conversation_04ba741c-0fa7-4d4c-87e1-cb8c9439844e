<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.w3.org/1999/xhtml">
<body>

<header th:fragment="header-content">
    <!-- Top Banner -->
    <div class="top-banner text-center py-1">
        Lots of discount codes are waiting for you
    </div>

    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom main-navbar">
        <div class="container">
            <a class="navbar-brand bookix-logo" th:href="@{/}">ReadHub</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNavDropdown">
                <ul class="navbar-nav mx-auto">
                    <li class="nav-item">
                        <a class="nav-link active" aria-current="page" th:href="@{/}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/all-category">Shop</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/blog">Blog</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="pagesDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            Pages
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="pagesDropdown">
                            <li><a class="dropdown-item" href="/terms-policy">Terms and Policy</a></li>

                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about-contact">About Us</a>
                    </li>
                </ul>
                <div class="navbar-nav nav-right-actions">
                    <!-- Search Icon and Dropdown -->
                    <div class="nav-item dropdown">
                        <a class="nav-link" href="#" id="searchIconToggle" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-search"></i> <span class="d-none d-lg-inline">Search</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end p-3 search-dropdown" style="width: 300px;" aria-labelledby="searchIconToggle">
                            <form class="" th:action="@{/all-category}" method="get">
                                <div class="input-group">
                                    <input name="search" class="form-control" type="search" placeholder="Search books..." aria-label="Search">
                                    <button class="btn btn-primary" type="submit"><i class="fas fa-search"></i></button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- User Account - Show Login link when not authenticated, or username with dropdown when authenticated -->
                    <div class="nav-item dropdown" sec:authorize="isAuthenticated()">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i> <span class="d-none d-lg-inline" th:text="${user != null ? user.fullName : #authentication.name}">User</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li sec:authorize="hasRole('BUYER')"><a class="dropdown-item" th:href="@{/buyer/account-info}"><i class="fas fa-user-circle me-2"></i>Manage Account</a></li>

                            <li sec:authorize="hasRole('BUYER') and !hasRole('SELLER')">
                                <a class="dropdown-item" th:href="@{/buyer/register-shop}">
                                    <i class="fas fa-store-alt me-2"></i>Seller Registration
                                </a>
                            </li>
                            <li sec:authorize="hasRole('ADMIN')"><a class="dropdown-item" th:href="@{/admin/products}"><i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard</a></li>

                            <li sec:authorize="hasRole('SELLER')"><a class="dropdown-item" th:href="@{/seller/dashboard}"><i class="fas fa-store me-2"></i>Seller Dashboard</a></li>

                            <li><hr class="dropdown-divider"></li>

                            <li sec:authorize="isAuthenticated()">
                                <a class="dropdown-item" th:href="@{/logout}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Log Out
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- Login link - Only show when not authenticated -->
                    <a th:href="@{/buyer/login}" class="nav-link" sec:authorize="!isAuthenticated()"><i class="fas fa-user"></i> <span class="d-none d-lg-inline">Login</span></a>

                    <!-- Shopping Cart - Link to cart page or login if not authenticated -->
                    <a th:href="${#authorization.expression('isAuthenticated()') ? '/buyer/cart' : '/buyer/login'}" class="nav-link position-relative">
                        <i class="fas fa-shopping-cart"></i> <span class="d-none d-lg-inline">Cart</span>
                        <span id="headerCartCountBadge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                              style="font-size: 0.7rem; display: none;">0</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>
    <!-- Search bar for mobile/tablet toggle -->
    <div class="container d-lg-none mt-2" id="searchFormMobileContainer" style="display: none;">
        <form class="d-flex" id="searchFormMobile" th:action="@{/all-category}" method="get">
            <input name="search" class="form-control me-2 search-input-header" type="search" placeholder="Search books..." aria-label="Search">
            <button class="btn btn-sm btn-primary" type="submit">Search</button>
        </form>
    </div>

    <!-- CSS for search dropdown -->
    <style>
        .search-dropdown {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            animation: fadeIn 0.3s ease-in-out;
            min-width: 300px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Focus styles for search input */
        .search-dropdown .form-control:focus {
            box-shadow: none;
            border-color: #80bdff;
        }

        /* Style for search button */
        .search-dropdown .btn-primary {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        /* Make the search dropdown wider on larger screens */
        @media (min-width: 768px) {
            .search-dropdown {
                min-width: 400px;
            }
        }
    </style>

    <!-- JavaScript for search behavior -->
    <script defer>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto focus input when search dropdown is shown
            const searchDropdown = document.querySelector('.search-dropdown');
            const searchInput = searchDropdown?.querySelector('input[name="search"]');

            // Listen for dropdown events to focus the search input
            document.querySelector('#searchIconToggle')?.addEventListener('click', function() {
                // Use setTimeout to ensure dropdown is fully shown before focusing
                setTimeout(() => {
                    searchInput?.focus();
                }, 200);
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                const dropdown = document.querySelector('.search-dropdown.show');
                const searchIcon = document.querySelector('#searchIconToggle');

                if (dropdown && !dropdown.contains(e.target) && e.target !== searchIcon && !searchIcon?.contains(e.target)) {
                    // Use Bootstrap's dropdown API to hide the dropdown
                    bootstrap.Dropdown.getInstance(searchIcon)?.hide();
                }
            });

            // Prevent dropdown from closing when clicking inside
            searchDropdown?.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    </script>
</header>

<!-- Initialize dropdowns -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        dropdownElementList.forEach(function(dropdownToggleEl) {
            new bootstrap.Dropdown(dropdownToggleEl);
        });
    });
</script>

<!-- Global Cart Count Synchronization Script -->
<script th:if="${#authorization.expression('isAuthenticated()')}">
    // Global function to update cart count across all pages
    window.updateGlobalCartCount = function() {
        const headerCartCountBadge = document.getElementById('headerCartCountBadge');
        if (!headerCartCountBadge) {
            console.warn('Header cart count badge not found');
            return;
        }

        fetch('/buyer/cart/total-quantity', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                // Add CSRF token if available
                ...(window.getCsrfHeaders ? window.getCsrfHeaders() : {})
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                const uniqueItemCount = data.uniqueItemCount || 0;
                headerCartCountBadge.textContent = uniqueItemCount;

                // Always show badge if count > 0, hide if count = 0
                if (uniqueItemCount > 0) {
                    headerCartCountBadge.style.display = '';
                    headerCartCountBadge.style.visibility = 'visible';
                } else {
                    headerCartCountBadge.style.display = 'none';
                }

                console.log('Cart count updated:', uniqueItemCount);

                // Trigger custom event for other components to listen
                window.dispatchEvent(new CustomEvent('cartCountUpdated', {
                    detail: { uniqueItemCount: uniqueItemCount }
                }));
            } else {
                console.error('Failed to get cart unique item count:', data.message);
                // Hide badge on error
                headerCartCountBadge.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error fetching cart count:', error);
            // Hide badge on error
            headerCartCountBadge.style.display = 'none';
        });
    };

    // Helper function for CSRF headers (fallback if not defined elsewhere)
    if (!window.getCsrfHeaders) {
        window.getCsrfHeaders = function() {
            const headers = {};
            const csrfToken = document.querySelector('meta[name="_csrf"]')?.getAttribute('content');
            const csrfHeader = document.querySelector('meta[name="_csrf_header"]')?.getAttribute('content');
            if (csrfToken && csrfHeader) {
                headers[csrfHeader] = csrfToken;
            }
            return headers;
        };
    }

    // Update cart count on page load with multiple triggers
    document.addEventListener('DOMContentLoaded', function() {
        // Initial update
        window.updateGlobalCartCount();

        // Also update after a short delay to ensure everything is loaded
        setTimeout(function() {
            window.updateGlobalCartCount();
        }, 500);
    });

    // Update when page becomes visible (for browser tab switching)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            window.updateGlobalCartCount();
        }
    });

    // Listen for cart updates from other components
    window.addEventListener('cartUpdated', function() {
        window.updateGlobalCartCount();
    });

    // Update on window focus (for when user returns to tab)
    window.addEventListener('focus', function() {
        window.updateGlobalCartCount();
    });

    // Expose function for manual testing/debugging
    window.refreshCartCount = function() {
        console.log('Manually refreshing cart count...');
        window.updateGlobalCartCount();
    };
</script>
</body>
</html>