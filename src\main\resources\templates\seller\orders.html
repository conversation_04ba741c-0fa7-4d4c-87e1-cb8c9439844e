<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Orders - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Page-specific styles for orders */
        .table th, .table td {
            vertical-align: middle;
        }
        .table thead th {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            color: #333;
        }
        .pagination .page-link {
            color: #2C3E50;
        }
        .pagination .page-item.active .page-link {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card-footer {
            border-top: 1px solid #dee2e6;
            background-color: #fff;
            padding: 1rem;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            margin-top: -1.5rem;
        }
    </style>
</head>
<body>
<!-- Include Topbar -->
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
    <div class="row">
        <!-- Include Sidebar -->
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9">
            <main>
                <h3 class="section-title">
                    Orders Management <span class="badge bg-secondary align-middle" th:text="${orderPage.totalElements}"></span>
                </h3>

                <div th:if="${successMessage}" class="alert alert-success d-flex align-items-center" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <div th:text="${successMessage}"></div>
                </div>
                <div th:if="${errorMessage}" class="alert alert-danger d-flex align-items-center" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <div th:text="${errorMessage}"></div>
                </div>

                <div class="account-container" style="padding-bottom: 1rem; margin-bottom: 0;">
                    <form th:action="@{/seller/orders}" method="get">
                        <div class="row g-2 align-items-end">
                            <div class="col-lg-4 col-md-12">
                                <label for="keyword" class="form-label">Search</label>
                                <input type="text" id="keyword" name="keyword" class="form-control" placeholder="Order ID, Buyer Name..." th:value="${keyword}">
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <label for="statusFilter" class="form-label">Status</label>
                                <select id="statusFilter" name="status" class="form-select">
                                    <option value="">All Statuses</option>
                                    <option th:each="s : ${T(com.example.isp392.model.OrderStatus).values()}"
                                            th:value="${s}" th:text="${s}" th:selected="${s == selectedStatus}"></option>
                                </select>
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <label for="startDate" class="form-label">From Date</label>
                                <input type="date" id="startDate" name="startDate" class="form-control" th:value="${startDate}">
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <label for="endDate" class="form-label">To Date</label>
                                <input type="date" id="endDate" name="endDate" class="form-control" th:value="${endDate}">
                            </div>

                            <div class="col-lg-1 col-md-3">
                                <button type="submit" class="btn btn-primary w-100" title="Filter">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>

                            <div class="col-lg-1 col-md-3">
                                <a th:href="@{/seller/orders}" class="btn btn-outline-secondary w-100" title="Clear Filters">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="account-container" style="border-top-left-radius: 0; border-top-right-radius: 0; padding-top: 1rem;">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                            <tr>
                                <th>
                                    <a th:href="@{/seller/orders(sortField='orderId', sortDir=${sortField == 'orderId' ? reverseSortDir : 'asc'}, keyword=${keyword}, status=${selectedStatus}, startDate=${startDate}, endDate=${endDate})}">
                                        Order
                                        <i th:if="${sortField == 'orderId'}" th:class="${sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                    </a>
                                </th>
                                <th>
                                    <a th:href="@{/seller/orders(sortField='orderDate', sortDir=${sortField == 'orderDate' ? reverseSortDir : 'asc'}, keyword=${keyword}, status=${selectedStatus}, startDate=${startDate}, endDate=${endDate})}">
                                        Date
                                        <i th:if="${sortField == 'orderDate'}" th:class="${sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                    </a>
                                </th>
                                <th>Buyer</th>
                                <th>
                                    <a th:href="@{/seller/orders(sortField='totalAmount', sortDir=${sortField == 'totalAmount' ? reverseSortDir : 'asc'}, keyword=${keyword}, status=${selectedStatus}, startDate=${startDate}, endDate=${endDate})}">
                                        Total
                                        <i th:if="${sortField == 'totalAmount'}" th:class="${sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                    </a>
                                </th>
                                <th>Status</th>
                                <th class="text-center">Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="order : ${orderPage.content}" th:object="${order}">
                                <td>
                                    <a class="fw-bold text-decoration-none" th:href="@{/seller/orders/{id}(id=*{orderId})}" th:text="*{'#' + orderId}"></a>
                                    <div class="small text-muted" th:text="${#lists.size(order.orderItems)} + ' items'"></div>
                                </td>
                                <td class="small" th:text="*{#temporals.format(orderDate, 'dd MMM yy, HH:mm')}"></td>
                                <td>
                                    <div class="fw-bold" th:text="*{customerOrder != null ? customerOrder.recipientName : 'N/A'}"></div>
                                    <div class="small text-muted" th:text="*{customerOrder != null ? customerOrder.recipientPhone : 'N/A'}"></div>
                                </td>
                                <td class="fw-bold" th:text="*{#numbers.formatDecimal(totalAmount, 0, 'COMMA', 0, 'POINT')} + ' VND'"></td>
                                <td>
                                    <span class="badge rounded-pill"
                                          th:classappend="*{orderStatus == null ? 'bg-secondary' :
                                                         (orderStatus.name() == 'PENDING' ? 'bg-warning text-dark' :
                                                         (orderStatus.name() == 'PROCESSING' ? 'bg-info text-dark' :
                                                         (orderStatus.name() == 'SHIPPED' ? 'bg-primary' :
                                                         (orderStatus.name() == 'DELIVERED' ? 'bg-success' : 'bg-danger'))))}"
                                          th:text="*{orderStatus != null ? orderStatus : 'N/A'}">
                                    </span>
                                </td>
                                <td class="text-center">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" th:href="@{/seller/orders/{id}(id=*{orderId})}"><i class="fas fa-eye me-2"></i>View Details</a></li>

                                            <li th:if="*{orderStatus != null and orderStatus.name() == 'PROCESSING'}">
                                                <a class="dropdown-item" th:href="@{/seller/orders/print-label/{id}(id=*{orderId})}"><i class="fas fa-print me-2"></i>Print Label</a>
                                            </li>

                                            <li th:if="*{orderStatus != null and orderStatus.name() == 'PROCESSING'}">
                                                <form th:action="@{/seller/orders/update-status/{orderId}(orderId=*{orderId})}" method="post" class="d-inline">
                                                    <input type="hidden" name="newStatus" th:value="${T(com.example.isp392.model.OrderStatus).SHIPPED}"/>
                                                    <button type="submit" class="dropdown-item"><i class="fas fa-shipping-fast me-2"></i>Ship Order</button>
                                                </form>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr th:if="${orderPage.empty}">
                                <td colspan="6" class="text-center py-4">No orders found with the current filters.</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="card-footer bg-white py-3" th:if="${orderPage.totalPages > 1}">
                        <div class="row align-items-center">
                            <div class="col-md-6 text-muted small">
                                Showing <strong th:text="${orderPage.number * orderPage.size + 1}"></strong>
                                to <strong th:text="${orderPage.number * orderPage.size + orderPage.numberOfElements}"></strong>
                                of <strong th:text="${orderPage.totalElements}"></strong> entries
                            </div>
                            <div class="col-md-6">
                                <nav aria-label="Order table navigation" class="float-md-end">
                                    <ul class="pagination pagination-sm mb-0">
                                        <li class="page-item" th:classappend="${orderPage.isFirst() ? 'disabled' : ''}">
                                            <a class="page-link" th:href="@{/seller/orders(page=${orderPage.number - 1}, size=${orderPage.size}, keyword=${keyword}, status=${selectedStatus}, startDate=${startDate}, endDate=${endDate})}">Previous</a>
                                        </li>
                                        <li class="page-item" th:each="i : ${#numbers.sequence(0, orderPage.totalPages - 1)}" th:classappend="${i == orderPage.number ? 'active' : ''}">
                                            <a class="page-link" th:href="@{/seller/orders(page=${i}, size=${orderPage.size}, keyword=${keyword}, status=${selectedStatus}, startDate=${startDate}, endDate=${endDate})}" th:text="${i + 1}"></a>
                                        </li>
                                        <li class="page-item" th:classappend="${orderPage.isLast() ? 'disabled' : ''}">
                                            <a class="page-link" th:href="@{/seller/orders(page=${orderPage.number + 1}, size=${orderPage.size}, keyword=${keyword}, status=${selectedStatus}, startDate=${startDate}, endDate=${endDate})}">Next</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>