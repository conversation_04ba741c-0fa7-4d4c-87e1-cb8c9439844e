<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${book.title + ' - ReadHub'}">Book Title - ReadHub</title> <!-- Dynamic title -->
    <!-- CSRF Protection -->
    <meta name="_csrf" th:content="${_csrf != null ? _csrf.token : ''}"/>
    <meta name="_csrf_header" th:content="${_csrf != null ? _csrf.headerName : 'X-CSRF-TOKEN'}"/>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="product-detail-page py-4">
    <div class="container">
        <!-- Breadcrumbs and Sale Badge -->
        <div class="row mb-4 align-items-center">
            <div class="col-md-6">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb bg-transparent p-0 m-0">
                        <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                        <!-- Hiển thị danh mục đầu tiên của sách trong breadcrumb nếu có -->
                        <li class="breadcrumb-item" th:if="${!book.categories.isEmpty()}">
                            <a th:href="@{/all-category(category=${book.categories.iterator().next().categoryId})}" th:text="${book.categories.iterator().next().categoryName}">Category</a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page" th:text="${book.title}">Book Title</li>
                    </ol>
                </nav>
            </div>
            <div class="col-md-6 text-md-end">
                <!-- Hiển thị badge giảm giá nếu sách đang được giảm giá -->
                <span class="badge bg-danger product-sale-badge-breadcrumb" 
                     th:if="${book.originalPrice != null && book.sellingPrice != null && book.originalPrice.compareTo(book.sellingPrice) > 0}">Sale!</span>
            </div>
        </div>

        <!-- Product Display Section -->
        <section class="product-display-section mb-5">
            <div class="row">
                <!-- Product Image -->
                <div class="col-md-5 mb-4 mb-md-0">
                    <div class="product-image-gallery">
                        <div class="main-product-image position-relative">
                            <!-- Hiển thị ảnh bìa sách từ cơ sở dữ liệu hoặc ảnh mặc định nếu không có -->
                            <img th:src="${book.coverImgUrl != null ? book.coverImgUrl : '/images/product-detail-avenir-next.jpg'}" 
                                 th:alt="${book.title}" class="img-fluid rounded border" id="mainProductImage">
                            <button class="btn btn-light btn-sm position-absolute top-0 end-0 m-2" aria-label="Expand image" data-bs-toggle="modal" data-bs-target="#imageZoomModal">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Product Information & Actions -->
                <div class="col-md-7 ps-md-5">
                    <h1 class="product-title-detail display-6" th:text="${book.title}">Book Title</h1>
                    
                    <!-- Thêm hiển thị đánh giá sao -->
                    <div class="product-rating mb-2">
                        <div th:replace="~{fragments/book-rating :: star-rating(${book.averageRating})}"></div>
                    </div>
                    <div class="product-price-detail mb-3">
                        <!-- Sử dụng fragment hiển thị giá với style lớn hơn -->
                        <div th:replace="~{fragments/book-price :: price-display(${book.originalPrice}, ${book.sellingPrice})}"
                             class="fs-1 fw-bold"></div>
                    </div>

                    <div class="product-actions mb-4">
                        <div class="quantity-selector d-inline-flex align-items-center border rounded me-3 mb-2 mb-sm-0" style="width: 120px;">
                            <button class="btn btn-light btn-sm quantity-btn" type="button" id="decreaseBtn" data-action="decrease"><i class="fas fa-minus"></i></button>
                            <input type="text" readonly class="form-control form-control-sm text-center border-0" value="1" id="quantityInput" aria-label="Quantity">
                            <button class="btn btn-light btn-sm quantity-btn" type="button" id="increaseBtn" data-action="increase"><i class="fas fa-plus"></i></button>
                        </div>
                        <p></p>
                        <div class="d-flex flex-wrap gap-2">
                            <!-- Nút Add to Cart - Yêu cầu đăng nhập khi chưa authenticated -->
                            <button class="btn btn-primary btn-lg add-to-cart-btn" type="button"
                                    th:attr="data-auth=${isAuthenticated}"
                                    th:disabled="${book.stockQuantity == null || book.stockQuantity == 0}"
                                    th:title="${book.stockQuantity == null || book.stockQuantity == 0 ? 'Out of stock' : ''}">
                                <i class="fas fa-shopping-cart me-2"></i>
                                <span th:if="${book.stockQuantity == null || book.stockQuantity == 0}">Out of Stock</span>
                                <span th:unless="${book.stockQuantity == null || book.stockQuantity == 0}">Add to cart</span>
                            </button>

                            <!-- Nút Buy Now - Yêu cầu đăng nhập khi chưa authenticated -->
                            <button class="btn btn-success btn-lg buy-now-btn" type="button"
                                    th:attr="data-auth=${isAuthenticated}"
                                    th:disabled="${book.stockQuantity == null || book.stockQuantity == 0}"
                                    th:title="${book.stockQuantity == null || book.stockQuantity == 0 ? 'Out of stock' : ''}">
                                <i class="fas fa-bolt me-2"></i>
                                <span th:if="${book.stockQuantity == null || book.stockQuantity == 0}">Out of Stock</span>
                                <span th:unless="${book.stockQuantity == null || book.stockQuantity == 0}">Buy Now</span>
                            </button>
                        </div>
                    </div>

                    <div class="product-meta mt-4">
                        <p class="mb-1"><strong class="text-secondary">Author:</strong> <span th:text="${book.authors}">Author Name</span></p>
                        <p class="mb-1" th:if="${book.sku != null}"><strong class="text-secondary">SKU:</strong> <span th:text="${book.sku}">SKU</span></p>
                        <p class="mb-1" th:if="${!book.categories.isEmpty()}"><strong class="text-secondary">Category:</strong> 
                            <span th:each="category, iterStat : ${book.categories}">
                                <a th:href="@{/all-category(category=${category.categoryId})}" th:text="${category.categoryName}">Category</a><span th:if="${!iterStat.last}">, </span>
                            </span>
                        </p>
                        <p class="mb-1" th:if="${book.authors != null}"><strong class="text-secondary">Author:</strong> <span th:text="${book.authors}">Author Name</span></p>
                        <p class="mb-1" th:if="${book.publisher != null}"><strong class="text-secondary">Publisher:</strong> <span th:text="${book.publisher.publisherName}">Publisher Name</span></p>
                        <p class="mb-1"><strong class="text-secondary">Views:</strong> <span th:text="${book.viewsCount}">0</span></p>
                        <!-- Hiển thị thông tin về số lượng tồn kho -->
                        <p class="mb-1">
                            <strong class="text-secondary">Availability:</strong>
                            <span th:if="${book.stockQuantity == null || book.stockQuantity == 0}" class="text-danger">
                                <i class="fas fa-times-circle"></i> Out of Stock
                            </span>
                            <span th:if="${book.stockQuantity != null && book.stockQuantity > 0 && book.stockQuantity <= 5}" class="text-warning">
                                <i class="fas fa-exclamation-triangle"></i> Low Stock ([[${book.stockQuantity}]] left)
                            </span>
                            <span th:if="${book.stockQuantity != null && book.stockQuantity > 5}" class="text-success">
                                <i class="fas fa-check-circle"></i> In Stock
                            </span>
                        </p>

                        <p class="mb-1" th:if="${book.shop != null}">
                            <strong class="text-secondary">Sold by:</strong>
                            <a th:href="@{/shops/{shopId}(shopId=${book.shop.shopId})}"
                               th:text="${book.shop.shopName}"
                               class="text-decoration-none fw-bold">
                                Shop Name
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Product Content: Description, Additional Info, Reviews -->
        <section class="product-content-details">
            <!-- Description -->
            <div class="product-content-block mb-5">
                <h3 class="section-subheader-line"><span>Description</span></h3>
                <!-- Display book description from database -->
                <div th:if="${book.description != null and !book.description.isEmpty()}">
                    <!-- Display original description, may contain HTML formatting -->
                    <div th:utext="${book.description}"></div>
                </div>
                
                <!-- Display message if no description available -->
                <div th:if="${book.description == null or book.description.isEmpty()}">
                    <p class="text-muted">No description available for this book.</p>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="product-content-block mb-5">
                <h3 class="section-subheader-line"><span>Additional information</span></h3>
                <div class="additional-info-grid">
                    <div class="info-item" th:if="${book.numberOfPages > 0}">
                        <i class="fas fa-ruler-combined me-2 text-secondary"></i>
                        <div>
                            <span class="info-label">Length</span>
                            <span class="info-value" th:text="${book.numberOfPages + ' pages'}">320 pages</span>
                        </div>
                    </div>
                    <div class="info-item" th:if="${book.publicationDate != null}">
                        <i class="far fa-calendar-alt me-2 text-secondary"></i>
                        <div>
                            <span class="info-label">Publication date</span>
                            <span class="info-value" th:text="${#temporals.format(book.publicationDate, 'MMMM d, yyyy')}">October 16, 2023</span>
                        </div>
                    </div>
                    <div class="info-item" th:if="${book.dimensions != null}">
                        <i class="fas fa-arrows-alt-h me-2 text-secondary"></i>
                        <div>
                            <span class="info-label">Dimensions</span>
                            <span class="info-value" th:text="${book.dimensions}">6.28 x 1.1 x 5.28 inches</span>
                        </div>
                    </div>
                    <div class="info-item" th:if="${book.isbn != null}">
                        <i class="fas fa-barcode me-2 text-secondary"></i>
                        <div>
                            <span class="info-label">ISBN</span>
                            <span class="info-value" th:text="${book.isbn}">978-3-16-148410-0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reviews Section -->
            <div class="product-content-block mb-5" id="reviewsSection">
                <h3 class="section-subheader-line">
                    <span>Reviews (<span th:text="${totalReviews}">0</span>)</span>
                </h3>
                <!-- Review Filters -->
                <div class="review-filters d-flex justify-content-end mb-4 gap-2">
                    <form id="reviewFilterForm" th:action="@{/product-detail}" method="get" class="d-flex gap-2">
                        <input type="hidden" name="book_id" th:value="${book.bookId}">
                        <input type="hidden" name="page" value="0">
                        <input type="hidden" name="size" th:value="${param.size != null ? param.size : 5}">
                        <input type="hidden" name="sort" th:value="${sortField}" id="sortField">
                        <input type="hidden" name="direction" th:value="${sortDirection}" id="sortDirection">
                        
                        <!-- Filter by rating -->
                        <select class="form-select form-select-sm w-auto" aria-label="Filter by stars" name="minRating" onchange="document.getElementById('reviewFilterForm').submit();">
                            <option value="" th:selected="${minRating == null}">All Stars</option>
                            <option value="5" th:selected="${minRating != null && minRating == 5}">5 Stars</option>
                            <option value="4" th:selected="${minRating != null && minRating == 4}">4 Stars & Up</option>
                            <option value="3" th:selected="${minRating != null && minRating == 3}">3 Stars & Up</option>
                            <option value="2" th:selected="${minRating != null && minRating == 2}">2 Stars & Up</option>
                            <option value="1" th:selected="${minRating != null && minRating == 1}">1 Star & Up</option>
                        </select>
                        
                        <!-- Sort reviews -->
                        <select class="form-select form-select-sm w-auto" aria-label="Sort reviews" id="sortSelect" onchange="changeSorting(this.value)">
                            <option value="createdDate,DESC" th:selected="${sortField == 'createdDate' && sortDirection == 'DESC'}">Sort by Latest</option>
                            <option value="createdDate,ASC" th:selected="${sortField == 'createdDate' && sortDirection == 'ASC'}">Sort by Oldest</option>
                            <option value="rating,DESC" th:selected="${sortField == 'rating' && sortDirection == 'DESC'}">Highest Rating</option>
                            <option value="rating,ASC" th:selected="${sortField == 'rating' && sortDirection == 'ASC'}">Lowest Rating</option>
                        </select>
                    </form>
                </div>

                <!-- Message when no reviews -->
                <div class="alert alert-info" th:if="${bookReviews == null || bookReviews.empty}">
                    <p class="mb-0">This book doesn't have any reviews yet. Be the first to review!</p>
                </div>

                <!-- List of Reviews -->
                <div class="reviews-list mb-4" th:if="${bookReviews != null && !bookReviews.empty}">
                    <!-- Single Review Item (Dynamic from database) -->
                    <div class="review-item d-flex mb-4 pb-3 border-bottom" th:each="review : ${bookReviews}">
                        <!-- Default avatar if user has no profile image -->
                        <img th:src="${review.user.profilePicUrl != null ? review.user.profilePicUrl : '/images/default-avatar.jpg'}"
                             th:alt="${review.user.fullName}" class="reviewer-avatar rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;">
                        
                        <div class="review-content">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="reviewer-name mb-0">
                                    <span th:text="${review.user.fullName}">User Name</span>
                                    <!-- Verified purchase badge if orderItem exists -->
                                    <span class="text-success small" th:if="${review.orderItem != null}">
                                        <i class="fas fa-check-circle me-1"></i> Verified Purchase
                                    </span>
                                </h6>
                                <!-- Format date nicely -->
                                <span class="review-date text-muted small" th:text="${#temporals.format(review.createdDate, 'dd MMM, yyyy')}">Date</span>
                            </div>
                            
                            <!-- Use star-rating fragment for consistent display -->
                            <div class="my-1">
                                <div th:replace="~{fragments/book-rating :: star-rating(${review.rating})}"></div>
                            </div>
                            
                            <!-- Show review title if available -->
                            <h6 th:if="${review.title != null && !review.title.isEmpty()}" class="mb-1" th:text="${review.title}">Review Title</h6>
                            
                            <!-- Review content -->
                            <p class="review-text mb-0" th:text="${review.content}">Review content</p>
                        </div>
                    </div>
                </div>

                <!-- Review Pagination -->
                <nav aria-label="Review navigation" class="d-flex justify-content-center" th:if="${totalPages > 0}">
                    <ul class="pagination">
                        <!-- Previous page -->
                        <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/product-detail(book_id=${book.bookId}, page=${currentPage - 1}, size=${param.size}, sort=${sortField}, direction=${sortDirection}, minRating=${minRating})}" tabindex="-1" th:aria-disabled="${currentPage == 0}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        
                        <!-- Page numbers -->
                        <li class="page-item" th:each="i: ${#numbers.sequence(0, totalPages - 1)}" th:classappend="${i == currentPage ? 'active' : ''}">
                            <a class="page-link" th:href="@{/product-detail(book_id=${book.bookId}, page=${i}, size=${param.size}, sort=${sortField}, direction=${sortDirection}, minRating=${minRating})}" th:text="${i + 1}">1</a>
                        </li>
                        
                        <!-- Next page -->
                        <li class="page-item" th:classappend="${currentPage + 1 >= totalPages ? 'disabled' : ''}">
                            <a class="page-link" th:href="@{/product-detail(book_id=${book.book_id}, page=${currentPage + 1}, size=${param.size}, sort=${sortField}, direction=${sortDirection}, minRating=${minRating})}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </section>

        <!-- "You may also like" Section -->
        <section class="you-may-also-like-section py-5 border-top">
            <div class="container px-0"> 
                <h3 class="section-title mb-4">You may also like</h3>
                <div class="row">
                    <!-- Hiển thị các sách liên quan từ cơ sở dữ liệu -->
                    <div class="col-lg-3 col-md-4 col-6 mb-4" th:each="relatedBook : ${relatedBooks}" th:if="${relatedBook.book_id != book.book_id}">
                        <div class="book-card">
                            <a th:href="@{/product-detail(book_id=${relatedBook.book_id})}">
                                <img th:src="${relatedBook.coverImgUrl != null ? relatedBook.coverImgUrl : '/images/book-placeholder.jpg'}" 
                                     th:alt="${relatedBook.title}" class="book-cover img-fluid">
                            </a>
                            <div class="book-info">
                                <h6 class="book-title">
                                    <a th:href="@{/product-detail(book_id=${relatedBook.book_id})}" th:text="${relatedBook.title}">Book Title</a>
                                </h6>
                                <!-- Sử dụng fragment book-rating để hiển thị đánh giá sao -->
                                <div th:replace="~{fragments/book-rating :: star-rating(${relatedBook.averageRating})}"></div>
                                <!-- Sử dụng fragment book-price để hiển thị giá -->
                                <div th:replace="~{fragments/book-price :: price-display(${relatedBook.sellingPrice}, ${relatedBook.originalPrice})}"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hiển thị nội dung mặc định nếu không có sách liên quan -->
                    <div class="col-lg-3 col-md-4 col-6 mb-4" th:if="${#lists.isEmpty(relatedBooks)}">
                        <div class="book-card">
                            <a href="#"><img th:src="@{/images/book-romancing-mister-bridgerton.jpg}" alt="Romancing Mister Bridgerton" class="book-cover img-fluid"></a>
                            <div class="book-info">
                                <h6 class="book-title"><a href="#">Romancing Mister Bridgerton</a></h6>
                                <div class="star-rating">
                                    <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star-half-alt"></i>
                                </div>
                                <p class="book-price">$12.35</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div> <!-- /.container -->
</main>

<!-- Image Zoom Modal -->
<div class="modal fade" id="imageZoomModal" tabindex="-1" aria-labelledby="imageZoomModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img th:src="${book.coverImgUrl != null ? book.coverImgUrl : '/images/product-detail-avenir-next.jpg'}"  class="img-fluid" alt="Zoomed Product Image">
            </div>
        </div>
    </div>
</div>


<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle (Popper.js included) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/custom.js}"></script>
    
<!-- JavaScript for review sorting and filtering -->
<script>
    // Function to handle sorting change
    function changeSorting(sortValue) {
        // Split the value into field and direction
        const [field, direction] = sortValue.split(',');
        
        // Update hidden form fields
        document.getElementById('sortField').value = field;
        document.getElementById('sortDirection').value = direction;
        
        // Submit the form
        document.getElementById('reviewFilterForm').submit();
    }
    
    // Initialize quantity control buttons
    document.addEventListener('DOMContentLoaded', function() {
        // Quantity increment/decrement
        const quantityInput = document.getElementById('quantity');
        const decrementBtn = document.querySelector('.quantity-btn.decrement');
        const incrementBtn = document.querySelector('.quantity-btn.increment');
        
        if (quantityInput && decrementBtn && incrementBtn) {
            decrementBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value);
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                }
            });
            
            incrementBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value);
                const maxStock = parseInt(quantityInput.getAttribute('data-max-stock') || '100');
                if (currentValue < maxStock) {
                    quantityInput.value = currentValue + 1;
                }
            });
        }
    });
</script>

<!-- Product Detail JavaScript -->
<script th:inline="javascript">
    // Lấy thông tin sách từ Thymeleaf
    const bookId = /*[[${book.bookId}]]*/ 0;
    const isAuthenticated = /*[[${isAuthenticated}]]*/ false;
    const headerCartCountBadge = window.parent.document.getElementById('headerCartCountBadge');
    
    document.addEventListener('DOMContentLoaded', function() {
        // Lấy các phần tử DOM
        const quantityInput = document.getElementById('quantityInput');
        const decreaseBtn = document.getElementById('decreaseBtn');
        const increaseBtn = document.getElementById('increaseBtn');
        const addToCartBtn = document.querySelector('.add-to-cart-btn');
        const buyNowBtn = document.querySelector('.buy-now-btn');
        
        // Set max quantity from stock
        const maxQuantity = /*[[${book.stockQuantity}]]*/ 10;
        if (quantityInput) {
            quantityInput.setAttribute('data-max-stock', maxQuantity);
        }
        
        // Xử lý nút giảm số lượng
        if (decreaseBtn && quantityInput) {
            decreaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value);
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                }
            });
        }
        
        // Xử lý nút tăng số lượng
        if (increaseBtn && quantityInput) {
            increaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value);
                if (currentValue < maxQuantity) {
                    quantityInput.value = currentValue + 1;
                }
            });
        }
        
        // Hiển thị thông báo kiểu toast
        function showToast(type, message) {
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                // Tạo container cho toast nếu chưa tồn tại
                const container = document.createElement('div');
                container.id = 'toast-container';
                container.style.position = 'fixed';
                container.style.bottom = '20px';
                container.style.right = '20px';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
                toastContainer = container;
            }
            
            const toast = document.createElement('div');
            toast.className = `toast ${type === 'error' ? 'bg-danger' : 'bg-success'} text-white`;
            toast.style.minWidth = '250px';
            toast.innerHTML = `
                <div class="toast-body">
                    ${message}
                </div>
            `;
            
            toastContainer.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 5000
            });
            bsToast.show();
            
            // Xóa toast sau khi ẩn
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }
        
        // Xử lý nút "Buy Now"
        if (buyNowBtn) {
            buyNowBtn.addEventListener('click', function() {
                // Check if button is disabled (out of stock)
                if (this.disabled) {
                    showToast('error', 'This item is currently out of stock');
                    return;
                }

                const isAuthenticated = this.getAttribute('data-auth') === 'true';
                const quantity = quantityInput ? quantityInput.value || 1 : 1;

                // Additional stock check
                if (quantity > maxQuantity) {
                    showToast('error', `Only ${maxQuantity} items available in stock`);
                    return;
                }
                
                if (isAuthenticated) {
                    // Create a form to submit the buy now request
                    const headers = {
                        'Content-Type': 'application/json'
                    };
                    
                    const csrfToken = getCsrfToken();
                    if (csrfToken) {
                        headers[getCsrfHeader()] = csrfToken;
                    }
                    
                    fetch('/buyer/cart/buy-now', {
                        method: 'POST',
                        headers: headers,
                        body: JSON.stringify({
                            bookId: bookId,
                            quantity: quantity,
                            buyNow: true
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Redirect to checkout page
                            window.location.href = data.redirectUrl;
                        } else {
                            showToast('error', data.message || 'Có lỗi xảy ra khi xử lý yêu cầu');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('error', 'Có lỗi xảy ra khi xử lý yêu cầu');
                    });
                } else {
                    // Nếu chưa đăng nhập, chuyển đến trang đăng nhập với returnUrl
                    window.location.href = `/buyer/login?returnUrl=/product-detail?book_id=${bookId}`;
                }
            });
        }
        
        // CSRF setup
        function getCsrfToken() {
            const csrfTokenMeta = document.querySelector('meta[name="_csrf"]');
            if (csrfTokenMeta) {
                return csrfTokenMeta.getAttribute('content');
            }
            return null;
        }
        
        function getCsrfHeader() {
            const headerMeta = document.querySelector('meta[name="_csrf_header"]');
            if (headerMeta) {
                return headerMeta.getAttribute('content');
            }
            return 'X-CSRF-TOKEN';
        }
        
        // Function to update the cart count in the header
        function updateHeaderCartCount() {
            // Use global cart update function if available, otherwise fallback to local implementation
            if (window.updateGlobalCartCount) {
                window.updateGlobalCartCount();
                return;
            }

            // Fallback implementation
            fetch('/buyer/cart/total-quantity', {
                method: 'GET',
                headers: getCsrfHeaders() // Use the CSRF headers for the GET request as well
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const uniqueItemCount = data.uniqueItemCount;
                    if (headerCartCountBadge) {
                        headerCartCountBadge.textContent = uniqueItemCount;
                        headerCartCountBadge.style.display = uniqueItemCount > 0 ? '' : 'none';
                    }

                    // Trigger global cart update event
                    if (window.dispatchEvent) {
                        window.dispatchEvent(new CustomEvent('cartUpdated', {
                            detail: { uniqueItemCount: uniqueItemCount }
                        }));
                    }
                } else {
                    console.error('Failed to get cart unique item count:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching total cart quantity:', error);
            });
        }

        // Helper to get CSRF headers for fetch requests
        function getCsrfHeaders() {
            const headers = {};
            const csrfToken = getCsrfToken();
            if (csrfToken) {
                headers[getCsrfHeader()] = csrfToken;
            }
            return headers;
        }

        // Call updateHeaderCartCount on page load to ensure the initial count is correct
        if (isAuthenticated) {
            updateHeaderCartCount();
        }
        
        // Xử lý nút "Add to Cart"
        if (addToCartBtn) {
            addToCartBtn.addEventListener('click', function() {
                // Check if button is disabled (out of stock)
                if (this.disabled) {
                    showToast('error', 'This item is currently out of stock');
                    return;
                }

                const isAuthenticated = this.getAttribute('data-auth') === 'true';
                const quantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;

                // Additional stock check
                if (quantity > maxQuantity) {
                    showToast('error', `Only ${maxQuantity} items available in stock`);
                    return;
                }
                
                if (isAuthenticated) {
                    // Nếu đã đăng nhập, thêm vào giỏ hàng bằng Ajax
                    const headers = {
                        'Content-Type': 'application/json'
                    };
                    
                    const csrfToken = getCsrfToken();
                    if (csrfToken) {
                        headers[getCsrfHeader()] = csrfToken;
                    }
                    
                    fetch('/buyer/cart/add', {
                        method: 'POST',
                        headers: headers,
                        body: JSON.stringify({
                            bookId: bookId,
                            quantity: quantity
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showToast('success', data.message || 'Thêm vào giỏ hàng thành công!');
                            // Update the cart count badge in the header
                            updateHeaderCartCount();

                            // Also trigger global cart update
                            if (window.updateGlobalCartCount) {
                                window.updateGlobalCartCount();
                            }
                        } else {
                            showToast('error', data.message || 'Không thể thêm vào giỏ hàng');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast('error', 'Có lỗi xảy ra khi thêm vào giỏ hàng');
                    });
                } else {
                    // Nếu chưa đăng nhập, chuyển đến trang đăng nhập với returnUrl
                    window.location.href = `/buyer/login?returnUrl=/product-detail?book_id=${bookId}`;
                }
            });
        }
        
        // Ensure input doesn't exceed min/max
        if (quantityInput) {
            quantityInput.addEventListener('change', function() {
                const value = parseInt(this.value);
                if (isNaN(value) || value < 1) {
                    this.value = 1;
                } else if (value > maxQuantity) {
                    this.value = maxQuantity;
                }
            });
        }
    });
</script>

</body>
</html>