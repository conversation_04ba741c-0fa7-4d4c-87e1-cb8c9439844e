<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Page-specific styles for product details */
        .content-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .btn-icon {
            padding: 0.375rem 0.75rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-link {
            color: #7f8c8d;
            transition: color 0.2s;
        }

        .back-link:hover {
            color: #2C3E50;
        }

        .page-header {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
        }

        .product-image {
            width: 100%;
            max-height: 500px;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }

        .product-title {
            font-family: 'Lora', serif;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .product-author {
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 1rem;
        }

        .product-price {
            font-size: 1.75rem;
            font-weight: 700;
            color: #e74c3c;
            margin-bottom: 0.5rem;
        }

        .product-original-price {
            text-decoration: line-through;
            color: #95a5a6;
            font-size: 1.1rem;
        }

        .badge-large {
            font-size: 1rem;
            padding: 0.5rem 1rem;
        }

        .product-details {
            margin-top: 2rem;
        }

        .detail-label {
            font-weight: 600;
            color: #7f8c8d;
        }

        .detail-value {
            font-weight: 400;
        }

        .detail-row {
            margin-bottom: 1rem;
        }

        .category-badge {
            background-color: #3498db;
            color: white;
            padding: 0.35rem 0.65rem;
            border-radius: 4px;
            font-size: 0.875rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            display: inline-block;
        }

        .stock-badge {
            color: white;
            padding: 0.35rem 0.65rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 600;
            background-color: #3498db;
        }

        .low-stock {
            background-color: #f39c12;
        }

        .out-of-stock {
            background-color: #e74c3c;
        }

        .product-description {
            white-space: pre-line;
            line-height: 1.6;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .header-actions {
                margin-top: 1rem;
                width: 100%;
                justify-content: space-between;
            }
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1050;
            width: 350px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease-out;
            transform: translateX(400px);
        }

        .notification.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
<!-- Include Topbar -->
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<!-- Sticky notification for form feedback -->
<div id="notificationContainer"></div>

<div class="container my-4">
    <div class="row">
        <!-- Include Sidebar -->
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9">
            <!-- Success message -->
            <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- Error message -->
            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <div class="content-container">
                <div class="page-header">
                    <div>
                        <a th:href="@{/seller/products}" class="back-link text-decoration-none"><i class="fas fa-arrow-left"></i> Back to Products</a>
                        <h1 class="product-title mt-2" th:text="${book.title}">Product Title</h1>
                        <div class="product-author" th:text="${book.authors}">Author Name</div>
                    </div>
                    <div class="header-actions">
                        <a th:href="@{'/seller/products/' + ${book.bookId} + '/edit'}" class="btn btn-outline-primary btn-icon">
                            <i class="fas fa-edit"></i> Edit Product
                        </a>
                        <button type="button" class="btn btn-outline-danger btn-icon" data-bs-toggle="modal" data-bs-target="#deleteProductModal">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>

                <div class="row">
                    <!-- Left column: Product image -->
                    <div class="col-lg-5 mb-4">
                        <img th:if="${book.coverImgUrl}" th:src="${book.coverImgUrl}" 
                             class="product-image" th:alt="${book.title}" />
                        <img th:unless="${book.coverImgUrl}" src="/img/book-placeholder.jpg" 
                             class="product-image" alt="Book cover placeholder" />
                    </div>

                    <!-- Right column: Product details -->
                    <div class="col-lg-7">
                        <!-- Pricing and stock information -->
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="product-price" th:text="${#numbers.formatDecimal(book.sellingPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'">
                                    80,000 VND
                                </div>
                                <div th:if="${book.originalPrice != book.sellingPrice}" class="product-original-price mb-3">
                                    <span th:text="${#numbers.formatDecimal(book.originalPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'">
                                        100,000 VND
                                    </span>
                                    <span class="ms-2">
                                        (<span th:text="${#numbers.formatPercent((1 - book.sellingPrice.doubleValue() / book.originalPrice.doubleValue()), 0, 0)}">
                                            -20%
                                        </span> discount)
                                    </span>
                                </div>
                            </div>
                            <div>
                                <span th:if="${book.stockQuantity > 5}" class="stock-badge">
                                    <i class="fas fa-check-circle me-1"></i> In Stock: <span th:text="${book.stockQuantity}">10</span>
                                </span>
                                <span th:if="${book.stockQuantity > 0 && book.stockQuantity <= 5}" class="stock-badge low-stock">
                                    <i class="fas fa-exclamation-triangle me-1"></i> Low Stock: <span th:text="${book.stockQuantity}">3</span>
                                </span>
                                <span th:if="${book.stockQuantity == 0}" class="stock-badge out-of-stock">
                                    <i class="fas fa-times-circle me-1"></i> Out of Stock
                                </span>
                            </div>
                        </div>

                        <!-- Categories -->
                        <div class="mb-3">
                            <div class="detail-label mb-2">Categories</div>
                            <div th:if="${book.categories != null && !book.categories.isEmpty()}">
                                <span th:each="category : ${book.categories}" class="category-badge" th:text="${category.categoryName}">
                                    Fiction
                                </span>
                            </div>
                            <div th:if="${book.categories == null || book.categories.isEmpty()}" class="text-muted">
                                No categories assigned
                            </div>
                        </div>

                        <!-- Key details -->
                        <div class="product-details">
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">Publisher</div>
                                <div class="col-sm-8 detail-value" th:text="${book.publisher != null ? book.publisher.publisherName : 'Not specified'}">
                                    Publisher Name
                                </div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">Publication Date</div>
                                <div class="col-sm-8 detail-value" th:text="${book.publicationDate != null ? #temporals.format(book.publicationDate, 'dd MMMM yyyy') : 'Not specified'}">
                                    01 January 2023
                                </div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">ISBN</div>
                                <div class="col-sm-8 detail-value" th:text="${book.isbn != null ? book.isbn : 'Not specified'}">
                                    978-0-123456-78-9
                                </div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">Pages</div>
                                <div class="col-sm-8 detail-value" th:text="${book.numberOfPages != null ? book.numberOfPages : 'Not specified'}">
                                    300
                                </div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">Dimensions</div>
                                <div class="col-sm-8 detail-value" th:text="${book.dimensions != null ? book.dimensions + ' cm' : 'Not specified'}">
                                    15x23x2.5 cm
                                </div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">SKU</div>
                                <div class="col-sm-8 detail-value" th:text="${book.sku != null ? book.sku : 'Not specified'}">
                                    BK1234567
                                </div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">Date Added</div>
                                <div class="col-sm-8 detail-value" th:text="${book.dateAdded != null ? #temporals.format(book.dateAdded, 'dd MMMM yyyy') : 'Not recorded'}">
                                    01 January 2023
                                </div>
                            </div>
                            <div class="row detail-row">
                                <div class="col-sm-4 detail-label">Status</div>
                                <div class="col-sm-8 detail-value">
                                    <span th:if="${book.active}" class="badge bg-success">Active</span>
                                    <span th:unless="${book.active}" class="badge bg-secondary">Inactive</span>
                                </div>
                            </div>
                            <div th:if="${book.averageRating != null}" class="row detail-row">
                                <div class="col-sm-4 detail-label">Rating</div>
                                <div class="col-sm-8 detail-value">
                                    <span th:text="${#numbers.formatDecimal(book.averageRating, 1, 1)}">4.5</span>/5
                                    <small class="ms-2 text-muted" th:text="'(' + ${book.totalReviews} + ' reviews)'">
                                        (10 reviews)
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product description -->
                <div class="mt-4">
                    <h2 class="section-title">Description</h2>
                    <div class="product-description" th:utext="${book.description}">
                        Product description goes here...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Delete Product Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1" aria-labelledby="deleteProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProductModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this product?</p>
                <p><strong th:text="${book.title}">Product Name</strong></p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form th:action="@{'/seller/products/' + ${book.bookId} + '/delete'}" method="post">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script>
    // Show notifications on page load if messages exist
    document.addEventListener('DOMContentLoaded', function() {
        const successMessage = document.querySelector('.alert-success');
        const errorMessage = document.querySelector('.alert-danger');

        if (successMessage) {
            showNotification(successMessage.querySelector('span').textContent, 'success');
            successMessage.remove(); // Remove original message
        }

        if (errorMessage) {
            showNotification(errorMessage.querySelector('span').textContent, 'danger');
            errorMessage.remove(); // Remove original message
        }
    });
    
    // Show notification
    function showNotification(message, type) {
        const container = document.getElementById('notificationContainer');

        // Create notification
        const notification = document.createElement('div');
        notification.className = `notification alert alert-${type}`;
        notification.innerHTML = `
            <div class="d-flex">
                <div class="me-3">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} fa-2x"></i>
                </div>
                <div>
                    <h5>${type === 'success' ? 'Success!' : 'Error!'}</h5>
                    <p class="mb-0">${message}</p>
                </div>
                <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Add to container
        container.appendChild(notification);

        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
</script>
</body>
</html> 