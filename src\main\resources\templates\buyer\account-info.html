<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Information - READHUB</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="account-page py-5 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Account Information</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Account Sidebar using fragment -->
            <div th:replace="~{fragments/buyer-account-sidebar :: sidebar('info')}"></div>

            <!-- Account Content -->
            <section class="col-lg-9 account-content">
                <!-- Success message display -->
                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show mb-3" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Error message display -->
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show mb-3" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Info message display -->
                <div th:if="${infoMessage}" class="alert alert-info alert-dismissible fade show mb-3" role="alert">
                    <i class="fas fa-info-circle me-2"></i> <span th:text="${infoMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div class="card">
                    <div class="card-header bg-white py-3">
                        <h4 class="mb-0">Account Information</h4>
                    </div>
                    <div class="card-body p-4">
                        <div>
                            <!-- Personal Information -->
                            <h5 class="mb-3 section-form-title">Personal Information</h5>
                            <div class="row mb-4 align-items-center">
                                <div class="col-md-3 text-center text-md-start mb-3 mb-md-0">
                                    <div class="position-relative d-inline-block">
                                        <img th:src="${user.profilePicUrl != null ? user.profilePicUrl : '/images/avatar-placeholder.png'}" alt="User Avatar" class="rounded-circle border" width="100" height="100" id="avatarPreview">
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="mb-3">
                                        <strong>Full Name:</strong> <span th:text="${user.fullName}">Full Name</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>Email:</strong> <span th:text="${user.email}">Email</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>Phone Number:</strong> <span th:text="${user.phone}">Phone Number</span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <strong>Date of Birth:</strong> <span th:text="${#temporals.format(user.dateOfBirth, 'yyyy-MM-dd')}">Date of Birth</span>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <strong>Gender:</strong> 
                                    <span th:if="${user.gender == 0}">Male</span>
                                    <span th:if="${user.gender == 1}">Female</span>
                                    <span th:if="${user.gender == 2}">Other</span>
                                </div>
                            </div>
                            <a th:href="@{/buyer/edit-info}" class="btn btn-primary">Modify Profile Information</a>
                            <hr class="my-4">

                            <!-- Contact Information -->
                            <h5 class="mb-3 section-form-title">Contact Information</h5>
                            <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                <div>
                                    <p class="mb-0 fw-medium">Email Address</p>
                                    <p class="mb-0 text-muted small" th:text="${user.email}">Email address</p>
                                </div>
                            </div>
                            <hr class="my-4">

                            <!-- Security -->
                            <h5 class="mb-3 section-form-title">Security</h5>
                            <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                <div>
                                    <p class="mb-0 fw-medium">Change Password</p>
                                </div>
                                <a th:href="@{/buyer/change-password}" class="btn btn-outline-primary btn-sm">Update</a>
                            </div>

                                <div class="d-flex justify-content-between align-items-center mb-3 p-3 border rounded">
                                    <div>
                                        <p class="mb-0 fw-medium">Request Account Deletion</p>
                                    </div>
                                    <a th:href="@{/buyer/account-deletion}" class="btn btn-outline-danger btn-sm">Request</a>
                                </div>
                                <hr class="my-4">



                            <!-- Social Links -->

                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>


<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script th:src="@{/js/script.js}"></script>
<!-- Initialize Bootstrap components -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        dropdownElementList.forEach(function(dropdownToggleEl) {
            new bootstrap.Dropdown(dropdownToggleEl);
        });
    });
</script>



</body>
</html>