<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title layout:title-pattern="$CONTENT_TITLE - ReadHub">ReadHub</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    
    <!-- Additional head content -->
    <th:block layout:fragment="head"></th:block>
</head>
<body>
    <!-- Header -->
    <div th:replace="~{fragments/header :: header-content}"></div>
    
    <!-- Main Content -->
    <main layout:fragment="content">
        <!-- Page content will be inserted here -->
    </main>
    
    <!-- Footer -->
    <div th:replace="~{fragments/footer :: footer-content}"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JavaScript -->
    <script th:src="@{/js/scripts.js}"></script>
    
    <!-- Additional scripts -->
    <th:block layout:fragment="scripts"></th:block>

    <!-- Chat Widget - Buyers Only -->
    <div th:replace="~{fragments/chat-widget :: buyer-chat-widget}"></div>
</body>
</html>
