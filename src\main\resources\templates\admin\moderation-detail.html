<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moderation Detail - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
    </style>
</head>
<body>
<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>
<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar(activeMenu='moderation')}"></div>
        </div>
        <div class="col-lg-9">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title" th:text="${contentType == 'review'} ? 'Review Detail' : 'Comment Detail'"></h3>
                    <a th:href="${contentType == 'review' ? '/admin/product-reviews' : '/admin/blog-comments'}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>

                <div th:if="${contentType == 'review'}" class="card">
                    <div class="card-body">
                        <h5 class="card-title" th:text="${review.title}"></h5>
                        <div class="mb-3">
                            <span th:each="i : ${#numbers.sequence(1, review.rating)}" class="text-warning"><i class="fas fa-star"></i></span>
                        </div>
                        <p class="card-text" style="white-space: pre-wrap;" th:text="${review.content}"></p>
                        <hr>
                        <p class="mb-1"><strong>User:</strong> <span th:text="${review.user.fullName}"></span></p>
                        <p class="mb-1"><strong>Book:</strong> <span th:text="${review.orderItem?.book?.title ?: 'N/A'}"></span></p>
                        <p class="mb-1"><strong>Date:</strong> <span th:text="${#temporals.format(review.createdDate, 'dd-MM-yyyy HH:mm')}"></span></p>
                        <p class="mb-3"><strong>Status:</strong> <span class="badge" th:classappend="${review.isApproved} ? 'bg-success' : 'bg-warning'" th:text="${review.isApproved} ? 'Approved' : 'Pending'"></span></p>

                        <div class="actions">
                            <form th:if="${!review.isApproved}" th:action="@{/admin/reviews/approve/{id}(id=${review.reviewId})}" method="post" class="d-inline me-2">
                                <button type="submit" class="btn btn-success"><i class="fas fa-check me-2"></i>Approve</button>
                            </form>
                            <form th:action="@{/admin/reviews/delete/{id}(id=${review.reviewId})}" method="post" onsubmit="return confirm('Delete this review?')" class="d-inline">
                                <button type="submit" class="btn btn-danger"><i class="fas fa-trash me-2"></i>Delete</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div th:if="${contentType == 'comment'}" class="card">
                    <div class="card-body">
                        <p class="card-text" style="white-space: pre-wrap;" th:text="${comment.content}"></p>
                        <hr>
                        <p class="mb-1"><strong>User:</strong> <span th:text="${comment.user.fullName}"></span></p>
                        <p class="mb-1"><strong>Blog Post:</strong> <span th:text="${comment.blogPost?.title ?: 'N/A'}"></span></p>
                        <p class="mb-1"><strong>Date:</strong> <span th:text="${#temporals.format(comment.createdDate, 'dd-MM-yyyy HH:mm')}"></span></p>
                        <p class="mb-3"><strong>Status:</strong> <span class="badge" th:classappend="${comment.isApproved} ? 'bg-success' : 'bg-warning'" th:text="${comment.isApproved} ? 'Approved' : 'Pending'"></span></p>

                        <div class="actions">
                            <form th:if="${!comment.isApproved}" th:action="@{/admin/comments/approve/{id}(id=${comment.commentId})}" method="post" class="d-inline me-2">
                                <button type="submit" class="btn btn-success"><i class="fas fa-check me-2"></i>Approve</button>
                            </form>
                            <form th:action="@{/admin/comments/delete/{id}(id=${comment.commentId})}" method="post" onsubmit="return confirm('Delete this comment?')" class="d-inline">
                                <button type="submit" class="btn btn-danger"><i class="fas fa-trash me-2"></i>Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>