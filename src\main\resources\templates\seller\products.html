<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Products - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Page-specific styles for products */
        .content-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .product-card {
            height: 100%;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            border: 1px solid #e6e6e6;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .product-card-img-container {
            aspect-ratio: 2/3;
            overflow: hidden;
        }

        .product-card-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .product-card:hover .product-card-img {
            transform: scale(1.05);
        }

        .product-card-body {
            padding: 1rem;
        }

        .product-title {
            font-weight: 600;
            color: #2C3E50;
            margin-bottom: 0.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            height: 3rem;
        }

        .product-price {
            font-weight: 700;
            color: #e74c3c;
        }

        .product-original-price {
            text-decoration: line-through;
            color: #95a5a6;
            font-size: 0.9rem;
        }

        .product-discount-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #e74c3c;
            color: white;
            font-weight: 700;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .stock-badge {
            background-color: #3498db;
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            margin-left: 0.5rem;
        }

        .low-stock {
            background-color: #f39c12;
        }

        .out-of-stock {
            background-color: #e74c3c;
        }

        .product-actions {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(44, 62, 80, 0.9);
            padding: 0.5rem;
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.3s;
            display: flex;
            justify-content: space-around;
        }

        .product-card:hover .product-actions {
            opacity: 1;
            transform: translateY(0);
        }

        .action-btn {
            color: white;
            border: none;
            background: transparent;
            padding: 0.4rem 0.6rem;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .action-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Table view styles */
        .product-table img {
            width: 60px;
            height: 90px;
            object-fit: cover;
            border-radius: 4px;
        }

        .product-table .title-col {
            max-width: 300px;
        }

        .btn-icon {
            padding: 0.375rem 0.75rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Search and filter section */
        .search-filter-container {
            background-color: #fff;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }

        .view-toggle-btn {
            color: #7f8c8d;
            background-color: transparent;
            border: none;
            padding: 0.5rem;
            font-size: 1.2rem;
            cursor: pointer;
        }

        .view-toggle-btn.active {
            color: #2C3E50;
        }

        /* Sort controls styling */
        .sort-controls .btn-outline-secondary {
            border-color: #ced4da;
            color: #6c757d;
        }

        .sort-controls .btn-outline-secondary:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
            color: #495057;
        }

        .sort-controls .dropdown-toggle-split {
            border-left: 0;
        }

        .sort-controls .dropdown-menu {
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .sort-controls .dropdown-item {
            padding: 0.5rem 1rem;
            color: #495057;
        }

        .sort-controls .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #2C3E50;
        }

        .sort-controls .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        /* Sort direction badge */
        .sort-controls .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.4rem;
            background-color: #6c757d;
            border: none;
        }

        .sort-controls .badge:hover {
            background-color: #5a6268;
        }

        /* Sort direction indicator */
        .sort-controls .btn-outline-secondary i {
            transition: transform 0.2s ease;
        }

        .sort-controls .btn-outline-secondary:hover i {
            transform: scale(1.1);
        }

        /* Active sort indicator */
        .sort-controls .btn-outline-secondary.active {
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: white;
        }

        .sort-controls .btn-outline-secondary.active:hover {
            background-color: #1e2b37;
            border-color: #1a252f;
        }

        /* Pagination */
        .pagination .page-link {
            color: #2C3E50;
        }

        .pagination .page-item.active .page-link {
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: white;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            z-index: 1050;
            width: 350px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease-out;
            transform: translateX(400px);
        }

        .notification.show {
            transform: translateX(0);
        }

        /* Responsive */
        @media (max-width: 992px) {
            .product-table .title-col {
                max-width: 200px;
            }
            .hide-md {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .product-table .hide-sm {
                display: none;
            }
        }
    </style>
</head>
<body>
<!-- Include Topbar -->
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<!-- Sticky notification for form feedback -->
<div id="notificationContainer"></div>

<div class="container my-4">
    <div class="row">
        <!-- Include Sidebar -->
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9">
            <!-- Success message -->
            <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- Error message -->
            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- Search and filter section -->
            <div class="search-filter-container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h2 class="mb-0">My Products</h2>
                    </div>
                    <div class="col-md-6 text-md-end mt-3 mt-md-0">
                        <a th:href="@{/seller/products/add}" class="btn btn-primary btn-icon">
                            <i class="fas fa-plus"></i> Add New Product
                        </a>
                    </div>
                </div>

                <hr class="my-3">

                <form th:action="@{/seller/products}" method="get" class="row g-3" id="productSearchForm" onsubmit="handleSearch(event)">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" name="searchQuery" placeholder="Search products..." 
                                   th:value="${searchQuery}" aria-label="Search products" id="searchInput">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group sort-controls">
                            <label class="input-group-text" for="sortField">Sort by</label>
                            <select class="form-select" id="sortField" name="sortField" onchange="setSortField(this.value)">
                                <option value="dateAdded" th:selected="${sortField == 'dateAdded'}">Date Added</option>
                                <option value="title" th:selected="${sortField == 'title'}">Title</option>
                                <option value="sellingPrice" th:selected="${sortField == 'sellingPrice'}">Price</option>
                                <option value="stockQuantity" th:selected="${sortField == 'stockQuantity'}">Stock</option>
                            </select>
<!--                            <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split" -->
<!--                                    data-bs-toggle="dropdown" aria-expanded="false">-->
<!--                                <span class="visually-hidden">Toggle Dropdown</span>-->
<!--                            </button>-->
<!--                            <ul class="dropdown-menu dropdown-menu-end">-->
<!--                                <li>-->
<!--                                    <a class="dropdown-item" href="#" onclick="setSortDirection('asc'); return false;">-->
<!--                                        <i class="fas fa-sort-amount-up me-2"></i>Ascending-->
<!--                                    </a>-->
<!--                                </li>-->
<!--                                <li>-->
<!--                                    <a class="dropdown-item" href="#" onclick="setSortDirection('desc'); return false;">-->
<!--                                        <i class="fas fa-sort-amount-down me-2"></i>Descending-->
<!--                                    </a>-->
<!--                                </li>-->
<!--                            </ul>-->
                            <input type="hidden" name="sortDir" th:value="${sortDir}" id="sortDirInput">
                            <button type="button" class="btn btn-outline-secondary" onclick="toggleSortDirection()" title="Toggle sort direction">
                                <i class="fas" th:class="${sortDir == 'asc'} ? 'fa-sort-amount-up' : 'fa-sort-amount-down'"></i>
                                <span class="badge bg-secondary ms-1" th:text="${sortDir == 'asc'} ? 'A-Z' : 'Z-A'">Z-A</span>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex justify-content-end">
                            <button type="button" class="view-toggle-btn active me-2" data-view="grid">
                                <i class="fas fa-th"></i>
                            </button>
                            <button type="button" class="view-toggle-btn" data-view="table">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Product count and pagination info -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <p class="mb-0">
                        <span th:text="${bookPage.totalElements}">0</span> products found
                        <span th:if="${searchQuery != ''}" class="ms-2">
                            for '<span th:text="${searchQuery}"></span>'
                            <a th:href="@{/seller/products}" class="ms-1 text-decoration-none">
                                <i class="fas fa-times-circle"></i> Clear
                            </a>
                        </span>
                    </p>
                </div>
                <div th:if="${bookPage.totalPages > 0}">
                    <span th:text="${bookPage.number * bookPage.size + 1}"></span> - 
                    <span th:text="${bookPage.number * bookPage.size + bookPage.numberOfElements}"></span> of 
                    <span th:text="${bookPage.totalElements}"></span>
                </div>
            </div>

            <!-- Grid view -->
            <div class="product-grid-view">
                <!-- No products message -->
                <div th:if="${bookPage.totalElements == 0}" class="content-container text-center py-5">
                    <i class="fas fa-book fa-4x text-muted mb-3"></i>
                    <h3>No products found</h3>
                    <p class="text-muted mb-4">Start adding your first product to your shop inventory!</p>
                    <a th:href="@{/seller/products/add}" class="btn btn-primary btn-icon">
                        <i class="fas fa-plus"></i> Add New Product
                    </a>
                </div>
                
                <div th:if="${bookPage.totalElements > 0}" class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4">
                    <div th:each="book : ${bookPage.content}" class="col">
                        <div class="product-card position-relative">
                            <!-- Discount badge -->
                            <div th:if="${book.originalPrice != book.sellingPrice}" class="product-discount-badge">
                                <span th:text="${#numbers.formatPercent(
                                    (1 - book.sellingPrice.doubleValue() / book.originalPrice.doubleValue()), 0, 0)}">
                                    -20%
                                </span>
                            </div>
                            
                            <!-- Product image -->
                            <div class="product-card-img-container">
                                <img th:if="${book.coverImgUrl}" th:src="${book.coverImgUrl}" 
                                     class="product-card-img" th:alt="${book.title}" />
                                <img th:unless="${book.coverImgUrl}" src="/img/book-placeholder.jpg" 
                                     class="product-card-img" alt="Book cover placeholder" />
                            </div>
                            
                            <!-- Product details -->
                            <div class="product-card-body">
                                <h5 class="product-title" th:text="${book.title}">Book Title</h5>
                                <p class="mb-1">
                                    <span class="product-price" th:text="${#numbers.formatDecimal(book.sellingPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'">
                                        80,000 VND
                                    </span>
                                    <span th:if="${book.originalPrice != book.sellingPrice}" class="product-original-price ms-2" 
                                          th:text="${#numbers.formatDecimal(book.originalPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'">
                                        100,000 VND
                                    </span>
                                </p>
                                <p class="mb-1 d-flex justify-content-between align-items-center">
                                    <span class="text-muted small">Stock:</span>
                                    <span>
                                        <span th:text="${book.stockQuantity}"></span>
                                        <span th:if="${book.stockQuantity == 0}" class="stock-badge out-of-stock">Out of stock</span>
                                        <span th:if="${book.stockQuantity > 0 && book.stockQuantity <= 5}" class="stock-badge low-stock">Low stock</span>
                                    </span>
                                </p>
                                <p class="mb-0 small text-muted">
                                    Added: <span th:text="${#temporals.format(book.dateAdded, 'dd MMM yyyy')}">01 Jan 2023</span>
                                </p>
                            </div>
                            
                            <!-- Hover actions -->
                            <div class="product-actions">
                                <a th:href="@{'/seller/products/' + ${book.bookId}}" class="action-btn" title="View details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a th:href="@{'/seller/products/' + ${book.bookId} + '/edit'}" class="action-btn" title="Edit product">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="action-btn" title="Deactive product"
                                        data-bs-toggle="modal" data-bs-target="#deleteProductModal"
                                        th:data-book-id="${book.bookId}" th:data-book-title="${book.title}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table view (initially hidden) -->
            <div class="product-table-view d-none">
                <div class="content-container">
                    <div class="table-responsive">
                        <table class="table product-table">
                            <thead>
                                <tr>
                                    <th>Image</th>
                                    <th>Title</th>
                                    <th class="hide-sm">Price</th>
                                    <th class="hide-md">Stock</th>
                                    <th class="hide-md">Added</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:each="book : ${bookPage.content}">
                                    <td>
                                        <img th:if="${book.coverImgUrl}" th:src="${book.coverImgUrl}" th:alt="${book.title}" />
                                        <img th:unless="${book.coverImgUrl}" src="/img/book-placeholder.jpg" alt="Book cover placeholder" />
                                    </td>
                                    <td class="title-col">
                                        <strong th:text="${book.title}">Book Title</strong>
                                        <p class="mb-0 small text-muted d-block d-md-none">
                                            <span th:text="${#numbers.formatDecimal(book.sellingPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'"></span>
                                            <span th:if="${book.stockQuantity == 0}" class="stock-badge out-of-stock">Out of stock</span>
                                            <span th:if="${book.stockQuantity > 0 && book.stockQuantity <= 5}" class="stock-badge low-stock">Low stock</span>
                                        </p>
                                    </td>
                                    <td class="hide-sm">
                                        <span th:text="${#numbers.formatDecimal(book.sellingPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'">
                                            80,000 VND
                                        </span>
                                        <small th:if="${book.originalPrice != book.sellingPrice}" class="product-original-price d-block" 
                                              th:text="${#numbers.formatDecimal(book.originalPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'">
                                            100,000 VND
                                        </small>
                                    </td>
                                    <td class="hide-md">
                                        <span th:text="${book.stockQuantity}"></span>
                                        <span th:if="${book.stockQuantity == 0}" class="stock-badge out-of-stock">Out of stock</span>
                                        <span th:if="${book.stockQuantity > 0 && book.stockQuantity <= 5}" class="stock-badge low-stock">Low stock</span>
                                    </td>
                                    <td class="hide-md">
                                        <span th:text="${#temporals.format(book.dateAdded, 'dd MMM yyyy')}">01 Jan 2023</span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a th:href="@{'/seller/products/' + ${book.bookId}}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a th:href="@{'/seller/products/' + ${book.bookId} + '/edit'}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="modal" data-bs-target="#deleteProductModal"
                                                    th:data-book-id="${book.bookId}" th:data-book-title="${book.title}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <nav th:if="${bookPage.totalPages > 1}" class="mt-4" aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <li class="page-item" th:classappend="${bookPage.number == 0} ? 'disabled' : ''">
                        <a class="page-link" th:href="@{/seller/products(page=0, size=${bookPage.size}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}"
                           aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${bookPage.number == 0} ? 'disabled' : ''">
                        <a class="page-link" th:href="@{/seller/products(page=${bookPage.number - 1}, size=${bookPage.size}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}"
                           aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:each="i : ${#numbers.sequence(0, bookPage.totalPages - 1)}"
                        th:if="${i &gt;= bookPage.number - 2 and i &lt;= bookPage.number + 2}"
                        th:classappend="${i == bookPage.number} ? 'active' : ''">
                        <a class="page-link" th:href="@{/seller/products(page=${i}, size=${bookPage.size}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}">
                            <span th:text="${i + 1}">1</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${bookPage.number == bookPage.totalPages - 1} ? 'disabled' : ''">
                        <a class="page-link" th:href="@{/seller/products(page=${bookPage.number + 1}, size=${bookPage.size}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}"
                           aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item" th:classappend="${bookPage.number == bookPage.totalPages - 1} ? 'disabled' : ''">
                        <a class="page-link" th:href="@{/seller/products(page=${bookPage.totalPages - 1}, size=${bookPage.size}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}"
                           aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Delete Product Modal -->
<div class="modal fade" id="deleteProductModal" tabindex="-1" aria-labelledby="deleteProductModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteProductModalLabel">Confirm Deactive</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to deactive this product?</p>
                <p><strong id="deleteProductName"></strong></p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteProductForm" method="post">
                    <button type="submit" class="btn btn-danger">Deactive</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script>
    // Toggle between grid and table view
    document.querySelectorAll('.view-toggle-btn').forEach(button => {
        button.addEventListener('click', function() {
            // Update active class on buttons
            document.querySelectorAll('.view-toggle-btn').forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Show/hide appropriate view
            const view = this.getAttribute('data-view');
            if (view === 'grid') {
                document.querySelector('.product-grid-view').classList.remove('d-none');
                document.querySelector('.product-table-view').classList.add('d-none');
            } else {
                document.querySelector('.product-grid-view').classList.add('d-none');
                document.querySelector('.product-table-view').classList.remove('d-none');
            }
        });
    });
    
    // Set up delete product modal
    const deleteProductModal = document.getElementById('deleteProductModal');
    if (deleteProductModal) {
        deleteProductModal.addEventListener('show.bs.modal', function (event) {
            // Button that triggered the modal
            const button = event.relatedTarget;
            
            // Extract info from data attributes
            const bookId = button.getAttribute('data-book-id');
            const bookTitle = button.getAttribute('data-book-title');
            
            // Update the modal
            const productNameElement = document.getElementById('deleteProductName');
            productNameElement.textContent = bookTitle;
            
            // Set form action URL
            const deleteForm = document.getElementById('deleteProductForm');
            deleteForm.action = `/seller/products/${bookId}/delete`;
        });
    }
    
    // Toggle sort direction
    function toggleSortDirection() {
        const currentDirection = document.getElementById('sortDirInput').value;
        const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
        
        // Build URL with current parameters
        const url = buildProductsUrl(newDirection);
        
        // Navigate to the new URL
        window.location.href = url;
    }
    
    // Set sort direction
    function setSortDirection(direction) {
        // Build URL with current parameters
        const url = buildProductsUrl(direction);
        
        // Navigate to the new URL
        window.location.href = url;
    }
    
    // Build products URL with current parameters
    function buildProductsUrl(sortDir) {
        const urlParams = new URLSearchParams(window.location.search);
        const searchQuery = urlParams.get('searchQuery') || '';
        const sortField = urlParams.get('sortField') || 'dateAdded';
        const page = urlParams.get('page') || '0';
        const size = urlParams.get('size') || '9';
        
        let url = '/seller/products?';
        url += `page=${page}&size=${size}&sortField=${sortField}&sortDir=${sortDir}`;
        
        if (searchQuery) {
            url += `&searchQuery=${encodeURIComponent(searchQuery)}`;
        }
        
        return url;
    }
    
    // Set sort field
    function setSortField(sortField) {
        const urlParams = new URLSearchParams(window.location.search);
        const searchQuery = urlParams.get('searchQuery') || '';
        const sortDir = urlParams.get('sortDir') || 'desc';
        const page = urlParams.get('page') || '0';
        const size = urlParams.get('size') || '9';
        
        let url = '/seller/products?';
        url += `page=${page}&size=${size}&sortField=${sortField}&sortDir=${sortDir}`;
        
        if (searchQuery) {
            url += `&searchQuery=${encodeURIComponent(searchQuery)}`;
        }
        
        // Navigate to the new URL
        window.location.href = url;
    }
    
    // Handle search form submission
    function handleSearch(event) {
        event.preventDefault();
        
        const searchQuery = document.getElementById('searchInput').value.trim();
        const urlParams = new URLSearchParams(window.location.search);
        const sortField = urlParams.get('sortField') || 'dateAdded';
        const sortDir = urlParams.get('sortDir') || 'desc';
        const size = urlParams.get('size') || '9';
        
        let url = '/seller/products?';
        url += `page=0&size=${size}&sortField=${sortField}&sortDir=${sortDir}`;
        
        if (searchQuery) {
            url += `&searchQuery=${encodeURIComponent(searchQuery)}`;
        }
        
        // Navigate to the new URL
        window.location.href = url;
    }
    
    // Show notifications on page load if messages exist
    document.addEventListener('DOMContentLoaded', function() {
        const successMessage = document.querySelector('.alert-success');
        const errorMessage = document.querySelector('.alert-danger');

        if (successMessage) {
            showNotification(successMessage.querySelector('span').textContent, 'success');
            successMessage.remove(); // Remove original message
        }

        if (errorMessage) {
            showNotification(errorMessage.querySelector('span').textContent, 'danger');
            errorMessage.remove(); // Remove original message
        }
    });
    
    // Show notification
    function showNotification(message, type) {
        const container = document.getElementById('notificationContainer');

        // Create notification
        const notification = document.createElement('div');
        notification.className = `notification alert alert-${type}`;
        notification.innerHTML = `
            <div class="d-flex">
                <div class="me-3">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} fa-2x"></i>
                </div>
                <div>
                    <h5>${type === 'success' ? 'Success!' : 'Error!'}</h5>
                    <p class="mb-0">${message}</p>
                </div>
                <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        // Add to container
        container.appendChild(notification);

        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
</script>
</body>
</html> 