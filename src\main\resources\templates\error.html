<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        .error-container {
            text-align: center;
            padding: 100px 0;
        }
        .error-code {
            font-size: 120px;
            font-weight: 700;
            color: #dc3545;
            margin-bottom: 0;
            line-height: 1;
        }
        .error-message {
            font-size: 24px;
            color: #495057;
            margin-bottom: 30px;
        }
        .error-details {
            font-size: 16px;
            color: #6c757d;
            max-width: 500px;
            margin: 0 auto 30px;
        }
        .home-button {
            background-color: #2c7be5;
            color: white;
            font-weight: 600;
            padding: 10px 30px;
            border-radius: 5px;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            display: inline-block;
        }
        .home-button:hover {
            background-color: #1a68d1;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            color: white;
        }
        .technical-details {
            margin-top: 40px;
            text-align: left;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            font-family: monospace;
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="py-5 bg-light">
    <div class="container">
        <div class="error-container">
            <h1 class="error-code" th:text="${status != null ? status : 'Error'}">500</h1>
            <h2 class="error-message" th:text="${error != null ? error : 'Something went wrong'}">Internal Server Error</h2>
            <p class="error-details">
                We're sorry, but something went wrong on our end. Please try again later or contact support if the problem persists.
            </p>
            <div class="mt-4">
                <a th:href="@{/}" class="home-button">
                    <i class="fas fa-home me-2"></i>
                    Return to Homepage
                </a>
                <a th:href="@{/buyer/cart}" class="btn btn-outline-secondary ms-2">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Go to Cart
                </a>
            </div>
            
            <!-- Technical details for development environment -->
            <div class="technical-details" th:if="${trace != null}">
                <h5>Technical Details:</h5>
                <p th:text="${message}">Error Message</p>
                <pre th:text="${trace}">Stack Trace</pre>
            </div>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JavaScript -->
<script th:src="@{/js/scripts.js}"></script>

</body>
</html> 