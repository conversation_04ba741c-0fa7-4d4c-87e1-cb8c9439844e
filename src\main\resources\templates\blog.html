<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookix Blog - Latest Posts</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="blog-page py-5">
    <div class="container">
        <!-- Blog Page Header -->
        <header class="blog-page-header text-center mb-5">
            <h1 class="blog-main-title display-5">Latest Post</h1>
            <p class="blog-main-subtitle text-muted">Exploring Ideas, One Blog at a Time</p>
        </header>

        <!-- Highlighted Latest Post -->
        <section class="latest-post-highlight mb-5" th:if="${latestBlog != null}">
            <div class="card latest-post-card flex-md-row">
                <div class="col-md-6">
                    <img th:src="${latestBlog.imageUrl != null} ? @{${latestBlog.imageUrl}} : 'https://via.placeholder.com/600x400/f8f9fa/6c757d?text=Blog+Post'"
                         class="card-img-top card-img-md-left h-100 object-fit-cover"
                         th:alt="${latestBlog.title}">

                </div>
                <div class="col-md-6">
                    <div class="card-body p-md-5 d-flex flex-column justify-content-center">
                        <p class="blog-post-meta small text-muted mb-1" th:text="${#temporals.format(latestBlog.createdDate, 'dd MMM, yyyy')} + ' • by ' + ${latestBlog.user.fullName}">24 Oct, 2023 • by Author Name</p>
                        <h2 class="card-title latest-post-title mb-3"><a th:href="@{/blog/{id}(id=${latestBlog.blogId})}" th:text="${latestBlog.title}">Latest Blog Title</a></h2>
                        <p class="card-text latest-post-excerpt text-muted" th:text="${#strings.abbreviate(latestBlog.content, 150)}">
                            Blog excerpt...
                        </p>
                        <a th:href="@{/blog/{id}(id=${latestBlog.blogId})}" class="btn btn-link read-more-link ps-0 mt-2">Read More <i class="fas fa-arrow-right ms-1"></i></a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Blog Post Filters & Search -->
        <section class="blog-filters-search mb-4">
            <div class="row justify-content-between align-items-center">
                <div class="col-md-6 mb-3 mb-md-0">
                    <form class="d-flex blog-search-form" th:action="@{/blog}" method="get">
                        <input class="form-control me-2" type="search" name="search" th:value="${search}" placeholder="Search blog posts..." aria-label="Search blog posts">
                        <input type="hidden" name="sort" th:value="${sort}">
                        <button class="btn btn-outline-secondary" type="submit"><i class="fas fa-search"></i></button>
                    </form>
                </div>

                <div class="col-md-2 mb-3 mb-md-0 text-md-end">
                    <div th:if="${#authorization.expression('isAuthenticated()')}">
                        <a th:href="@{/blog/create}" class="btn btn-primary w-100"> <i class="fas fa-plus me-1"></i> Create Post
                        </a>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <form id="sortForm" th:action="@{/blog}" method="get">
                        <input type="hidden" name="search" th:value="${search}">
                        <select class="form-select form-select-sm" name="sort" aria-label="Filter posts" onchange="document.getElementById('sortForm').submit()">
                            <option value="latest" th:selected="${sort == 'latest'}">Sort by Latest</option>
                            <option value="oldest" th:selected="${sort == 'oldest'}">Sort by Oldest</option>
                            <option value="popular" th:selected="${sort == 'popular'}">Sort by Popularity</option>
                        </select>
                    </form>
                </div>
            </div>
        </section>

        <!-- Blog Posts Grid -->
        <section class="blog-posts-grid">
            <div class="row row-cols-1 row-cols-sm-2 row-cols-lg-4 g-4">
                <!-- Blog Post Card (Dynamic Blogs) -->
                <div class="col" th:each="blog : ${blogs}" th:if="${latestBlog == null || blog.blogId != latestBlog.blogId}">
                    <div class="card blog-post-card h-100">
                        <a th:href="@{/blog/{id}(id=${blog.blogId})}"><img th:src="${blog.imageUrl != null} ? @{${blog.imageUrl}} : 'https://via.placeholder.com/400x300/f8f9fa/6c757d?text=Blog+Post'"
                                                                           class="card-img-top card-img-md-left h-100 object-fit-cover"
                                                                           th:alt="${blog.title}">
                        </a>
                        <div class="card-body">
                            <p class="blog-post-meta small text-muted">
                                <span th:text="${#temporals.format(blog.createdDate, 'dd MMM, yyyy')}">Date</span>
                                <span class="mx-1">•</span> by
                                <a href="#" class="text-muted" th:text="${blog.user.fullName}">Author Name</a>
                            </p>
                            <h5 class="card-title blog-title">
                                <a th:href="@{/blog/{id}(id=${blog.blogId})}" th:text="${blog.title}">Blog Title</a>
                            </h5>
                        </div>
                    </div>
                </div>
                
                <!-- Empty state when no blogs are found -->
                <div class="col-12 text-center py-5" th:if="${#lists.isEmpty(blogs)}">
                    <div class="empty-state">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <h4>No Blog Posts Found</h4>
                        <p class="text-muted">We couldn't find any blog posts matching your criteria.</p>
                        <a th:href="@{/blog}" class="btn btn-outline-primary mt-3">View All Blogs</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pagination -->
        <nav th:if="${totalPages > 1}" aria-label="Blog page navigation" class="mt-5 d-flex justify-content-center">
            <ul class="pagination">
                <!-- Previous page -->
                <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled' : ''">
                    <a class="page-link" 
                       th:href="@{/blog(page=${currentPage - 1}, sort=${sort}, search=${search})}" 
                       tabindex="-1" th:aria-disabled="${currentPage == 0}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                
                <!-- Page numbers -->
                <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}"
                    th:classappend="${currentPage == i} ? 'active' : ''" th:if="${i < 5}">
                    <a class="page-link" 
                       th:href="@{/blog(page=${i}, sort=${sort}, search=${search})}"
                       th:text="${i + 1}" th:aria-current="${currentPage == i} ? 'page' : null">1</a>
                </li>
                
                <!-- Next page -->
                <li class="page-item" th:classappend="${currentPage + 1 >= totalPages} ? 'disabled' : ''">
                    <a class="page-link" 
                       th:href="@{/blog(page=${currentPage + 1}, sort=${sort}, search=${search})}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div> <!-- /.container -->
</main>


<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
        crossorigin="anonymous"></script>
<!-- Custom JS -->
<script th:src="@{/js/script.js}"></script>
</body>
</html>