<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ReadHub - Your World of Books</title>
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
  <!-- Custom CSS -->
  <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main>
  <!-- Hero Section -->
  <section class="hero-section text-white text-start d-flex align-items-center"
           th:if="${globalSettings.containsKey('hero_background_image') and not #strings.isEmpty(globalSettings['hero_background_image'])}"
           th:style="'background-image: url(' + @{${globalSettings['hero_background_image']}} + '); background-size: cover; background-position: center center; background-repeat: no-repeat;'">
    <div class="container">
      <p class="hero-subtitle" th:text="${globalSettings['hero_description']}">THE READHUB EDITORS'</p>

      <h1 class="hero-title" th:utext="${globalSettings['hero_title']}">First Time <br>With ReadHub?</h1>

      <a href="/about-contact" class="btn btn-hero">Learn More <i class="fas fa-arrow-right ms-1"></i></a>
    </div>
  </section>

  <!-- Features Bar -->
  <section class="features-bar py-4 bg-light">
    <div class="container">
      <div class="row text-center">
        <div class="col-md-3 col-6 feature-item">
          <h6>EXCLUSIVE DISCOUNTS</h6>
          <p class="small">Numerous promo codes daily</p>
        </div>
        <div class="col-md-3 col-6 feature-item">
          <h6>DIVERSE BOOKS</h6>
          <p class="small">Wide variety of book genres</p>
        </div>
        <div class="col-md-3 col-6 feature-item mt-3 mt-md-0">
          <h6>TRUSTED SELLERS</h6>
          <p class="small">Multiple reputable vendors</p>
        </div>
        <div class="col-md-3 col-6 feature-item mt-3 mt-md-0">
          <h6>FLEXIBLE PAYMENTS</h6>
          <p class="small">Various payment methods</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Featured Categories -->
  <section class="featured-categories py-5">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="section-title">Featured Categories</h2>
        <a href="/all-category" class="view-all-link">View All <i class="fas fa-arrow-right"></i></a>
      </div>
      <div class="row">
        <!-- Lặp qua danh sách danh mục từ cơ sở dữ liệu -->
        <div class="col-lg-2 col-md-4 col-6 mb-4" th:each="category, iterStat : ${categories}" th:if="${iterStat.index < 6}">
          <a th:href="@{/all-category(category=${category.categoryId})}" class="category-card text-decoration-none">
            <!-- Sử dụng ảnh mặc định cho các danh mục -->

            <h6 class="category-name" th:text="${category.categoryName}">Category Name</h6>
          </a>
        </div>
        <!-- Hiển thị danh mục mặc định nếu không có dữ liệu -->
        <div class="col-lg-2 col-md-4 col-6 mb-4" th:if="${#lists.isEmpty(categories) || categories.size() < 1}">
          <a href="/all-category" class="category-card text-decoration-none">
            <h6 class="category-name">Children's Books</h6>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- People's Choice -->
  <section class="people-choice-section py-5 bg-light">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="section-title">People's Choice</h2>
        <a th:href="@{/all-category(sort='averageRating',direction='DESC')}" class="view-all-link">View All <i class="fas fa-arrow-right"></i></a>
      </div>
      <div class="row">
        <!-- Hiển thị sách được đánh giá cao từ cơ sở dữ liệu -->
        <div class="col-lg col-md-4 col-sm-6 mb-4" th:each="book : ${topRatedBooks}">
          <div class="book-card">
            <a th:href="@{/product-detail(book_id=${book.bookId})}">
              <!-- Hiển thị ảnh bìa từ cơ sở dữ liệu hoặc mặc định nếu không có -->
              <img th:src="${book.coverImgUrl != null ? book.coverImgUrl : '/images/book-placeholder.jpg'}" 
                  th:alt="${book.title}" class="book-cover img-fluid">
            </a>
            <div class="book-info">
              <h6 class="book-title">
                <a th:href="@{/product-detail(book_id=${book.bookId})}" th:text="${book.title}">Book Title</a>
              </h6>
              <!-- Sử dụng fragment đánh giá sao -->
              <div th:replace="~{fragments/book-rating :: star-rating(${book.averageRating})}"></div>
              
              <!-- Sử dụng fragment hiển thị giá -->
              <div th:replace="~{fragments/book-price :: price-display(${book.originalPrice}, ${book.sellingPrice})}"></div>
            </div>
          </div>
        </div>
        
        <!-- Hiển thị mẫu nếu không có dữ liệu -->
        <div class="col-lg col-md-4 col-sm-6 mb-4" th:if="${#lists.isEmpty(topRatedBooks)}">
          <div class="book-card">
            <a href="#"><img th:src="@{/images/book-project-nim.jpg}" alt="Project Nim" class="book-cover img-fluid"></a>
            <div class="book-info">
              <h6 class="book-title"><a href="#">Under a Firefly Moon (Firefly Lake Book 1)</a></h6>
              <div class="star-rating">
                <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i>
              </div>
              <p class="book-price">$100.00 - $400.00</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Discount Banner -->
  <section class="discount-banner py-4">
    <div class="container text-center">
      <span class="discount-icon">%</span>
      <span class="discount-text">Lots of discount codes are waiting for you</span>
      <a href="/all-category" class="btn btn-shop-deals">Shop Now <i class="fas fa-arrow-right ms-1"></i></a>
    </div>
  </section>

  <!-- New Additions -->
  <section class="new-additions-section py-5">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="section-title">New Additions</h2>
        <a th:href="@{/all-category(sort='dateAdded',direction='DESC')}" class="view-all-link">View All <i class="fas fa-arrow-right"></i></a>
      </div>
      <div class="row">
        <!-- Hiển thị sách mới thêm từ cơ sở dữ liệu -->
        <div class="col-lg col-md-4 col-sm-6 mb-4" th:each="book : ${newAdditions}">
          <div class="book-card">
            <div class="position-relative">
              <a th:href="@{/product-detail(book_id=${book.bookId})}">
                <img th:src="${book.coverImgUrl != null ? book.coverImgUrl : '/images/book-placeholder.jpg'}"
                     th:alt="${book.title}" class="book-cover img-fluid">
              </a>
              <!-- Hiển thị badge Sale nếu đây là sách giảm giá -->
              <span class="badge bg-danger sale-badge" 
                   th:if="${book.originalPrice != null && book.sellingPrice != null && book.originalPrice.compareTo(book.sellingPrice) > 0}">Sale</span>
            </div>
            <div class="book-info">
              <h6 class="book-title">
                <a th:href="@{/product-detail(book_id=${book.bookId})}" th:text="${book.title}">Book Title</a>
              </h6>
              <!-- Sử dụng fragment đánh giá sao -->
              <div th:replace="~{fragments/book-rating :: star-rating(${book.averageRating})}"></div>
              
              <!-- Sử dụng fragment hiển thị giá -->
              <div th:replace="~{fragments/book-price :: price-display(${book.originalPrice}, ${book.sellingPrice})}"></div>
            </div>
          </div>
        </div>
        
        <!-- Hiển thị mẫu nếu không có dữ liệu -->
        <div class="col-lg col-md-4 col-sm-6 mb-4" th:if="${#lists.isEmpty(newAdditions)}">
          <div class="book-card">
            <div class="position-relative">
              <a href="#"><img th:src="@{/images/book-star-trek-picard.jpg}" alt="Star Trek Picard" class="book-cover img-fluid"></a>
              <span class="badge bg-danger sale-badge">Sale</span>
            </div>
            <div class="book-info">
              <h6 class="book-title"><a href="#">Star Trek: Picard: The Last Best Hope</a></h6>
              <div class="star-rating">
                <i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="far fa-star"></i>
              </div>
              <p class="book-price">
                <span class="original-price text-decoration-line-through me-1">$99.29</span>
                <span class="sale-price">$33.95</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Best New Books of April -->
  <section class="best-new-books py-5 bg-light">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-md-6 mb-4 mb-md-0">
          <img th:src="@{/uploads/settings/best_book.jpg}" alt="Person Reading Book" class="img-fluid rounded">
        </div>
        <div class="col-md-6 text-center text-md-start ps-md-5">
          <p class="text-uppercase small text-muted">NEW YEAR, NEW BOOKS</p>
          <h2 class="display-5 fw-bold mb-3">Explore your favorite books with READHUB</h2>
        </div>
      </div>
    </div>
  </section>

  <!-- Favorite Authors -->
  <section class="super-books-testimonial py-5">
    <div class="container text-center">
      <div class="quote-icon-superbooks mb-3">"</div>
      <h2 class="section-title mb-3">Super Books</h2>
      <p class="lead mb-4 col-md-8 mx-auto">READHUB is a monthly book review publication distributed to 400,000 avid readers through subscribing bookstores & public libraries.</p>

    </div>
  </section>

  <!-- From the Blog -->
  <section class="from-the-blog-section py-5 bg-light">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="section-title">From the Blog</h2>
        <a href="/blog" class="view-all-link">View All <i class="fas fa-arrow-right"></i></a>
      </div>
      <div class="row">
        <!-- Display blog posts from database -->
        <div class="col-md-6 col-lg-3 mb-4" th:each="blog, iterStat : ${recentBlogs}">
          <div class="card blog-post-card h-100">
            <a th:href="@{/blog/{id}(id=${blog.blogId})}">
              <!-- Use actual blog image or placeholder -->
              <img th:src="${blog.imageUrl != null} ? @{${blog.imageUrl}} : 'https://via.placeholder.com/400x300/f8f9fa/6c757d?text=Blog+Post'"
                  class="card-img-top" th:alt="${blog.title}">
            </a>
            <div class="card-body d-flex flex-column">
              <p class="card-text small text-muted">
                <span th:text="${#temporals.format(blog.createdDate, 'dd MMM, yyyy')}">24 Oct, 2023</span> 
                by <span th:text="${blog.user != null ? blog.user.fullName : 'Unknown'}">Author</span>
              </p>
              <h5 class="card-title blog-title">
                <a th:href="@{/blog/{id}(id=${blog.blogId})}" th:text="${blog.title}">Blog Title</a>
              </h5>
            </div>
          </div>
        </div>
        
        <!-- Hiển thị mẫu nếu không có dữ liệu -->
        <div class="col-md-6 col-lg-3 mb-4" th:if="${#lists.isEmpty(recentBlogs)}">
          <div class="card blog-post-card h-100">
            <a href="#"><img th:src="@{/images/blog-post-1.jpg}" class="card-img-top" alt="Blog Post 1"></a>
            <div class="card-body d-flex flex-column">
              <p class="card-text small text-muted">24 Oct, 2023 by Ali Tufan</p>
              <h5 class="card-title blog-title"><a href="#">Should You Feel Embarrassed for Reading Kids Books?</a></h5>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</main>


<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle (Popper.js included) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
        crossorigin="anonymous"></script>
<!-- Custom JS -->
<script th:src="@{/js/script.js}"></script>

<!-- Chat Widget - Buyers Only -->
<div th:replace="~{fragments/chat-widget :: buyer-chat-widget}"></div>

</body>
</html>