<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    
    <style>
        .failed-container {
            text-align: center;
            padding: 3rem 0;
        }
        
        .failed-icon {
            font-size: 5rem;
            color: #dc3545;
            margin-bottom: 1.5rem;
        }
        
        .failed-title {
            color: #dc3545;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .order-info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .action-buttons {
            margin-top: 2rem;
        }
        
        .btn-danger-custom {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 8px;
            color: white;
        }
        
        .btn-danger-custom:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
            transform: translateY(-1px);
            color: white;
        }
        
        .troubleshooting {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .troubleshooting h6 {
            color: #856404;
            margin-bottom: 1rem;
        }
        
        .troubleshooting ul {
            color: #856404;
            margin-bottom: 0;
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="failed-container">
                    <i class="fas fa-times-circle failed-icon"></i>
                    <h1 class="failed-title">Payment Failed</h1>
                    <p class="lead">We're sorry, but your payment could not be processed at this time.</p>
                    
                    <div class="order-info-card">
                        <h5 class="mb-3">
                            <i class="fas fa-receipt me-2"></i>
                            Order Details #<span th:text="${customerOrder.customerOrderId}">12345</span>
                        </h5>
                        
                        <div class="info-row">
                            <span>Order Date:</span>
                            <span th:text="${#temporals.format(customerOrder.createdAt, 'dd/MM/yyyy HH:mm')}">01/01/2024 10:30</span>
                        </div>
                        
                        <div class="info-row">
                            <span>Payment Method:</span>
                            <span th:text="${customerOrder.paymentMethod}">VNPay</span>
                        </div>
                        
                        <div class="info-row">
                            <span>Payment Status:</span>
                            <span class="badge bg-danger" th:text="${customerOrder.paymentStatus}">FAILED</span>
                        </div>
                        
                        <div class="info-row">
                            <span>Total Amount:</span>
                            <span th:text="${#numbers.formatDecimal(customerOrder.totalAmount, 0, 'COMMA', 0, 'POINT')} + ' VND'">480,000 VND</span>
                        </div>
                    </div>
                    
                    <div class="troubleshooting">
                        <h6><i class="fas fa-lightbulb me-2"></i>What can you do?</h6>
                        <ul>
                            <li>Check your payment method details and try again</li>
                            <li>Ensure you have sufficient funds in your account</li>
                            <li>Try using a different payment method</li>
                            <li>Contact your bank if the problem persists</li>
                            <li>Contact our customer support for assistance</li>
                        </ul>
                    </div>
                    
                    <div class="action-buttons">
                        <a th:href="@{'/buyer/checkout?customerOrderId=' + ${customerOrder.customerOrderId}}" class="btn btn-danger-custom me-3">
                            <i class="fas fa-redo me-2"></i>
                            Try Payment Again
                        </a>
                        <a th:href="@{/buyer/cart}" class="btn btn-outline-secondary me-2">
                            <i class="fas fa-shopping-cart me-2"></i>
                            Back to Cart
                        </a>
                        <a th:href="@{/}" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>
                            Continue Shopping
                        </a>
                    </div>
                    
                    <div class="mt-4">
                        <p class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            Your order has been saved and no payment has been charged. You can retry the payment or contact support for help.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
