<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - READHUB</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS (shared style.css + signup specific) -->
    <style>
        /* Signup Page Specific Styles (largely similar to login page styles) */
        body.signup-page {
            background-color: #F8F5F0; /* Light beige background */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 1rem 0; /* Added some vertical padding for scroll */
            font-family: 'Open Sans', sans-serif;
        }

        .signup-container {
            background-color: #FFFFFF;
            padding: 2rem 2.5rem; /* Adjusted padding slightly */
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 480px; /* Slightly wider for more fields */
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .signup-logo {
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2C3E50; /* Dark Teal/Slate */
            text-align: center;
            margin-bottom: 1.5rem;
        }
        .signup-logo a {
            text-decoration: none;
            color: inherit;
        }

        .form-label-signup {
            font-weight: 600;
            font-size: 0.875rem;
            color: #495057;
            margin-bottom: 0.3rem;
        }

        .form-control-signup {
            height: calc(1.5em + .75rem + 6px);
            border-radius: 0.25rem;
            font-size: 0.95rem;
        }
        .form-control-signup:focus {
            border-color: #2C3E50;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .btn-signup-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: #FFFFFF;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-signup-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
            color: #FFFFFF;
        }

        /* Style for radio buttons inline */
        .gender-options .form-check {
            padding-left: 1.75rem; /* Adjust for checkbox alignment */
        }
        .gender-options .form-check-input {
            margin-top: 0.25rem; /* Align radio with label text */
        }

        /* Re-using Google login button style from login page */
        .btn-google-login {
            background-color: #FFFFFF;
            border: 1px solid #ced4da;
            color: #333;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-google-login:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
        }
        .btn-google-login img,
        .btn-google-login .fab {
            width: 20px;
            margin-right: 0.75rem;
        }

        .or-separator {
            display: flex;
            align-items: center;
            text-align: center;
            color: #6c757d;
            margin: 1.25rem 0;
            font-size: 0.9rem;
        }
        .or-separator::before,
        .or-separator::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #dee2e6;
        }
        .or-separator:not(:empty)::before {
            margin-right: .5em;
        }
        .or-separator:not(:empty)::after {
            margin-left: .5em;
        }

        .terms-link {
            font-size: 0.8rem;
            color: #555;
            text-align: center;
            margin-top: 1rem;
            margin-bottom: 1.5rem; /* Space before Login with Google */
        }
        .terms-link a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: underline;
        }
        .terms-link a:hover {
            color: #1e2b37;
        }

        .login-link-footer, .seller-center-link { /* Renamed from signup-link for clarity */
            font-size: 0.9rem;
            color: #555;
        }
        .login-link-footer a, .seller-center-link a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: none;
        }
        .login-link-footer a:hover, .seller-center-link a:hover {
            text-decoration: underline;
            color: #1e2b37;
        }
        .seller-center-link {
            margin-top: 2rem;
            text-align: center;
            padding-bottom: 1rem; /* Ensure it's visible if content is long */
        }
    </style>
</head>
<body class="signup-page">

<main class="signup-container">
    <div class="signup-logo">
        <a href="/">ReadHub</a> <!-- Link to homepage -->
    </div>

    <h2 class="text-center h4 mb-4" style="color: #333; font-weight: 600;">
        <span th:if="${param.sellerFlow}">Create Seller Account</span>
        <span th:unless="${param.sellerFlow}">Create your Account</span>
    </h2>
    
    <!-- Success message display -->
    <div th:if="${successMessage}" class="alert alert-success text-center mb-3" role="alert">
        <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
    </div>
    
    <!-- Error message display -->
    <div th:if="${errorMessage}" class="alert alert-danger text-center mb-3" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
    </div>

    <!-- Registration form -->
    <form th:action="${param.sellerFlow} ? @{/seller/signup} : @{/buyer/register}" th:object="${userRegistrationDTO}" method="post" id="registrationForm">
        <div class="mb-3">
            <label for="fullName" class="form-label-signup">Full Name</label>
            <input type="text" class="form-control form-control-signup" th:field="*{fullName}" id="fullName" placeholder="Enter your full name" required>
            <div class="invalid-feedback" th:if="${#fields.hasErrors('fullName')}" th:errors="*{fullName}">Full name error</div>
        </div>

        <div class="mb-3">
            <label for="email" class="form-label-signup">Email address</label>
            <input type="email" class="form-control form-control-signup" th:field="*{email}" id="email" placeholder="Enter your email" required>
            <div class="invalid-feedback" th:if="${#fields.hasErrors('email')}" th:errors="*{email}">Email error</div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="password" class="form-label-signup">Password</label>
                <input type="password" class="form-control form-control-signup" th:field="*{password}" id="password" placeholder="Create a password" required>
                <div class="invalid-feedback" th:if="${#fields.hasErrors('password')}" th:errors="*{password}">Password error</div>
                <small class="form-text text-muted">Must be at least 6 characters with letters and numbers</small>
            </div>
            <div class="col-md-6 mb-3">
                <label for="confirmPassword" class="form-label-signup">Confirm Password</label>
                <input type="password" class="form-control form-control-signup" th:field="*{confirmPassword}" id="confirmPassword" placeholder="Confirm your password" required>
                <div class="invalid-feedback" th:if="${#fields.hasErrors('confirmPassword')}" th:errors="*{confirmPassword}">Confirm password error</div>
            </div>
        </div>

        <div class="mb-3">
            <label for="phoneNumber" class="form-label-signup">Phone Number</label>
            <input type="tel" class="form-control form-control-signup" th:field="*{phoneNumber}" id="phoneNumber" placeholder="Enter your phone number" required>
            <div class="invalid-feedback" th:if="${#fields.hasErrors('phoneNumber')}" th:errors="*{phoneNumber}">Phone number error</div>
        </div>

        <div class="mb-3">
            <label for="dateOfBirth" class="form-label-signup">Date of Birth</label>
            <input type="date" class="form-control form-control-signup" th:field="*{dateOfBirth}" id="dateOfBirth">
            <div class="invalid-feedback" th:if="${#fields.hasErrors('dateOfBirth')}" th:errors="*{dateOfBirth}">Date of birth error</div>
        </div>

        <!-- Gender Radio Buttons -->
        <div class="mb-3">
            <label for="gender" class="form-label-signup">Gender</label>
            <div class="gender-options d-flex">
                <div class="form-check form-check-inline me-3">
                    <input class="form-check-input" type="radio" th:field="*{gender}" id="genderMale" value="male" required>
                    <label class="form-check-label" for="genderMale" style="font-size: 0.9rem;">Male</label>
                </div>
                <div class="form-check form-check-inline me-3">
                    <input class="form-check-input" type="radio" th:field="*{gender}" id="genderFemale" value="female" required>
                    <label class="form-check-label" for="genderFemale" style="font-size: 0.9rem;">Female</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" th:field="*{gender}" id="genderOther" value="other" required>
                    <label class="form-check-label" for="genderOther" style="font-size: 0.9rem;">Other</label>
                </div>
            </div>
            <div class="invalid-feedback" th:if="${#fields.hasErrors('gender')}" th:errors="*{gender}">Gender error</div>
        </div>
        <!-- End of Gender Radio Buttons -->

        <div class="terms-link">
            By signing up, you agree to our <a href="#">Terms of Service</a>.
        </div>

        <button type="submit" class="btn btn-signup-primary w-100 mb-3">
            <span th:if="${param.sellerFlow}">Create Seller Account</span>
            <span th:unless="${param.sellerFlow}">Sign Up</span>
        </button>

        <div class="or-separator">or</div>

        <button type="button" class="btn btn-google-login w-100 mb-3">
            <i class="fab fa-google me-2"></i> 
            <span th:if="${param.sellerFlow}">Sign Up with Google (Seller)</span>
            <span th:unless="${param.sellerFlow}">Sign Up with Google</span>
        </button>
    </form>

    <p class="text-center login-link-footer mb-0">
        Already have an account? <a href="login">Login</a>
    </p>
</main>

<div class="seller-center-link">
    <a th:if="${param.sellerFlow}" href="/buyer/login">Shopping Center</a>
    <a th:unless="${param.sellerFlow}" href="/seller/login">Seller Center</a>
</div>

<!-- Bootstrap JS Bundle (Popper.js included) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS for form validation -->
<script th:src="@{/js/signup-validation.js}"></script>
</body>
</html>