<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${isEdit ? 'Edit Address' : 'Create New Address'}">Create New Address - Bookix</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        .form-check-label {
            font-weight: 500;
            color: #555;
        }
        .is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
        .invalid-feedback {
            display: none;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        #addressAutocomplete {
            margin-bottom: 1rem;
        }
        select.is-invalid, select.is-valid {
            background-image: none;
            padding-right: 0.75rem;
        }
    </style>
    <!-- Vietnamese Provinces API dependencies -->    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.26.1/axios.min.js" integrity="sha512-bPh3uwgU5qEMipS/VOmRqynnMXGGSRv+72H/N260MQeXZIK4PG48401Bsby9Nq5P5fz7hy5UGNmC/W1Z51h2GQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script th:src="@{/js/address-location.js}" defer></script>
    
    <!-- Google Maps API for address autocomplete -->

</head>
<body>
<div th:replace="~{fragments/header :: header-content}"></div>
<main class="account-page py-5 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item"><a th:href="@{/buyer/account-info}">Account Information</a></li>
                <li class="breadcrumb-item"><a th:href="@{/buyer/addresses}">Addresses</a></li>
                <li class="breadcrumb-item active" aria-current="page" th:text="${isEdit ? 'Edit Address' : 'Create New Address'}">Create New Address</li>
            </ol>
        </nav>
        <div class="row">
            <div th:replace="~{fragments/buyer-account-sidebar :: sidebar('address')}"></div>
            <section class="col-lg-9 account-content">
                <div class="card">
                    <div class="card-header bg-white py-3">
                        <h4 class="mb-0" th:text="${isEdit ? 'Edit Address' : 'Create New Address'}">Create New Address</h4>
                    </div>
                    <div class="card-body p-4">
                        <div th:if="${param.success}" class="alert alert-success mb-4">
                            Address saved successfully!
                        </div>
                        <div th:if="${param.error}" class="alert alert-danger mb-4">
                            Error saving address. Please try again. <span th:if="${errorMessage}" th:text="${errorMessage}"></span>
                        </div>
                        <form th:action="${isEdit ? '/buyer/addresses/update/' + address?.addressId : '/buyer/addresses/create'}"
                              th:object="${address}" method="post" id="addressForm" novalidate>
                            <input type="hidden" th:if="${isEdit}" th:field="*{addressId}">
                            <div class="mb-3">
                                <label for="recipientName" class="form-label">Recipient Name</label>
                                <input type="text" class="form-control" id="recipientName" th:field="*{recipientName}"
                                       placeholder="Enter recipient name" required minlength="2" maxlength="100">
                                <div class="invalid-feedback" th:if="${#fields.hasErrors('recipientName')}" th:errors="*{recipientName}">
                                    Recipient name is required and must be between 2-100 characters
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="recipientPhone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="recipientPhone" th:field="*{recipientPhone}"
                                       placeholder="Enter phone number" required pattern="^(\+84|84|0)[3|5|7|8|9][0-9]{8}$">
                                <div class="invalid-feedback" th:if="${#fields.hasErrors('recipientPhone')}" th:errors="*{recipientPhone}">
                                    Valid phone number is required (e.g., 0912345678)
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="company" class="form-label">Company (Optional)</label>
                                <input type="text" class="form-control" id="company" th:field="*{company}"
                                       placeholder="Enter company name" maxlength="50">
                                <div class="invalid-feedback" th:if="${#fields.hasErrors('company')}" th:errors="*{company}">
                                    Company name must be less than 50 characters
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 mb-3 mb-md-0">
                                    <label for="province" class="form-label">Province/City</label>
                                    <select class="form-control" id="province" name="provinceCode" required>
                                        <option value="">Select Province/City</option>
                                    </select>
                                    <!-- Hidden field to store the province name -->
                                    <input type="hidden" id="provinceName" th:field="*{province}">
                                    <div class="invalid-feedback">
                                        Province is required
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3 mb-md-0">
                                    <label for="district" class="form-label">District</label>
                                    <select class="form-control" id="district" name="districtCode" required disabled>
                                        <option value="">Select District</option>
                                    </select>
                                    <!-- Hidden field to store the district name -->
                                    <input type="hidden" id="districtName" th:field="*{district}">
                                    <div class="invalid-feedback">
                                        District is required
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <label for="ward" class="form-label">Ward/Commune</label>
                                    <select class="form-control" id="ward" name="wardCode" disabled required>
                                        <option value="">Select Ward/Commune</option>
                                    </select>
                                    <!-- Hidden field to store the ward name -->
                                    <input type="hidden" id="wardName" th:field="*{ward}">
                                    <div class="invalid-feedback">
                                        Ward is required
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="addressDetail" class="form-label">Address Details (Street, House No.)</label>
                                <textarea class="form-control" id="addressDetail" th:field="*{addressDetail}"
                                          rows="2" placeholder="e.g., 123 Nguyen Hue Street"
                                          required maxlength="500"></textarea>
                                <div class="invalid-feedback" th:if="${#fields.hasErrors('addressDetail')}" th:errors="*{addressDetail}">
                                    Address details are required and must be less than 500 characters
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label d-block">Address Type</label>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="addressType"
                                           id="addressType0" value="0" th:field="*{addressType}" required>
                                    <label class="form-check-label" for="addressType0">Residential</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="addressType"
                                           id="addressType1" value="1" th:field="*{addressType}" required>
                                    <label class="form-check-label" for="addressType1">Company</label>
                                </div>
                                <div class="invalid-feedback d-block" th:if="${#fields.hasErrors('addressType')}" th:errors="*{addressType}">
                                    Please select an address type
                                </div>
                            </div>
                            <div class="mb-4 form-check" th:if="${!isEdit || (address != null && !address.default)}">
                                <input type="checkbox" class="form-check-input" id="setDefault" th:field="*{default}">
                                <label class="form-check-label" for="setDefault">Set as Default Address</label>
                            </div>
                            <div class="mb-4 alert alert-info" th:if="${isEdit && address != null && address.default}">
                                <i class="fas fa-info-circle me-2"></i> This is your default address
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Address
                            </button>
                            <a th:href="@{/buyer/addresses}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                        </form>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>
<div th:replace="~{fragments/footer :: footer-content}"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>


    function populateProvinces() {
        const provinceSelect = document.getElementById('province');
        if (!provinceSelect) return;
        provinceSelect.innerHTML = '<option value="">Select Province/City</option>';
        Object.keys(vietnamData).sort().forEach(province => {
            const option = document.createElement('option');
            option.value = province;
            option.textContent = province;
            provinceSelect.appendChild(option);
        });
    }

    function populateDistricts(province) {
        const districtSelect = document.getElementById('district');
        const wardSelect = document.getElementById('ward');
        if (!districtSelect || !wardSelect) return;

        districtSelect.innerHTML = '<option value="">Select District</option>';
        wardSelect.innerHTML = '<option value="">Select Ward/Commune (Optional)</option>';

        if (province && vietnamData[province]) {
            Object.keys(vietnamData[province]).sort().forEach(district => {
                const option = document.createElement('option');
                option.value = district;
                option.textContent = district;
                districtSelect.appendChild(option);
            });
            districtSelect.disabled = false;
        } else {
            districtSelect.disabled = true;
            wardSelect.disabled = true;
        }
    }

    function populateWards(province, district) {
        const wardSelect = document.getElementById('ward');
        if (!wardSelect) return;

        wardSelect.innerHTML = '<option value="">Select Ward/Commune (Optional)</option>';

        if (province && district && vietnamData[province] && vietnamData[province][district]) {
            vietnamData[province][district].sort().forEach(ward => {
                const option = document.createElement('option');
                option.value = ward;
                option.textContent = ward;
                wardSelect.appendChild(option);
            });
            wardSelect.disabled = false;
        } else {
            wardSelect.disabled = true;
        }
    }

    function fillInAddress() {
        if (!autocomplete) {
            console.error("Autocomplete not initialized.");
            return;
        }

        const place = autocomplete.getPlace();
        const provinceSelect = document.getElementById('province');
        const districtSelect = document.getElementById('district');
        const wardSelect = document.getElementById('ward');
        const addressDetailInput = document.getElementById('addressDetail');

        // Clear previous values and validation states
        const fields = [provinceSelect, districtSelect, wardSelect, addressDetailInput];
        fields.forEach(field => {
            if (field) {
                if (field.tagName === 'SELECT') {
                    field.selectedIndex = 0;
                    field.classList.remove('is-valid', 'is-invalid');
                } else {
                    field.value = '';
                    field.classList.remove('is-valid', 'is-invalid');
                }
                const feedback = field.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.style.display = 'none';
                }
            }
        });

        if (!place || !place.address_components) {
            console.warn("Place selected has no address components:", place);
            const autocompleteInput = document.getElementById('addressAutocomplete');
            if (autocompleteInput) {
                autocompleteInput.classList.add('is-invalid');
            }
            return;
        }

        let streetNumber = '';
        let route = '';
        let ward = '';
        let district = '';
        let province = '';

        for (const component of place.address_components) {
            const types = component.types;
            if (types.includes('street_number')) {
                streetNumber = component.long_name;
            }
            if (types.includes('route')) {
                route = component.long_name;
            }
            if (types.includes('sublocality_level_1') || types.includes('sublocality') || types.includes('neighborhood')) {
                ward = component.long_name;
            }
            if (types.includes('administrative_area_level_2') || (types.includes('locality') && !types.includes('administrative_area_level_1'))) {
                district = component.long_name;
            }
            if (types.includes('administrative_area_level_1') || (types.includes('locality') && types.includes('political'))) {
                province = component.long_name;
            }
        }

        // Normalize province names
        province = province.replace(/^Thành phố\s+|^Tỉnh\s+/i, '');
        if (/hanoi|ha noi|hà nội/i.test(province)) province = 'Hà Nội';
        if (/ho chi minh|hồ chí minh/i.test(province)) province = 'Hồ Chí Minh';

        // Populate dropdowns
        if (province && vietnamData[province]) {
            if (provinceSelect) {
                provinceSelect.value = province;
                populateDistricts(province);
                if (district && vietnamData[province][district]) {
                    if (districtSelect) {
                        districtSelect.value = district;
                        populateWards(province, district);
                        if (ward && vietnamData[province][district].includes(ward) && wardSelect) {
                            wardSelect.value = ward;
                        }
                    }
                }
            }
        }

        // Set address details
        const detailedAddress = (streetNumber + ' ' + route).trim();
        if (addressDetailInput) {
            addressDetailInput.value = detailedAddress || '';
        }

        // Validate fields
        fields.forEach(field => {
            if (field) {
                validateAndSetStatus(field);
            }
        });
    }

    function validateAndSetStatus(element) {
        if (!element) return;
        const isRequired = element.hasAttribute('required');
        const hasValue = element.tagName === 'SELECT' ? element.value !== '' : element.value.trim() !== '';
        const isValid = !isRequired || hasValue;

        setValidationVisuals(element, isValid, `${element.previousElementSibling?.textContent || 'Field'} is required.`);
    }

    window.setValidationVisuals = function(element, isValid, errorMessage = '') {
        if (!element) return;
        const feedbackElement = element.nextElementSibling;

        if (isValid) {
            element.classList.remove('is-invalid');
            element.classList.add('is-valid');
            if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                feedbackElement.style.display = 'none';
                feedbackElement.textContent = '';
            }
        } else {
            element.classList.remove('is-valid');
            element.classList.add('is-invalid');
            if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                feedbackElement.textContent = errorMessage;
                feedbackElement.style.display = 'block';
            }
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const addressForm = document.getElementById('addressForm');
        const recipientNameInput = document.getElementById('recipientName');
        const phoneInput = document.getElementById('recipientPhone');
        const companyInput = document.getElementById('company');
        const provinceSelect = document.getElementById('province');
        const districtSelect = document.getElementById('district');
        const wardSelect = document.getElementById('ward');
        const addressDetailInput = document.getElementById('addressDetail');

        function validatePhone(phone) {
            const regex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
            return regex.test(phone);
        }

        // Set up province/district/ward selectors
        const savedProvince = document.getElementById('provinceName')?.value;
        const savedDistrict = document.getElementById('districtName')?.value;
        const savedWard = document.getElementById('wardName')?.value;

        if (savedProvince) {
            const provinceObserver = new MutationObserver(function(mutations) {
                const provinceSelect = document.getElementById('province');
                if (provinceSelect && provinceSelect.options.length > 1) {
                    // Find and select the matching province
                    for (let i = 0; i < provinceSelect.options.length; i++) {
                        if (provinceSelect.options[i].text === savedProvince) {
                            provinceSelect.value = provinceSelect.options[i].value;
                            provinceSelect.dispatchEvent(new Event('change'));
                            this.disconnect(); // Stop observing once we've found the match
                            break;
                        }
                    }
                }
            });

            const provinceSelect = document.getElementById('province');
            if (provinceSelect) {
                provinceObserver.observe(provinceSelect, { childList: true });
            }

            // Set up observers for district and ward
            if (savedDistrict) {
                const districtObserver = new MutationObserver(function(mutations) {
                    const districtSelect = document.getElementById('district');
                    if (districtSelect && districtSelect.options.length > 1) {
                        // Find and select the matching district
                        for (let i = 0; i < districtSelect.options.length; i++) {
                            if (districtSelect.options[i].text === savedDistrict) {
                                districtSelect.value = districtSelect.options[i].value;
                                districtSelect.dispatchEvent(new Event('change'));
                                this.disconnect(); // Stop observing
                                break;
                            }
                        }
                    }
                });

                const districtSelect = document.getElementById('district');
                if (districtSelect) {
                    districtObserver.observe(districtSelect, { childList: true });
                }

                // Set up ward observer
                if (savedWard) {
                    const wardObserver = new MutationObserver(function(mutations) {
                        const wardSelect = document.getElementById('ward');
                        if (wardSelect && wardSelect.options.length > 1) {
                            // Find and select the matching ward
                            for (let i = 0; i < wardSelect.options.length; i++) {
                                if (wardSelect.options[i].text === savedWard) {
                                    wardSelect.value = wardSelect.options[i].value;
                                    wardSelect.dispatchEvent(new Event('change'));
                                    this.disconnect(); // Stop observing
                                    break;
                                }
                            }
                        }
                    });

                    const wardSelect = document.getElementById('ward');
                    if (wardSelect) {
                        wardObserver.observe(wardSelect, { childList: true });
                    }
                }
            }
        }

        // Attach event listeners only if elements exist
        if (provinceSelect) {
            provinceSelect.addEventListener('change', function() {
                populateDistricts(this.value);
                validateAndSetStatus(this);
            });
        }

        if (districtSelect) {
            districtSelect.addEventListener('change', function() {
                populateWards(provinceSelect?.value || '', this.value);
                validateAndSetStatus(this);
            });
        }

        if (wardSelect) {
            wardSelect.addEventListener('change', function() {
                validateAndSetStatus(this);
            });
        }

        if (recipientNameInput) {
            recipientNameInput.addEventListener('input', function() {
                const value = this.value.trim();
                const isValid = value.length >= 2 && value.length <= 100;
                setValidationVisuals(this, isValid, 'Recipient name must be 2-100 characters.');
            });
        }

        if (phoneInput) {
            phoneInput.addEventListener('input', function() {
                const isValid = validatePhone(this.value);
                setValidationVisuals(this, isValid, 'Please enter a valid Vietnamese phone number (e.g., 0912345678).');
            });
        }

        if (companyInput) {
            companyInput.addEventListener('input', function() {
                const value = this.value.trim();
                const maxLength = 255;
                if (value.length > maxLength) {
                    setValidationVisuals(this, false, `Company name must be less than ${maxLength} characters.`);
                } else if (value.length > 0) {
                    setValidationVisuals(this, true);
                } else {
                    this.classList.remove('is-invalid', 'is-valid');
                    const feedback = this.nextElementSibling;
                    if (feedback && feedback.classList.contains('invalid-feedback')) {
                        feedback.style.display = 'none';
                    }
                }
            });
        }

        if (addressDetailInput) {
            addressDetailInput.addEventListener('input', function() {
                const value = this.value.trim();
                const isValid = value.length > 0 && value.length <= 500;
                setValidationVisuals(this, isValid, 'Address details are required (max 500 chars).');
            });
        }

        if (addressForm) {
            addressForm.addEventListener('submit', function(event) {
                let isFormValid = true;

                if (recipientNameInput) {
                    const nameValue = recipientNameInput.value.trim();
                    const isNameValid = nameValue.length >= 2 && nameValue.length <= 100;
                    setValidationVisuals(recipientNameInput, isNameValid, 'Recipient name must be 2-100 characters.');
                    if (!isNameValid) isFormValid = false;
                }

                if (phoneInput) {
                    const isPhoneValid = validatePhone(phoneInput.value);
                    setValidationVisuals(phoneInput, isPhoneValid, 'Valid Vietnamese phone number required.');
                    if (!isPhoneValid) isFormValid = false;
                }

                if (companyInput) {
                    const companyValue = companyInput.value.trim();
                    if (companyValue.length > 255) {
                        setValidationVisuals(companyInput, false, 'Company name must be less than 255 characters.');
                        isFormValid = false;
                    } else if (companyValue.length > 0) {
                        setValidationVisuals(companyInput, true);
                    } else {
                        companyInput.classList.remove('is-invalid', 'is-valid');
                        const feedback = companyInput.nextElementSibling;
                        if (feedback && feedback.classList.contains('invalid-feedback')) {
                          `1-


                              feedback.style.display = 'none';
                        }
                    }
                }

                // Validate province and district selectors
                const provinceCode = document.querySelector('#province');
                const districtCode = document.querySelector('#district');
                const provinceName = document.querySelector('#provinceName');
                const districtName = document.querySelector('#districtName');
                
                if (provinceCode && provinceCode.hasAttribute('required') && provinceCode.value === '') {
                    setValidationVisuals(provinceCode, false, 'Province is required');
                    isFormValid = false;
                } else if (provinceCode && provinceCode.value !== '') {
                    // Ensure the province name is set in the hidden field
                    if (provinceName) {
                        const selectedOption = provinceCode.options[provinceCode.selectedIndex];
                        provinceName.value = selectedOption ? selectedOption.text : '';
                    }
                    setValidationVisuals(provinceCode, true);
                }
                
                if (districtCode && districtCode.hasAttribute('required') && districtCode.value === '') {
                    setValidationVisuals(districtCode, false, 'District is required');
                    isFormValid = false;
                } else if (districtCode && districtCode.value !== '') {
                    // Ensure the district name is set in the hidden field
                    if (districtName) {
                        const selectedOption = districtCode.options[districtCode.selectedIndex];
                        districtName.value = selectedOption ? selectedOption.text : '';
                    }
                    setValidationVisuals(districtCode, true);
                }
                
                // Ensure ward name is set in the hidden field
                const wardCode = document.querySelector('#ward');
                const wardName = document.querySelector('#wardName');
                if (wardCode && wardCode.value !== '' && wardName) {
                    const selectedOption = wardCode.options[wardCode.selectedIndex];
                    wardName.value = selectedOption ? selectedOption.text : '';
                }

                if (addressDetailInput) {
                    const addressDetailValue = addressDetailInput.value.trim();
                    const isAddressDetailValid = addressDetailValue.length > 0 && addressDetailValue.length <= 500;
                    setValidationVisuals(addressDetailInput, isAddressDetailValid, 'Address details are required (max 500 chars).');
                    if (!isAddressDetailValid) isFormValid = false;
                }

                const addressTypeSelected = document.querySelector('input[name="addressType"]:checked');
                const addressTypeGroup = document.querySelector('input[name="addressType"]')?.closest('.mb-3');
                const addressTypeFeedback = addressTypeGroup?.querySelector('.invalid-feedback.d-block');

                if (!addressTypeSelected) {
                    isFormValid = false;
                    if (addressTypeFeedback) {
                        addressTypeFeedback.textContent = 'Please select an address type.';
                        addressTypeFeedback.style.display = 'block';
                    }
                    if (addressTypeGroup) {
                        Array.from(addressTypeGroup.querySelectorAll('.form-check-input')).forEach(radio => {
                            radio.classList.add('is-invalid');
                        });
                    }
                } else {
                    if (addressTypeFeedback) {
                        addressTypeFeedback.style.display = 'none';
                    }
                    if (addressTypeGroup) {
                        Array.from(addressTypeGroup.querySelectorAll('.form-check-input')).forEach(radio => {
                            radio.classList.remove('is-invalid');
                        });
                    }
                }

                if (!isFormValid) {
                    event.preventDefault();
                    const firstInvalidField = document.querySelector('.is-invalid, input.is-invalid, textarea.is-invalid, select.is-invalid');
                    if (firstInvalidField) {
                        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstInvalidField.focus();
                    }
                }
            });
        }
    });
</script>

<!-- Chat Widget - Buyers Only -->
<div th:replace="~{fragments/chat-widget :: buyer-chat-widget}"></div>

</body>
</html>