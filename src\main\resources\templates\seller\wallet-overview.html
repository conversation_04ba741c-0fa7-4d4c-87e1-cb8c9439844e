<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Wallet - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Page-specific styles for wallet */
        .wallet-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .wallet-balance {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .wallet-actions .btn {
            margin: 0.25rem;
        }

        .transaction-item {
            border-left: 4px solid #dee2e6;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }

        .transaction-item.credit {
            border-left-color: #28a745;
        }

        .transaction-item.debit {
            border-left-color: #dc3545;
        }

        .transaction-amount.credit {
            color: #28a745;
        }

        .transaction-amount.debit {
            color: #dc3545;
        }
    </style>
</head>
<body>

<div class="topbar-wrapper" th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 mb-4 sidebar-wrapper">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9 main-content-wrapper">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title mb-0">
                        <i class="fas fa-wallet text-primary me-2"></i>My Wallet
                    </h3>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a th:href="@{/seller/dashboard}">Dashboard</a></li>
                            <li class="breadcrumb-item active">My Wallet</li>
                        </ol>
                    </nav>
                </div>

                <!-- Error Message -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span th:text="${errorMessage}"></span>
                </div>

                <!-- Success Message -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <span th:text="${successMessage}"></span>
                </div>

                <!-- Wallet Balance Card -->
                <div class="wallet-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-2">
                                <i class="fas fa-wallet me-2"></i>
                                Wallet Balance
                            </h3>
                            <div class="wallet-balance" th:text="${walletBalance.formattedBalance}">0 VND</div>
                            <p class="mb-0 opacity-75">Available for purchases and refunds</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="wallet-actions">
                                <a th:href="@{/seller/wallet/withdraw}" class="btn btn-warning btn-sm me-2">
                                    <i class="fas fa-money-bill-wave me-1"></i> Withdraw
                                </a>
                                <a th:href="@{/seller/wallet/transactions}" class="btn btn-light btn-sm">
                                    <i class="fas fa-history me-1"></i> View History
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="card">
                    <div class="card-header bg-white py-3">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">Recent Transactions</h5>
                            </div>
                            <div class="col-auto">
                                <a th:href="@{/seller/wallet/transactions}" class="btn btn-outline-primary btn-sm">
                                    View All
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div th:if="${recentTransactions.empty}" class="text-center py-4 text-muted">
                            <i class="fas fa-receipt fa-3x mb-3"></i>
                            <p class="mb-0">No transactions yet</p>
                            <small>Your wallet transactions will appear here</small>
                        </div>

                        <div th:unless="${recentTransactions.empty}">
                            <div th:each="transaction : ${recentTransactions.content}"
                                 class="transaction-item"
                                 th:classappend="${transaction.transactionType.name().toLowerCase()}">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <div class="fw-bold" th:text="${transaction.description}">Transaction Description</div>
                                        <small class="text-muted">
                                            <span th:text="${#temporals.format(transaction.createdAt, 'dd/MM/yyyy HH:mm')}">Date</span>
                                            <span th:if="${transaction.referenceType}" class="ms-2">
                                                • <span th:text="${transaction.referenceType.name()}">Reference Type</span>
                                            </span>
                                        </small>
                                    </div>
                                    <div class="col-auto">
                                        <div class="transaction-amount fw-bold"
                                             th:classappend="${transaction.transactionType.name().toLowerCase()}">
                                            <span th:if="${transaction.transactionType.name() == 'CREDIT'}">+</span>
                                            <span th:if="${transaction.transactionType.name() == 'DEBIT'}">-</span>
                                            <span th:text="${#numbers.formatDecimal(transaction.amount, 0, 'COMMA', 0, 'POINT')} + ' VND'">Amount</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </main>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>
