<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Details - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
    </style>
</head>
<body>

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar(activeMenu='users')}"></div>
        </div>

        <div class="col-lg-9">
            <main th:if="${user != null}">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="section-title mb-0">User Profile</h2>
                    <div>
                        <a th:href="@{/admin/users}" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i>Back to List</a>
                        <a th:href="@{'/admin/users/edit/' + ${user.userId}}" class="btn btn-primary"><i class="fas fa-edit me-2"></i>Edit User</a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-4">
                        <div class="card shadow-sm mb-4">
                            <div class="card-body text-center">
                                <img th:src="${user.profilePicUrl != null ? user.profilePicUrl : '/images/default-avatar.png'}"
                                     alt="User Avatar" class="profile-image mb-3">
                                <h4 class="mb-1" th:text="${user.fullName}"></h4>
                                <p class="text-muted mb-2" th:text="${user.email}"></p>
                                <div>
                                    <span th:each="userRole : ${user.userRoles}"
                                          class="badge rounded-pill me-1" th:text="${userRole.role.roleName}"
                                          th:classappend="${userRole.role.roleName == 'ADMIN' ? 'bg-danger' : 'bg-primary'}"></span>
                                </div>
                            </div>
                            <ul class="list-group list-group-flush p-3">
                                <li class="list-group-item">
                                    <strong>Phone</strong>
                                    <span class="text-secondary" th:text="${user.phone ?: 'N/A'}"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Date of Birth</strong>
                                    <span class="text-secondary" th:text="${user.dateOfBirth != null ? #temporals.format(user.dateOfBirth, 'dd / MM / yyyy') : 'N/A'}"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong>Gender</strong>
                                    <span class="text-secondary" th:switch="${user.gender}">
                                        <span th:case="0">Male</span>
                                        <span th:case="1">Female</span>
                                        <span th:case="*">Other</span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-8">
                        <div class="card shadow-sm">
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="detailsTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="addresses-tab" data-bs-toggle="tab" data-bs-target="#addresses-tab-pane" type="button">Addresses</button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="orders-tab" data-bs-toggle="tab" data-bs-target="#orders-tab-pane" type="button">Order History</button>
                                    </li>
                                </ul>
                                <div class="tab-content pt-3" id="detailsTabContent">
                                    <div class="tab-pane fade show active" id="addresses-tab-pane" role="tabpanel">
                                        <h5>Saved Addresses (<span th:text="${user.addresses != null ? #lists.size(user.addresses) : 0}"></span>)</h5>
                                        <hr>
                                        <div th:if="${user.addresses == null or #lists.isEmpty(user.addresses)}" class="alert alert-light">
                                            This user has not saved any addresses.
                                        </div>
                                        <div th:each="address, iterStat : ${user.addresses}" class="p-3 mb-2 border rounded bg-light">
                                            <h6>Address <span th:text="${iterStat.count}"></span></h6>
                                            <p class="mb-1" th:text="${address.addressDetail}"></p>
                                            <p class="mb-0 text-muted" th:text="${address.ward + ', ' + address.district + ', ' + address.province}"></p>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="orders-tab-pane" role="tabpanel">
                                        <h5>Order History (<span th:text="${user.orders != null ? #lists.size(user.orders) : 0}"></span>)</h5>
                                        <hr>
                                        <div th:if="${user.orders == null or #lists.isEmpty(user.orders)}" class="alert alert-light">
                                            This user has no orders.
                                        </div>
                                        <div th:if="${user.orders != null and !#lists.isEmpty(user.orders)}" class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead><tr><th>ID</th><th>Date</th><th>Total</th><th>Status</th></tr></thead>
                                                <tbody>
                                                <tr th:each="order : ${user.orders}">
                                                    <td th:text="${order.orderId}"></td>
                                                    <td th:text="${#temporals.format(order.orderDate, 'dd-MM-yyyy')}"></td>
                                                    <td th:text="${#numbers.formatDecimal(order.totalAmount, 0, 'COMMA', 0, 'POINT')} + ' ₫'"></td>
                                                    <td><span class="badge bg-info" th:text="${order.orderStatus}"></span></td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>