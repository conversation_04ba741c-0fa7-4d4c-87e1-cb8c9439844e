<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set New Password - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome (for icons) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <style>
        /* Page Specific Styles (adapted from forgot-password.html theme) */
        body.auth-page {
            background-color: #F8F5F0; /* Light beige background */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 1rem;
            font-family: 'Open Sans', sans-serif;
        }

        .auth-container {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            text-align: center;
        }

        .auth-logo {
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2C3E50; /* Dark Teal/Slate */
            margin-bottom: 1rem;
        }
        .auth-logo a {
            text-decoration: none;
            color: inherit;
        }

        .auth-title {
            font-family: 'Lora', serif;
            font-size: 1.75rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .auth-subtitle {
            font-size: 0.95rem;
            color: #555;
            margin-bottom: 1.8rem; /* Adjusted for new password context */
            line-height: 1.6;
        }

        .form-label-auth { /* For labels above inputs */
            font-weight: 600;
            margin-bottom: 0.3rem;
            color: #495057;
            display: block; /* Make label take full width */
            text-align: left; /* Align label text left */
        }

        .form-control-auth {
            height: calc(1.5em + .75rem + 8px);
            border-radius: 0.25rem;
            font-size: 0.95rem;
            text-align: left; /* Align input text left */
            padding-left: 0.75rem; /* Ensure placeholder isn't too close to edge */
            margin-bottom: 0.25rem; /* Space below input before feedback/meter */
        }
        .form-control-auth:focus {
            border-color: #2C3E50;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .btn-auth-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: #FFFFFF;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-auth-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
            color: #FFFFFF;
        }
        .btn-auth-primary:disabled {
            background-color: #6c757d;
            border-color: #6c757d;
            opacity: 0.65;
        }

        .back-to-login-link {
            display: block;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: #555;
        }
        .back-to-login-link a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: none;
        }
        .back-to-login-link a:hover {
            text-decoration: underline;
        }

        .password-field-container {
            position: relative;
            margin-bottom: 0.25rem; /* Reduced margin as feedback is below */
        }
        .toggle-password-visibility {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            border: none;
            background: transparent;
            padding: 0.35rem 0.5rem; /* Adjusted padding for better click area */
            line-height: 1; /* Ensure icon is centered if it has weird line height */
        }
        .toggle-password-visibility:hover {
            color: #2C3E50;
        }

        /* Validation styling */
        .form-control-auth.is-invalid {
            border-color: #dc3545;
            padding-right: calc(1.5em + .75rem); /* Space for Bootstrap's default icon if it were used */
            background-repeat: no-repeat;
            background-position: right calc(.375em + .1875rem) center;
            background-size: calc(.75em + .375rem) calc(.75em + .375rem);
        }
        .form-control-auth.is-valid {
            border-color: #198754;
        }
        .invalid-feedback {
            font-size: 0.85em; /* Slightly larger for better readability */
            text-align: left;
            width: 100%; /* Ensure it takes full width */
            margin-top: .25rem;
            display: none; /* Hidden by default */
        }
        .form-control-auth.is-invalid ~ .invalid-feedback {
            display: block; /* Show feedback when input is invalid */
        }

        .password-strength-meter {
            height: 6px; /* Slightly thicker bar */
            background-color: #e9ecef;
            border-radius: 3px;
            margin-top: 0.3rem; /* Space above the meter */
            margin-bottom: 0.5rem; /* Space below meter before next field */
            overflow: hidden; /* Ensure inner div respects border-radius */
        }
        .password-strength-meter div {
            height: 100%;
            border-radius: 3px; /* Match parent */
            transition: width 0.3s ease-in-out, background-color 0.3s ease-in-out;
            width: 0%; /* Start with 0 width */
        }
        .strength-very-weak { background-color: #dc3545; width: 20%; }
        .strength-weak { background-color: #fd7e14; width: 40%; }
        .strength-medium { background-color: #ffc107; width: 60%; }
        .strength-strong { background-color: #20c997; width: 80%; }
        .strength-very-strong { background-color: #198754; width: 100%; }
    </style>
</head>
<body class="auth-page">

<main class="auth-container">
    <div class="auth-logo">
        <!-- Assuming link to home page or login page if no index.html specified -->
        <a th:href="@{/login}">ReadHub</a>
    </div>

    <h1 class="auth-title">Set New Password</h1>
    <p class="auth-subtitle">
        Create a strong and memorable password for your account.
    </p>

    <form id="newPasswordForm" novalidate>
        <div class="mb-3 text-start">
            <label for="newPassword" class="form-label-auth">New Password</label>
            <div class="password-field-container">
                <input type="password" class="form-control form-control-auth" id="newPassword" name="newPassword" placeholder="Enter new password" required>
                <button type="button" class="toggle-password-visibility" aria-label="Toggle password visibility">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
            <div class="invalid-feedback" id="newPasswordFeedback">
                Password must be at least 8 characters and meet complexity requirements.
            </div>
        </div>

        <div class="mb-4 text-start"> <!-- Increased bottom margin for spacing -->
            <label for="confirmPassword" class="form-label-auth">Confirm New Password</label>
            <div class="password-field-container">
                <input type="password" class="form-control form-control-auth" id="confirmPassword" name="confirmPassword" placeholder="Confirm new password" required>
                <button type="button" class="toggle-password-visibility" aria-label="Toggle password visibility">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
            <div class="invalid-feedback" id="confirmPasswordFeedback">
                Passwords do not match.
            </div>
        </div>

        <button type="submit" class="btn btn-auth-primary w-100 mt-2" id="submitNewPasswordBtn">Set New Password</button>
    </form>

    <div class="back-to-login-link">
        <a th:href="@{/login}"><i class="fas fa-arrow-left me-1"></i>Back to Login</a>
    </div>

</main>

<!-- Bootstrap JS Bundle (Popper.js included) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Font Awesome JS (already included, but if not using a kit, this is standard) -->
<!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script> -->

</body>
</html>