<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - BOOKIX</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS (shared style.css + page specific) -->
    <link rel="stylesheet" href="/css/style.css"> <!-- Fixed path to CSS file -->
    <style>
        /* Page Specific Styles (can be moved to style.css if preferred) */
        body.auth-page { /* More generic class for login/forgot/signup pages */
            background-color: #F8F5F0; /* Light beige background */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 1rem;
            font-family: 'Open Sans', sans-serif;
        }

        .auth-container { /* Replaces login-container for more general use */
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            text-align: center; /* Center text within the container */
        }

        .auth-logo { /* Replaces login-logo */
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2C3E50; /* Dark Teal/Slate */
            margin-bottom: 1rem;
        }
        .auth-logo a {
            text-decoration: none;
            color: inherit;
        }

        .auth-title {
            font-family: 'Lora', serif;
            font-size: 1.75rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .auth-subtitle {
            font-size: 0.95rem;
            color: #555;
            margin-bottom: 2rem;
        }

        .form-control-auth { /* Replaces form-control-login */
            height: calc(1.5em + .75rem + 8px);
            border-radius: 0.25rem;
            font-size: 0.95rem;
            text-align: left; /* Align placeholder text left */
        }
        .form-control-auth:focus {
            border-color: #2C3E50;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .btn-auth-primary { /* Replaces btn-login-primary */
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: #FFFFFF;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-auth-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
            color: #FFFFFF;
        }

        .or-separator {
            display: flex;
            align-items: center;
            text-align: center;
            color: #6c757d;
            margin: 1.25rem 0;
            font-size: 0.9rem;
        }
        .or-separator::before,
        .or-separator::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #dee2e6;
        }
        .or-separator:not(:empty)::before {
            margin-right: .5em;
        }
        .or-separator:not(:empty)::after {
            margin-left: .5em;
        }

        .create-account-section {
            margin-top: 2rem;
        }
        .btn-create-account {
            background-color: transparent;
            border: 1px solid #2C3E50;
            color: #2C3E50;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-create-account:hover {
            background-color: #2C3E50;
            color: #FFFFFF;
        }
        .back-to-login-link {
            display: block;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: #555;
        }
        .back-to-login-link a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: none;
        }
        .back-to-login-link a:hover {
            text-decoration: underline;
        }

    </style>
</head>
<body class="auth-page">

<main class="auth-container">
    <div class="auth-logo">
        <a href="/">ReadHub</a>
    </div>

    <h1 class="auth-title">Forgot Your Password?</h1>
    <p class="auth-subtitle">
        No worries! Enter your email address below and we'll send you a link to reset it.
    </p>

    <!-- Display success messages -->
    <div th:if="${successMessage}" class="alert alert-success text-center mb-3" role="alert">
        <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
    </div>

    <!-- Display error messages -->
    <div th:if="${errorMessage}" class="alert alert-danger text-center mb-3" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
    </div>

    <form th:action="@{/password-reset/forgot}" method="post">
        <div class="mb-3">
            <label for="email" class="form-label visually-hidden">Email address</label>
            <input type="email" class="form-control form-control-auth" id="email" name="email" placeholder="Enter your email address" required autofocus>
        </div>

        <button type="submit" class="btn btn-auth-primary w-100 mt-2">Reset Password</button>
    </form>

    <div class="create-account-section">
        <div class="or-separator">New to ReadHub?</div>
        <a th:href="@{/buyer/signup}" class="btn btn-create-account w-100">Create an account</a>
    </div>

    <div class="back-to-login-link">
        <a th:href="@{/buyer/login}"><i class="fas fa-arrow-left me-2"></i>Back to Login</a>
    </div>

</main>

<!-- Bootstrap JS Bundle (Popper.js included) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Font Awesome JS (if using fas fa-arrow-left) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/js/all.min.js"></script>
</body>
</html>