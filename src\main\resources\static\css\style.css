/* Global Styles */
body {
    font-family: 'Open Sans', sans-serif;
    color: #333;
    line-height: 1.6;
}

a {
    color: #3A50A0; /* Primary link color, adjust as needed */
    text-decoration: none;
}

a:hover {
    color: #2C3E50; /* Darker shade for hover */
}

.section-title {
    font-family: 'Open Sans', sans-serif;
    font-weight: 700;
    color: #212529; /* Darker heading color */
    margin-bottom: 1.5rem;
}

.view-all-link {
    font-size: 0.9rem;
    font-weight: bold;
    color: #555;
}
.view-all-link:hover {
    color: #3A50A0;
}

.bg-light {
    background-color: #f8f9fa !important; /* Standard light background */
}

/* Header Styles */
.top-banner {
    background-color: #f0f0f0; /* Light gray for top banner */
    font-size: 0.85rem;
    color: #555;
}

.main-navbar .navbar-brand.bookix-logo {
    font-family: '<PERSON>ra', serif;
    font-size: 2.2rem;
    font-weight: 700;
    color: #2C3E50 !important; /* Dark blue/charcoal for logo */
}

.main-navbar .nav-link {
    font-weight: 600;
    color: #333 !important;
    margin: 0 0.5rem;
    text-transform: uppercase;
    font-size: 0.9rem;
}
.main-navbar .nav-link:hover, .main-navbar .nav-link.active {
    color: #3A50A0 !important; /* Accent color for hover/active */
}
.main-navbar .dropdown-menu {
    border-radius: 0;
    border: 1px solid #eee;
}
.main-navbar .dropdown-item {
    font-weight: 500;
    font-size: 0.9rem;
}
.main-navbar .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #3A50A0;
}

.nav-right-actions .nav-link {
    font-size: 0.9rem; /* Smaller for action links */
    color: #555 !important;
}
.nav-right-actions .nav-link i {
    margin-right: 0.3rem;
}
.search-input-header {
    border-radius: 20px; /* Rounded search input */
    font-size: 0.9rem;
}

/* Hero Section */
.hero-section {
    /*background: url('../images/hero-background.jpg') no-repeat center center/cover; *!*/
    min-height: 70vh; /* Adjust height as needed */
    padding: 6rem 0;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-subtitle {
    font-size: 1.2rem;
    font-weight: 300;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.hero-title {
    font-family: 'Lato', sans-serif; /* Or another bold sans-serif */
    font-size: 3.5rem; /* Adjust size based on image */
    font-weight: 700; /* Bold */
    line-height: 1.2;
    margin-bottom: 2rem;
}

.btn-hero {
    background-color: #3A50A0; /* Hero button color from image */
    color: white;
    padding: 0.8rem 2rem;
    font-size: 1rem;
    font-weight: bold;
    border-radius: 5px;
    text-transform: uppercase;
}
.btn-hero:hover {
    background-color: #2C3E50; /* Darker shade for hover */
    color: white;
}

/* Features Bar */
.features-bar .feature-item h6 {
    font-size: 0.9rem;
    font-weight: 700;
    margin-top: 0.5rem;
    margin-bottom: 0.2rem;
    color: #333;
}
.features-bar .feature-item p {
    font-size: 0.8rem;
    color: #666;
}
.feature-icon {
    width: 40px; /* Adjust size as needed */
    height: 40px;
    margin-bottom: 0.5rem;
}

/* Featured Categories */
.category-card .category-img {
    border-radius: 8px; /* Softly rounded corners for images */
    transition: transform 0.3s ease;
    aspect-ratio: 3/4; /* Maintain aspect ratio */
    object-fit: cover;
}
.category-card:hover .category-img {
    transform: scale(1.05);
}
.category-card .category-name {
    text-align: center;
    font-size: 0.95rem;
    font-weight: 600;
    color: #333;
    margin-top: 0.5rem;
}
.category-card:hover .category-name {
    color: #3A50A0;
}

/* Book Card (Used in People's Choice, New Additions, Popular Books) */
.book-card {
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 4px;
    text-align: center; /* Center content in card */
    transition: box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}
.book-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
.book-card .book-cover {
    width: 100%;
    max-width: 180px; /* Control max width of book cover */
    height: auto;
    aspect-ratio: 2/3; /* Common book cover aspect ratio */
    object-fit: cover;
    margin: 0 auto 1rem; /* Center image and add bottom margin */
    padding-top: 1rem; /* Space above book cover image */
}
.book-card .book-info {
    padding: 0 1rem 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.book-card .book-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    line-height: 1.3;
    min-height: 40px; /* Ensure consistent height for titles */
}
.book-card .book-title a {
    color: #333;
}
.book-card .book-title a:hover {
    color: #3A50A0;
}
.star-rating {
    color: #ffc107; /* Bootstrap warning color for stars */
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
}
.star-rating .far.fa-star {
    color: #ccc; /* Lighter color for empty stars */
}
.book-card .book-price {
    font-size: 1rem;
    font-weight: bold;
    color: #E74C3C; /* Price color, e.g., a reddish tone */
    margin-bottom: 0;
}
.book-price .original-price {
    font-size: 0.85rem;
    color: #777;
}
.sale-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    font-size: 0.75rem;
    padding: 0.3em 0.6em;
}

/* Discount Banner */
.discount-banner {
    background-color: #FFF3CD; /* Light yellow, similar to Bootstrap warning bg */
    color: #664D03; /* Darker text for contrast */
    padding: 1.5rem 0;
}
.discount-icon {
    font-size: 1.8rem;
    font-weight: bold;
    background-color: #F7941D; /* Orange color */
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}
.discount-text {
    font-size: 1.1rem;
    font-weight: 500;
    margin-right: 1.5rem;
}
.btn-shop-deals {
    background-color: #F7941D; /* Orange color for button */
    color: white;
    border: none;
    padding: 0.6rem 1.5rem;
    font-weight: bold;
}
.btn-shop-deals:hover {
    background-color: #e6830d; /* Darker orange */
    color: white;
}

/* Best New Books of April */
.best-new-books .display-5 {
    color: #2C3E50; /* Dark blue/charcoal */
}
.best-new-books .btn-dark {
    background-color: #2C3E50;
    border-color: #2C3E50;
    padding: 0.8rem 1.8rem;
}
.best-new-books .btn-dark:hover {
    background-color: #1a252f;
    border-color: #1a252f;
}

/* Popular Books - uses .book-card styles */
/* People's Choice - uses .book-card styles */
/* New Additions - uses .book-card styles */

/* Super Books Testimonial */
.super-books-testimonial .quote-icon-superbooks {
    font-size: 4rem;
    font-family: 'Playfair Display', serif;
    color: #3A50A0; /* Accent color */
    line-height: 1;
}
.super-books-testimonial .testimonial-avatar {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.super-books-testimonial .testimonial-name {
    font-weight: 600;
    margin-top: 0.5rem;
    margin-bottom: 0.1rem;
}
.super-books-testimonial .testimonial-title {
    font-size: 0.9rem;
}

/* Favorite Authors */
.author-avatar {
    width: 100px;
    height: 100px;
    object-fit: cover;
    transition: transform 0.3s ease;
}
.author-link:hover .author-avatar {
    transform: scale(1.1);
}
.author-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    margin-top: 0.5rem;
}
.author-link:hover .author-name {
    color: #3A50A0;
}


/* From the Blog */
.blog-post-card {
    border: 1px solid #e0e0e0;
    transition: box-shadow 0.3s ease;
}
.blog-post-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}
.blog-post-card .card-img-top {
    aspect-ratio: 16/10;
    object-fit: cover;
}
.blog-post-card .blog-title {
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
}
.blog-post-card .blog-title a {
    color: #333;
}
.blog-post-card .blog-title a:hover {
    color: #3A50A0;
}
.blog-post-card .card-text.small {
    margin-bottom: 0.5rem;
}

/* Newsletter Subscription Section */
.newsletter-section {
    background-color: #2C3E50; /* Dark blue/gray */
    color: #fff;
}
.newsletter-title {
    font-weight: 600;
}
.newsletter-section .form-control {
    border-radius: 5px;
}
.btn-subscribe {
    background-color: #F7941D; /* Orange */
    color: white;
    font-weight: bold;
    border: none;
}
.btn-subscribe:hover {
    background-color: #e6830d; /* Darker orange */
    color: white;
}

/* Footer Styles */
.site-footer {
    background-color: #212529; /* Very dark gray, almost black */
    color: #adb5bd; /* Lighter text for contrast */
    font-size: 0.9rem;
}
.site-footer .footer-heading {
    font-size: 1.1rem;
    color: #fff;
    margin-bottom: 1rem;
    font-weight: 600;
}
.site-footer .footer-links li {
    margin-bottom: 0.5rem;
}
.site-footer .footer-links a {
    color: #adb5bd;
    transition: color 0.2s ease;
}
.site-footer .footer-links a:hover {
    color: #fff;
    text-decoration: none;
}
.site-footer .social-icons .social-icon {
    display: inline-block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 50%;
    background-color: #343a40; /* Slightly lighter than footer bg */
    color: #fff;
    margin-right: 0.5rem;
    transition: background-color 0.2s ease;
}
.site-footer .social-icons .social-icon:hover {
    background-color: #3A50A0; /* Accent color on hover */
}
.site-footer .footer-hr {
    border-top: 1px solid #343a40; /* Separator line color */
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
}
.payment-methods-img {
    max-height: 25px; /* Adjust as needed */
}

/* Responsive Adjustments */
@media (max-width: 991.98px) { /* Medium devices (tablets, less than 992px) */
    .main-navbar .navbar-nav { /* Center nav items on mobile toggle */
        text-align: center;
    }
    .nav-right-actions {
        width: 100%;
        justify-content: center;
        margin-top: 1rem;
    }
    .nav-right-actions .nav-link {
        padding: 0.5rem;
    }
    .hero-title {
        font-size: 2.5rem;
    }
    .people-choice-section .col-lg,
    .new-additions-section .col-lg,
    .popular-books-section .col-lg-2 { /* Make book cards stack earlier */
        flex: 0 0 auto;
        width: 50%; /* 2 cards per row on medium */
    }
}

@media (max-width: 767.98px) { /* Small devices (landscape phones, less than 768px) */
    .hero-title {
        font-size: 2rem;
    }
    .hero-section {
        min-height: 50vh;
        padding: 4rem 0;
    }
    .discount-text {
        display: block;
        margin-bottom: 0.5rem;
        margin-right: 0;
    }
    .features-bar .feature-item {
        margin-bottom: 1rem;
    }
    .people-choice-section .col-lg,
    .new-additions-section .col-lg,
    .popular-books-section .col-lg-2,
    .featured-categories .col-lg-2 {
        width: 50%; /* 2 cards per row */
    }
    .favorite-authors-section .col-lg,
    .favorite-authors-section .col-md-3 {
        width: 33.333%; /* 3 authors per row */
    }
}

@media (max-width: 575.98px) { /* Extra small devices (portrait phones, less than 576px) */
    .hero-title {
        font-size: 1.8rem;
    }
    .section-title {
        font-size: 1.5rem;
    }
    .people-choice-section .col-lg,
    .new-additions-section .col-lg,
    .popular-books-section .col-lg-2,
    .featured-categories .col-lg-2 {
        width: 100%; /* 1 card per row */
    }
    .favorite-authors-section .col-4 {
        width: 50%; /* 2 authors per row */
    }
    .newsletter-section .form-control-lg, .newsletter-section .btn-lg {
        font-size: 1rem; /* Reduce size on small screens */
        padding: 0.5rem 1rem;
    }
}


/*-----------------------------------------------------------------------------------------*/
/* style.css additions for Product List*/

/* Shop Page Header Section */
.shop-page-header-section {
    background-color: #f9f9f9; /* Light greyish background */
    /* If you have a subtle pattern image:
    background-image: url('../images/shop-banner-bg.png');
    background-repeat: repeat; */
    border-bottom: 1px solid #eee;
}

.shop-page-title {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 2.8rem;
    color: #2C3E50; /* Dark blue/charcoal from logo */
    margin-bottom: 0.5rem;
}

.shop-page-subtitle {
    font-size: 1rem;
}

.shop-page-header-section .breadcrumb-item a {
    color: #555;
    text-decoration: none;
}
.shop-page-header-section .breadcrumb-item a:hover {
    color: #3A50A0;
}
.shop-page-header-section .breadcrumb-item.active {
    color: #777;
}

.shop-category-filters-scrollable {
    padding-bottom: 10px; /* For scrollbar visibility if needed */
}

.shop-category-filter-btn {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    color: #555;
    border-radius: 25px; /* Pill shape */
    padding: 0.5rem 1.2rem;
    margin-right: 0.75rem;
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap; /* Prevent button text from wrapping */
    transition: all 0.3s ease;
}
.shop-category-filter-btn:hover,
.shop-category-filter-btn.active {
    background-color: #3A50A0; /* Accent color */
    color: #fff;
    border-color: #3A50A0;
}
.shop-category-filter-btn i {
    font-size: 0.9em; /* Slightly smaller icon */
}

/* Shop Content Section: Sidebar & Products */
.filter-widget {
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #eee;
    box-shadow: 0 2px 5px rgba(0,0,0,0.03);
}

.filter-widget-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.category-filter-list li,
.author-filter-list li {
    margin-bottom: 0.6rem;
}
.category-filter-list li a {
    color: #555;
    font-size: 0.9rem;
    text-decoration: none;
    transition: color 0.2s ease;
}
.category-filter-list li a:hover {
    color: #3A50A0;
}
.category-filter-list li a .count {
    color: #999;
    font-size: 0.8em;
}
.author-filter-list .form-check-label {
    font-size: 0.9rem;
    color: #555;
    cursor: pointer;
}
.author-filter-list .form-check-input {
    cursor: pointer;
    border-color: #ccc;
}
.author-filter-list .form-check-input:checked {
    background-color: #3A50A0;
    border-color: #3A50A0;
}

.price-filter-slider.form-range::-webkit-slider-thumb {
    background: #3A50A0;
}
.price-filter-slider.form-range::-moz-range-thumb {
    background: #3A50A0;
}
.price-filter-slider.form-range::-ms-thumb {
    background: #3A50A0;
}
.price-label {
    color: #777;
}

.rating-filter-stars .form-check-label {
    color: #ffc107; /* Star color */
    cursor: pointer;
}
.rating-filter-stars .form-check-input {
    margin-top: 0.4em; /* Align checkbox with stars better */
    cursor: pointer;
    visibility: hidden; /* Hide default radio/checkbox, style label instead or use custom icons */
    width: 0;
    height: 0;
    position: absolute;
    opacity: 0;
}
/* Style the label as the clickable element */
.rating-filter-stars .form-check-input + label {
    font-size: 1.1rem;
    padding: 0.2rem 0;
}
.rating-filter-stars .form-check-input:checked + label i.far.fa-star {
    /* Could add style for checked but currently based on actual full/half stars */
}
.rating-filter-stars .form-check-inline {
    margin-right: 0; /* Remove default spacing */
    display: block; /* Make each rating option take full width */
    margin-bottom: 0.3rem;
}


/* Product Grid Controls */
.product-grid-controls .form-select-sm {
    font-size: 0.875rem;
    border-radius: 20px;
    max-width: 200px;
}

/* Shop Book Card adjustments (if any, mostly reuses .book-card) */
.shop-book-card {
    /* Any specific styles for shop page book cards */
}

.book-card .book-cover {
    border-bottom: 1px solid #f0f0f0; /* Subtle line below image within card */
    padding-bottom: 1rem; /* Space below image if border added */
}

/* Hover effect for "Add to cart" */
.book-card-interactive .book-cover-container {
    overflow: hidden; /* To contain the hover content if it slides */
}
.book-card-interactive .book-card-hover-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 0.75rem;
    text-align: center;
    opacity: 0;
    transform: translateY(100%);
    transition: opacity 0.3s ease, transform 0.3s ease;
}
.book-card-interactive:hover .book-card-hover-content {
    opacity: 1;
    transform: translateY(0);
}
.book-card-hover-content .btn {
    width: 80%;
    border-radius: 20px;
}

/* Overlay text on book cover */
.book-cover-overlay-text {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 5px 8px;
    font-size: 0.75rem;
    font-weight: bold;
    text-align: center;
    border-radius: 3px;
    line-height: 1.2;
}


/* Pagination */
.pagination .page-item .page-link {
    color: #555;
    border-radius: 50% !important; /* Circular pagination items */
    margin: 0 0.25rem;
    width: 36px;
    height: 36px;
    line-height: 22px; /* Adjust for vertical centering */
    text-align: center;
    border-color: #e0e0e0;
}
.pagination .page-item.active .page-link {
    background-color: #2C3E50; /* Dark color for active page */
    border-color: #2C3E50;
    color: #fff;
}
.pagination .page-item.disabled .page-link {
    color: #aaa;
    background-color: #f8f9fa;
    border-color: #e0e0e0;
}
.pagination .page-item .page-link:hover {
    background-color: #f0f0f0;
    border-color: #ccc;
}
.pagination .page-item.active .page-link:hover {
    background-color: #2C3E50;
    border-color: #2C3E50;
}

/* Responsive adjustments for shop */
@media (max-width: 991.98px) { /* lg */
    .shop-page-title {
        font-size: 2.2rem;
    }
    aside.col-lg-3 {
        margin-bottom: 2rem; /* Space below sidebar on smaller screens */
    }
}

@media (max-width: 767.98px) { /* md */
    .shop-title-area .breadcrumb {
        margin-top: 0.5rem;
    }
}



/* style.css additions for Product Detail Page */

.product-detail-page .breadcrumb-item a {
    color: #555;
}
.product-detail-page .breadcrumb-item.active {
    color: #777;
}
.product-sale-badge-breadcrumb {
    font-size: 0.9rem;
    padding: 0.4em 0.8em;
}

/* Product Display Section */
.main-product-image img {
    max-height: 550px; /* Control max height */
    object-fit: contain; /* Ensure whole image is visible, or use 'cover' if preferred */
}
.product-detail-overlay { /* Specific styling for overlay on detail page if different */
    font-size: 0.8rem;
    /* Adjust position/background if needed */
}

.product-title-detail {
    font-family: 'Lato', sans-serif; /* Or Playfair for title */
    font-weight: 700;
    color: #2C3E50;
    line-height: 1.3;
}
.product-price-detail .current-price {
    color: #E74C3C; /* Price color */
}
.product-short-description {
    font-size: 0.95rem;
    line-height: 1.7;
}

.quantity-selector .form-control {
    box-shadow: none;
    height: calc(1.5em + .5rem + 2px); /* Match Bootstrap sm button height */
}
.quantity-selector .quantity-btn {
    padding: .25rem .65rem;
    line-height: 1.5;
}
.add-to-cart-btn {
    padding: 0.75rem 1.5rem;
}

.product-meta a {
    color: #3A50A0;
    text-decoration: none;
}
.product-meta a:hover {
    text-decoration: underline;
}
.social-share-icon {
    font-size: 1.1rem;
    transition: color 0.2s ease;
}
.social-share-icon:hover {
    color: #3A50A0 !important;
}

/* Product Content Details (Description, Additional Info, Reviews) */
.section-subheader-line {
    display: flex;
    align-items: center;
    text-align: left;
    color: #333;
    font-size: 1.5rem; /* Match h3 */
    font-weight: 600;
    margin-bottom: 1.5rem;
}
.section-subheader-line span {
    padding-right: 15px;
}
.section-subheader-line::after {
    content: '';
    flex-grow: 1;
    height: 1px;
    background-color: #e0e0e0; /* Line color */
}


.product-content-block ul.list-styled {
    list-style: none;
    padding-left: 0;
}
.product-content-block ul.list-styled li {
    padding-left: 1.5em;
    position: relative;
    margin-bottom: 0.5rem;
}
.product-content-block ul.list-styled li::before {
    content: "\f00c"; /* Font Awesome check icon */
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    color: #28a745; /* Green check */
}

.additional-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}
.additional-info-grid .info-item {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 5px;
}
.additional-info-grid .info-item i {
    font-size: 1.5rem;
}
.additional-info-grid .info-label {
    display: block;
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.1rem;
}
.additional-info-grid .info-value {
    font-weight: 500;
    color: #333;
}

/* Reviews Section */
.reviewer-avatar {
    width: 50px;
    height: 50px;
    object-fit: cover;
}
.reviewer-name {
    font-weight: 600;
}
.review-date {
    font-size: 0.85rem;
}
.review-text {
    font-size: 0.95rem;
    color: #555;
    line-height: 1.6;
}

.add-review-stars {
    display: inline-block; /* To contain floated elements */
}
.add-review-stars input[type="radio"] {
    display: none; /* Hide the actual radio button */
}
.add-review-stars label {
    font-size: 1.5rem; /* Size of the star icons */
    color: #ccc;      /* Default color for stars */
    cursor: pointer;
    padding: 0 0.1em;
    float: right; /* Float them to fill from right to left */
}
/* Change color of stars on hover and for selected ones */
.add-review-stars input[type="radio"]:checked ~ label, /* Color selected star and those to its left */
.add-review-stars label:hover,
.add-review-stars label:hover ~ label { /* Color stars to the left on hover */
    color: #ffc107; /* Gold color for selected/hovered stars */
}
/* Ensure stars are in correct order for LTR text */
.add-review-stars {
    direction: rtl; /* This makes the float right behave as "fill from right" */
}
.add-review-stars label:hover ~ label,
.add-review-stars input[type="radio"]:checked ~ label {
    color: #ffc107;
}

/* Image Zoom Modal */
#imageZoomModal .modal-content {
    max-height: 90vh;
}
#imageZoomModal .modal-body img {
    max-height: 85vh;
    object-fit: contain;
}



/*-------------------------------------------------------------------------------------*/
/* style.css additions for Blog Page */

.blog-page {
    background-color: #fdfdfd; /* Very light off-white background */
}

.blog-page-header .blog-main-title {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: #2C3E50; /* Dark blue/charcoal */
}
.blog-page-header .blog-main-subtitle {
    font-size: 1.1rem;
}

/* Latest Post Highlight Section */
.latest-post-card {
    background-color: #f8f9fa; /* Light background for the featured post card */
    border: none; /* Remove default card border if desired */
    border-radius: 8px;
    overflow: hidden; /* To ensure rounded corners on image */
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}
.latest-post-card .card-img-md-left {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    border-top-right-radius: 0; /* Reset for md and up */
    border-bottom-right-radius: 0; /* Reset for md and up */
}
@media (max-width: 767.98px) { /* Below md */
    .latest-post-card .card-img-md-left {
        border-bottom-left-radius: 0;
        border-top-right-radius: 8px; /* Apply to top right on smaller screens */
    }
}
.latest-post-card .latest-post-title {
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    font-size: 1.8rem; /* Larger title for featured post */
    line-height: 1.3;
}
.latest-post-card .latest-post-title a {
    color: #2C3E50;
    text-decoration: none;
}
.latest-post-card .latest-post-title a:hover {
    color: #3A50A0;
}
.latest-post-card .latest-post-excerpt {
    font-size: 0.95rem;
    line-height: 1.7;
}
.latest-post-card .read-more-link {
    color: #3A50A0;
    font-weight: bold;
    text-decoration: none;
    font-size: 0.9rem;
}
.latest-post-card .read-more-link:hover {
    text-decoration: underline;
}
.blog-post-meta a {
    text-decoration: none;
}
.blog-post-meta a:hover {
    text-decoration: underline;
}

/* Blog Post Filters & Search */
.blog-search-form .form-control {
    border-radius: 20px 0 0 20px;
}
.blog-search-form .btn {
    border-radius: 0 20px 20px 0;
}
.blog-filters-search .form-select-sm {
    font-size: 0.875rem;
    border-radius: 20px;
}


/* Blog Post Card (re-uses .blog-post-card from index/global styles) */
/* Add any blog-page specific overrides if necessary */
.blog-post-card {
    background-color: #fff; /* Ensure white background for grid cards */
    /* transition: transform 0.2s ease-in-out; */ /* Optional: subtle lift on hover */
}
/*
.blog-post-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}
*/
.blog-post-card .card-body {
    background-color: #f8f9fa; /* Light bg for text area of card */
    border-top: 1px solid #eee; /* Separator if image and body have different bg */
}
.blog-post-card .blog-post-meta {
    margin-bottom: 0.5rem;
}
.blog-post-card .blog-title {
    font-size: 1.05rem; /* Slightly smaller than featured post title */
    font-weight: 600;
}
.blog-post-card .blog-title a {
    color: #333;
}
.blog-post-card .blog-title a:hover {
    color: #3A50A0;
}

/* Pagination styles are typically global or reused from shop/other pages */



/*--------------------------------------------------------------------------------------------*/
/* style.css additions for Blog Single Page */

.blog-single-page {
    background-color: #fff; /* Clean white background for single post */
}

.blog-single-hero-image {
    max-height: 500px; /* Or your preferred max height */
    object-fit: cover;
    border-radius: 0 0 12px 12px; /* Rounded bottom corners for hero image */
}

.blog-single-header .blog-single-title {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: #2C3E50;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}
.blog-single-header .blog-single-meta {
    font-size: 0.9rem;
}
.blog-single-header .blog-single-meta a {
    text-decoration: none;
}
.blog-single-header .blog-single-meta a:hover {
    text-decoration: underline;
}

/* Blog Single Content Styling */
.blog-single-content {
    font-size: 1.05rem; /* Slightly larger base font for readability */
    line-height: 1.8;
    color: #333;
}
.blog-single-content h1, .blog-single-content h2, .blog-single-content h3,
.blog-single-content h4, .blog-single-content h5, .blog-single-content h6 {
    font-family: 'Lato', sans-serif; /* Consistent heading font */
    color: #2C3E50;
    margin-top: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}
.blog-single-content p {
    margin-bottom: 1.25rem;
}
.blog-single-content a {
    color: #3A50A0;
    text-decoration: underline;
    text-decoration-color: rgba(58, 80, 160, 0.3);
    transition: text-decoration-color 0.2s ease;
}
.blog-single-content a:hover {
    text-decoration-color: #3A50A0;
}

.custom-blockquote {
    background-color: #f8f9fa;
    border-left: 5px solid #D9A170; /* Accent color for blockquote - orange/brownish */
    color: #444;
    font-style: italic;
}
.custom-blockquote .blockquote-footer {
    font-style: normal;
    color: #555;
}

.custom-bullet-list li {
    padding-left: 1.5em;
    position: relative;
    margin-bottom: 0.5rem;
}
.custom-bullet-list li::before {
    content: "\2022"; /* Bullet character */
    font-family: inherit; /* Use default font for bullet */
    position: absolute;
    left: 0;
    color: #D9A170; /* Accent color for bullets */
    font-weight: bold;
    font-size: 1.2em; /* Make bullet slightly larger */
    line-height: 1;
}

/* Post Footer: Tags & Share */
.blog-single-footer .tag-btn {
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    color: #555;
    border-color: #ddd;
}
.blog-single-footer .tag-btn:hover {
    background-color: #eee;
    color: #333;
}
.blog-single-footer .post-share .social-share-icon {
    font-size: 1rem; /* Smaller for post footer */
}

/* Author Bio Section */
.author-bio-section {
    background-color: #f8f9fa; /* Light bg for author bio */
}
.author-bio-section .author-avatar {
    width: 80px;
    height: 80px;
    object-fit: cover;
}
.author-bio-section .author-name {
    font-weight: 600;
    color: #2C3E50;
}

/* Prev/Next Post Navigation */
.post-navigation a {
    color: #333;
    max-width: 45%; /* Prevent overlapping on small screens */
}
.post-navigation a:hover .post-nav-title {
    color: #3A50A0;
}
.post-navigation .post-nav-title {
    font-weight: 500;
    display: block; /* Ensure it takes full width for text-end on next */
    font-size: 0.95rem;
}

/* Comments Section */
.comments-section .comments-title,
.comments-section .existing-comments-title {
    font-family: 'Lato', sans-serif;
    font-weight: 600;
    color: #2C3E50;
}
.comment-form .form-control {
    background-color: #f8f9fa; /* Light background for form fields */
    border-color: #e0e0e0;
}
.comment-form .form-control:focus {
    background-color: #fff;
    border-color: #3A50A0;
    box-shadow: 0 0 0 0.2rem rgba(58, 80, 160, 0.25);
}
.comment-form .required {
    color: #E74C3C; /* Red asterisk */
}
.submit-comment-btn {
    background-color: #2C3E50;
    border-color: #2C3E50;
    padding: 0.6rem 1.5rem;
    border-radius: 25px;
}
.submit-comment-btn:hover {
    background-color: #1e2b37;
    border-color: #1e2b37;
}

.comment-item .commenter-avatar {
    width: 45px;
    height: 45px;
    object-fit: cover;
}
.comment-item .commenter-name {
    font-weight: 600;
}
.comment-item .comment-date {
    font-size: 0.8rem;
}
.comment-item .comment-text {
    font-size: 0.9rem;
    color: #444;
    line-height: 1.7;
}
.comment-item .reply-link {
    font-size: 0.8rem;
    padding: 0.2rem 0.6rem;
}
.comment-reply {
    /* Indent replies */
    /* background-color: #fbfbfb; /* Slightly different bg for replies */
    /* padding-left: 1rem; /* Add padding for visual indent */
    /* border-left: 2px solid #eee; */
}

/* Pagination for comments reuses global pagination styles */

@media (max-width: 767.98px) {
    .blog-single-title {
        font-size: 2rem; /* Adjust for smaller screens */
    }
    .post-navigation {
        flex-direction: column;
    }
    .post-navigation a {
        max-width: 100%;
        margin-bottom: 1rem;
        text-align: center !important; /* Center prev/next links */
    }
    .post-navigation .next-post-link {
        margin-bottom: 0;
    }
}





/*-------------------------------------------------------------------------------*/
/* style.css additions for About & Contact Page */

.about-contact-page .bg-light-tint {
    background-color: #fcfcfc; /* A very light grey, almost white */
}

/* About Us Section */
.about-us-section .page-subtitle-header .breadcrumb-item a,
.about-us-section .page-subtitle-header .breadcrumb-item.active {
    font-size: 0.9rem;
}

.section-title-big {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 3rem; /* Large title */
    color: #2C3E50;
    line-height: 1.2;
}
.lead-text {
    font-size: 1.1rem;
    color: #555;
    line-height: 1.7;
}
.about-us-section .btn-outline-primary {
    border-color: #3A50A0;
    color: #3A50A0;
    font-weight: 500;
}
.about-us-section .btn-outline-primary:hover {
    background-color: #3A50A0;
    color: #fff;
}

/* Tiny Changes Section */
.tiny-changes-section .section-title {
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    color: #2C3E50;
}

/* Stats Counter Section */
.stats-counter-section .stat-item .stat-number {
    font-family: 'Lato', sans-serif;
    font-size: 3rem;
    font-weight: 700;
    color: #3A50A0; /* Accent color */
    margin-bottom: 0.25rem;
}
.stats-counter-section .stat-item .stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
}

/* Super Books Testimonial & Favorite Authors - Reuse existing styles from index.css */
/* Ensure .super-books-testimonial has white background if not already set by default */
.super-books-testimonial {
    background-color: #fff; /* Matches image if bg-light-tint is around it */
}
.super-books-testimonial .star-rating { /* Added based on image */
    color: #ffc107;
    font-size: 1.2rem;
}

/* Contact Us Section (Info Part) */
.contact-us-section .contact-info-block .contact-info-title {
    font-family: 'Lato', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}
.contact-us-section .contact-link {
    color: #3A50A0;
    font-weight: 500;
    text-decoration: none;
}
.contact-us-section .contact-link:hover {
    text-decoration: underline;
}
.contact-us-section .contact-social-icons .social-icon {
    display: inline-block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 50%;
    background-color: #f0f0f0; /* Light grey background for icons */
    color: #555; /* Icon color */
    transition: background-color 0.2s ease, color 0.2s ease;
    font-size: 0.9rem;
}
.contact-us-section .contact-social-icons .social-icon:hover {
    background-color: #3A50A0;
    color: #fff;
}

/* Contact Form Section */
.contact-form-section .section-title {
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    color: #2C3E50;
}
.contact-form-section .form-control {
    background-color: #fff; /* White background for form fields */
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
}
.contact-form-section .form-control:focus {
    border-color: #3A50A0;
    box-shadow: 0 0 0 0.2rem rgba(58, 80, 160, 0.25);
}
.submit-contact-btn {
    background-color: #2C3E50;
    border-color: #2C3E50;
    padding: 0.75rem 2.5rem;
    font-weight: 500;
    border-radius: 25px; /* Rounded button */
}
.submit-contact-btn:hover {
    background-color: #1e2b37;
    border-color: #1e2b37;
}

@media (max-width: 991.98px) { /* lg */
    .section-title-big {
        font-size: 2.5rem;
    }
}
@media (max-width: 767.98px) { /* md */
    .section-title-big {
        font-size: 2.2rem;
        text-align: center; /* Center titles on smaller screens */
    }
    .about-us-section .lead-text,
    .tiny-changes-section p {
        text-align: center;
    }
    .about-us-section .text-lg-start,
    .contact-us-section .col-lg-6 { /* Center content in columns on small screens */
        text-align: center;
    }
    .about-us-section .btn-outline-primary {
        display: block;
        width: fit-content;
        margin: 1rem auto 0;
    }
    .contact-us-section .contact-social-icons {
        justify-content: center;
        display: flex; /* Center social icons */
    }
}



/*------------------------------------------------------------------------------------------------*/
/* style.css additions for Terms & Policy Page */

.terms-policy-page {
    background-color: #fdfdfd; /* Very light off-white or white background */
}

.terms-policy-page .page-header .page-main-title {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: #2C3E50; /* Dark blue/charcoal */
}
.terms-policy-page .page-header .page-main-subtitle {
    font-size: 0.95rem;
}

.terms-policy-content {
    font-size: 1rem;      /* Base font size for readability */
    line-height: 1.7;     /* Generous line height */
    color: #333;          /* Standard text color */
}

.terms-policy-content .section-title-legal {
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    color: #2C3E50;
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e0e0e0; /* Subtle separator for main sections */
    font-size: 1.8rem; /* h2 equivalent */
}
.terms-policy-content .section-title-legal:first-child {
    margin-top: 0; /* Remove top margin for the very first section title */
}

.terms-policy-content .subsection-title-legal {
    font-family: 'Lato', sans-serif;
    font-weight: 600;
    color: #333; /* Slightly less prominent than main section titles */
    margin-top: 1.8rem;
    margin-bottom: 0.8rem;
    font-size: 1.4rem; /* h3 equivalent */
}

.terms-policy-content p {
    margin-bottom: 1.2rem;
}

.terms-policy-content ul,
.terms-policy-content ol {
    margin-bottom: 1.2rem;
    padding-left: 1.8rem; /* Indent lists */
}

.terms-policy-content ul li,
.terms-policy-content ol li {
    margin-bottom: 0.5rem;
}

.terms-policy-content a {
    color: #3A50A0; /* Standard link color */
    text-decoration: underline;
}
.terms-policy-content a:hover {
    text-decoration: none;
}

/* Optional: Style for "Last Updated" date if you want it to stand out a bit */
.page-main-subtitle .last-updated-date { /* If you wrap the date span with this class */
    font-weight: 500;
    color: #555;
}





/* style.css additions for Account Page */

.account-page {
    /* bg-light is already applied in HTML for overall page */
}

.account-sidebar .card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}
.account-sidebar .list-group-item {
    border-left: none;
    border-right: none;
    padding: 0.9rem 1.25rem;
}
.account-sidebar .list-group-item a {
    color: #333;
    font-weight: 500;
    transition: color 0.2s ease;
}
.account-sidebar .list-group-item a:hover {
    color: #3A50A0; /* Bookix primary color */
}
.account-sidebar .list-group-item.active {
    background-color: #e9ecef; /* Light grey for active */
    border-left: 3px solid #3A50A0; /* Accent border for active */
    padding-left: calc(1.25rem - 3px);
}
.account-sidebar .list-group-item.active a {
    color: #3A50A0;
    font-weight: bold;
}
.account-sidebar .list-group-item i {
    color: #777; /* Icon color */
    transition: color 0.2s ease;
}
.account-sidebar .list-group-item:hover i,
.account-sidebar .list-group-item.active i {
    color: #3A50A0;
}

.account-content .card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}
.account-content .card-header {
    border-bottom: 1px solid #f0f0f0;
}
.account-content .card-header h4 {
    color: #2C3E50; /* Dark title color */
    font-weight: 600;
}
.account-content .section-form-title {
    font-weight: 600;
    color: #444;
    margin-bottom: 1rem !important; /* Ensure spacing */
    padding-bottom: 0.5rem;
    border-bottom: 1px dashed #eee;
}

.avatar-edit-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}
.avatar-edit-btn:hover {
    background-color: #fff;
}
.avatar-edit-btn img {
    width: 14px;
    height: 14px;
}

.dob-select {
    /* Flex grow to fill space equally if needed, or set specific widths */
}

.account-content .form-label {
    font-weight: 500;
    color: #555;
    font-size: 0.9rem;
}
.account-content .form-control,
.account-content .form-select {
    font-size: 0.95rem;
}
.account-content .btn-outline-primary {
    border-color: #3A50A0;
    color: #3A50A0;
}
.account-content .btn-outline-primary:hover {
    background-color: #3A50A0;
    color: #fff;
}
.account-content .btn-primary {
    background-color: #3A50A0;
    border-color: #3A50A0;
}
.account-content .btn-primary:hover {
    background-color: #2C3E50;
    border-color: #2C3E50;
}

.address-saved-header {
    background-color: #e9f5fe; /* Light blue background */
    color: #333; /* Text color */
    padding: 0.75rem 1rem; /* Padding inside the box */
    border-radius: 5px; /* Slightly rounded corners */
    margin-bottom: 1.5rem; /* Space below the header */
    font-weight: 600; /* Make text bold */
}

.address-item {
    /* Additional styling for address blocks if needed */
    /* e.g., padding-top or border-top if border-bottom is not used */
}

.address-type-badge {
    background-color: #d0e9ff; /* Light blue badge color */
    color: #0a58ca; /* Dark blue text color (Bootstrap --bs-link-color or custom) */
    padding: 0.25em 0.5em; /* Padding inside the badge */
    border-radius: 0.25rem; /* Small border radius */
    font-size: 0.75rem; /* Smaller font size */
    font-weight: 500; /* Medium font weight */
    vertical-align: middle; /* Align vertically with text */
    white-space: nowrap; /* Prevent wrapping */
}

.address-item .text-end a {
    /* Style for edit/delete links */
    font-size: 0.9rem;
}
.address-item .text-end a.text-danger {
    /* Specific style for delete link */
    /* Ensure color is correct */
    color: #dc3545 !important; /* Bootstrap danger color */
}
.address-item .text-end a.text-danger:hover {
     color: #c82333 !important; /* Darker on hover */
}

/* Adjust spacing for last address item if no border */
.address-list .address-item:last-child {
    border-bottom: none !important;
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
}

/* Order Status Styles */
.order-status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 500;
}
.status-pending { background-color: #fff3cd; color: #856404; }
.status-processing { background-color: #cce5ff; color: #004085; }
.status-shipped { background-color: #d4edda; color: #155724; }
.status-delivered { background-color: #c3e6cb; color: #155724; }
.status-cancelled { background-color: #f8d7da; color: #721c24; }