<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop Information - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .shop-logo {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 8px;
        }
        .shop-cover {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .info-group {
            margin-bottom: 1rem;
        }
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
        .info-value {
            font-size: 1rem;
            margin-top: 0.25rem;
        }
        .section-divider {
            margin: 2rem 0;
            border-top: 1px solid #eee;
        }
        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 1.5rem;
        }
        .breadcrumb-item a {
            color: #2C3E50;
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- Include the common header -->
    <div th:replace="fragments/seller-topbar :: seller-topbar"></div>
    
    <div class="container my-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item"><a th:href="@{/seller/dashboard}">Seller Dashboard</a></li>
                <li class="breadcrumb-item active">Shop Information</li>
            </ol>
        </nav>
        
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 mb-4">
                <div th:replace="~{fragments/seller-sidebar :: seller-sidebar}"></div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Alert Messages -->
                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                
                <!-- Shop Information Status Banner -->
                <div th:if="${shop.shopId != null}" class="mb-4">
                    <div th:class="${'alert ' + 
                        (shop.approvalStatus == T(com.example.isp392.model.Shop.ApprovalStatus).APPROVED ? 'alert-success' : 
                        (shop.approvalStatus == T(com.example.isp392.model.Shop.ApprovalStatus).PENDING ? 'alert-warning' : 
                        (shop.approvalStatus == T(com.example.isp392.model.Shop.ApprovalStatus).REJECTED ? 'alert-danger' : 
                        'alert-secondary')))}">
                        <h5 class="mb-1">
                            <i th:class="${'me-2 fas ' + 
                                (shop.approvalStatus == T(com.example.isp392.model.Shop.ApprovalStatus).APPROVED ? 'fa-check-circle' : 
                                (shop.approvalStatus == T(com.example.isp392.model.Shop.ApprovalStatus).PENDING ? 'fa-clock' : 
                                (shop.approvalStatus == T(com.example.isp392.model.Shop.ApprovalStatus).REJECTED ? 'fa-times-circle' : 
                                'fa-info-circle')))}"></i>
                            Shop Status: <strong th:text="${shop.approvalStatus}">PENDING</strong>
                        </h5>
                        
                        <div th:if="${shop.reasonForStatus != null && !shop.reasonForStatus.isEmpty()}" class="mt-2 ps-4">
                            <strong>Reason:</strong> <span th:text="${shop.reasonForStatus}"></span>
                        </div>
                    </div>
                </div>

                <!-- Shop Information Display -->
                <div class="account-container">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3 class="section-title mb-0">Shop Information</h3>
                        <a th:href="@{/seller/shop-information/edit}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i> Edit Shop
                        </a>
                    </div>

                    <!-- Shop Cover Image -->
                    <div th:if="${shop.coverImageUrl != null}" class="text-center mb-4">
                        <img th:src="${shop.coverImageUrl}" alt="Shop Cover" class="shop-cover">
                    </div>
                    <div th:unless="${shop.coverImageUrl != null}" class="text-center mb-4">
                        <div class="shop-cover d-flex align-items-center justify-content-center bg-light">
                            <span class="text-muted">No cover image available</span>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <!-- Shop Logo -->
                        <div class="col-md-3 text-center mb-3 mb-md-0">
                            <div th:if="${shop.logoUrl != null}">
                                <img th:src="${shop.logoUrl}" alt="Shop Logo" class="shop-logo mb-2">
                            </div>
                            <div th:unless="${shop.logoUrl != null}" class="shop-logo mb-2 d-flex align-items-center justify-content-center bg-light">
                                <i class="fas fa-store text-muted fa-3x"></i>
                            </div>
                        </div>

                        <!-- Basic Shop Information -->
                        <div class="col-md-9">
                            <h4 th:text="${shop.shopName}" class="mb-3">Shop Name</h4>
                            <p th:text="${shop.description != null && !shop.description.isEmpty() ? shop.description : 'No description available.'}" class="mb-3 text-muted">
                                Shop description here.
                            </p>
                        </div>
                    </div>

                    <div class="section-divider"></div>

                    <!-- Shop Address -->
                    <h5 class="mb-3">Shop Address</h5>
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="info-group">
                                <div class="info-label">Full Address</div>
                                <div class="info-value">
                                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                    <span th:text="${shop.shopDetailAddress}">123 Main Street</span>,
                                    <span th:text="${shop.shopWard}">Ward</span>,
                                    <span th:text="${shop.shopDistrict}">District</span>,
                                    <span th:text="${shop.shopProvince}">Province</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section-divider"></div>

                    <!-- Contact Information -->
                    <h5 class="mb-3">Contact Information</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <div class="info-label">Email</div>
                                <div class="info-value">
                                    <i class="fas fa-envelope me-2 text-primary"></i>
                                    <span th:text="${shop.contactEmail}"><EMAIL></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <div class="info-label">Phone</div>
                                <div class="info-value">
                                    <i class="fas fa-phone me-2 text-primary"></i>
                                    <span th:text="${shop.contactPhone}">0*********</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section-divider"></div>

                    <!-- Business Information -->
                    <h5 class="mb-3">Business Information</h5>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-group">
                                <div class="info-label">Tax Code</div>
                                <div class="info-value">
                                    <i class="fas fa-file-invoice me-2 text-primary"></i>
                                    <span th:text="${shop.taxCode}">*********</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-group">
                                <div class="info-label">Identification Document</div>
                                <div class="info-value">
                                    <i class="fas fa-id-card me-2 text-primary"></i>
                                    <span th:if="${shop.identificationFileUrl != null}" class="badge bg-success">Document Uploaded</span>
                                    <span th:unless="${shop.identificationFileUrl != null}" class="badge bg-danger">Not Uploaded</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Include the common footer -->
    <div th:replace="~{fragments/footer :: footer}"></div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>