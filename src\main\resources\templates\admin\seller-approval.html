<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seller Approval Queue - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        .reason-text {
            font-style: italic;
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body class="admin-body">

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar(activeMenu='seller-approval')}"></div>
        </div>
        <div class="col-lg-9">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title mb-0">Seller Approval Queue</h3>
                    <button class="btn btn-outline-dark" type="button" data-bs-toggle="collapse" data-bs-target="#rejectionHistoryCollapse" aria-expanded="false" aria-controls="rejectionHistoryCollapse">
                        <i class="fas fa-history me-2"></i>View Rejection History
                    </button>
                </div>

                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i> <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead>
                                <tr>
                                    <th>Shop Name</th> <th>Owner</th> <th>Contact Email</th> <th>Contact Phone</th> <th>Requested At</th> <th class="text-center">Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:if="${#lists.isEmpty(pendingShops)}">
                                    <td colspan="6" class="text-center p-4 text-muted">No pending seller applications.</td>
                                </tr>
                                <tr th:each="shop : ${pendingShops}">
                                    <td th:text="${shop.shopName}"></td>
                                    <td th:text="${shop.user.fullName}"></td>
                                    <td th:text="${shop.contactEmail}"></td>
                                    <td th:text="${shop.contactPhone}"></td>
                                    <td th:text="${#temporals.format(shop.requestAt, 'dd-MM-yyyy HH:mm')}"></td>
                                    <td class="text-center">
                                        <a th:href="@{'/admin/shops/detail/' + ${shop.shopId}}" class="btn btn-sm btn-outline-secondary" title="View Details"><i class="fas fa-eye"></i></a>
                                        <form th:action="@{'/admin/seller-approvals/approve/' + ${shop.shopId}}" method="post" class="d-inline">
                                            <input type="hidden" name="returnUrl" th:value="@{/admin/seller-approvals}">
                                            <button type="submit" class="btn btn-sm btn-outline-success" title="Approve" onclick="return confirm('Are you sure you want to approve this shop?');"><i class="fas fa-check"></i></button>
                                        </form>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="Reject"
                                                th:attr="data-bs-shop-id=${shop.shopId}, data-bs-shop-name=${shop.shopName}"
                                                data-bs-toggle="modal" data-bs-target="#rejectModal">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="collapse mt-4" id="rejectionHistoryCollapse">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Rejection History</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead>
                                    <tr>
                                        <th>Shop Name</th>
                                        <th>Owner</th>
                                        <th>Rejected By (Admin)</th>
                                        <th>Rejected At</th>
                                        <th>Reason for Rejection</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr th:if="${#lists.isEmpty(rejectedShops)}">
                                        <td colspan="5" class="text-center p-4 text-muted">No rejected seller history found.</td>
                                    </tr>
                                    <tr th:each="shop : ${rejectedShops}" th:with="history=${!#lists.isEmpty(shop.approvalHistory) ? shop.approvalHistory[0] : null}">
                                        <td th:text="${shop.shopName}"></td>
                                        <td th:text="${shop.user.fullName}"></td>
                                        <td th:text="${history?.processedBy?.fullName} ?: 'N/A'"></td>
                                        <td th:text="${history != null ? #temporals.format(history.processedAt, 'dd-MM-yyyy HH:mm') : 'N/A'}"></td>
                                        <td><p class="mb-0 reason-text" th:text="${history?.reason}"></p></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </main>
        </div>
    </div>
</div>

<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="rejectForm" method="post">
                <div class="modal-header"><h5 class="modal-title" id="rejectModalLabel">Reject Application</h5><button type="button" class="btn-close" data-bs-dismiss="modal"></button></div>
                <div class="modal-body">
                    <input type="hidden" name="returnUrl" th:value="@{/admin/seller-approvals}">
                    <p>Rejecting shop: <strong id="shopNameToReject"></strong></p>
                    <div class="mb-3"><label for="rejectionReason" class="form-label">Reason (required):</label><textarea class="form-control" id="rejectionReason" name="reason" rows="3" required></textarea></div>
                </div>
                <div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button><button type="submit" class="btn btn-danger">Confirm Rejection</button></div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
    const rejectModal = document.getElementById('rejectModal');
    if(rejectModal) {
        rejectModal.addEventListener('show.bs.modal', event => {
            const button = event.relatedTarget;
            const shopId = button.getAttribute('data-bs-shop-id');
            const shopName = button.getAttribute('data-bs-shop-name');
            const form = rejectModal.querySelector('#rejectForm');
            if(form) form.action = '/admin/seller-approvals/reject/' + shopId;
            const shopNameSpan = rejectModal.querySelector('#shopNameToReject');
            if(shopNameSpan) shopNameSpan.textContent = shopName;
        });
    }
</script>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

</body>
</html>