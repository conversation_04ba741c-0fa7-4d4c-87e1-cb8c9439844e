<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đặt hàng thành công - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="py-5 bg-light">
    <div class="container my-5">
        <div class="text-center">
            <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
            <h2 class="mt-4">Đặt hàng thành công!</h2>
            <p class="lead">Cảm ơn bạn đã mua sắm tại ReadHub</p>
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">Thông tin đơn hàng #<span th:text="${customerOrder.customerOrderId}"></span></h5>
                    <div class="row mt-3">
                        <div class="col-md-6 text-start">
                            <p><strong>Ngày đặt hàng:</strong> <span th:text="${#temporals.format(customerOrder.createdAt, 'dd/MM/yyyy HH:mm')}"></span></p>
                            <p><strong>Phương thức thanh toán:</strong> <span th:text="${customerOrder.paymentMethod}"></span></p>
                            <p><strong>Trạng thái thanh toán:</strong> <span th:text="${customerOrder.paymentStatus}"></span></p>
                        </div>
                        <div class="col-md-6 text-start">
                            <p><strong>Tổng tiền hàng:</strong> <span th:text="${#numbers.formatDecimal(customerOrder.totalAmount - customerOrder.shippingFee + customerOrder.discountAmount, 0, 'COMMA', 0, 'POINT')} + ' VNĐ'"></span></p>
                            <p><strong>Phí vận chuyển:</strong> <span th:text="${#numbers.formatDecimal(customerOrder.shippingFee, 0, 'COMMA', 0, 'POINT')} + ' VNĐ'"></span></p>
                            <p th:if="${customerOrder.discountAmount != null && customerOrder.discountAmount > 0}">
                                <strong>Giảm giá<span th:if="${customerOrder.promotionCode != null && !customerOrder.promotionCode.isEmpty()}"
                                                     th:text="' (' + ${customerOrder.promotionCode} + ')'"></span>:</strong>
                                <span class="text-success" th:text="'-' + ${#numbers.formatDecimal(customerOrder.discountAmount, 0, 'COMMA', 0, 'POINT')} + ' VNĐ'"></span>
                            </p>
                            <p><strong>Tổng thanh toán:</strong> <span th:text="${#numbers.formatDecimal(customerOrder.totalAmount, 0, 'COMMA', 0, 'POINT')} + ' VNĐ'"></span></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <a href="/buyer/orders" class="btn btn-primary me-2">Xem đơn hàng của tôi</a>
                <a href="/buyer/home" class="btn btn-outline-primary">Tiếp tục mua sắm</a>
            </div>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>