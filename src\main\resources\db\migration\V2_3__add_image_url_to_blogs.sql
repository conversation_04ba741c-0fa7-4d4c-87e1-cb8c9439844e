-- Add image_url column to blogs table
-- This column stores the URL of the blog post's featured image

USE isp392

-- Check if image_url column exists, if not add it
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'blogs' AND COLUMN_NAME = 'image_url'
)
BEGIN
    ALTER TABLE blogs ADD image_url NVARCHAR(255) NULL;
    
    PRINT 'Added image_url column to blogs table';
END
ELSE
BEGIN
    PRINT 'image_url column already exists in blogs table';
END

-- Add comment to document the new column
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'URL of the blog post featured image', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'blogs', 
    @level2type = N'COLUMN', @level2name = N'image_url';
