<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category Details - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .sticky-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
        }
        .back-button {
            color: #2C3E50;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .back-button:hover {
            color: #1e2b37;
        }
        .back-button i {
            margin-right: 0.5rem;
        }
        .form-label {
            font-weight: 600;
            color: #2C3E50;
        }
        .category-info {
            background-color: #fff;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
        }
        .category-info h4 {
            color: #2C3E50;
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }
        .info-group {
            margin-bottom: 1.5rem;
        }
        .info-label {
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .info-value {
            color: #2C3E50;
        }
        .edit-buttons {
            margin-top: 2rem;
            display: flex;
            gap: 1rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body>
    <!-- Include Topbar -->
    <div th:replace="fragments/admin-topbar :: admin-topbar"></div>

    <div class="container my-4">
        <div class="row">
            <!-- Include Sidebar -->
            <div class="col-lg-3 mb-4">
                <div th:replace="fragments/admin-sidebar :: admin-sidebar(activeMenu='categories')"></div>
            </div>
            
            <div class="col-lg-9">
                <!-- Success and Error Messages -->
                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show sticky-alert" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show sticky-alert" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span th:text="${errorMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- Back Button -->
                <a href="/admin/categories" class="back-button">
                    <i class="fas fa-arrow-left"></i> Back to Categories
                </a>

                <!-- Category Details -->
                <div class="category-info">
                    <h4>Category Details</h4>
                    <form th:action="@{/admin/categories/{id}(id=${category.categoryId})}" method="post" id="categoryForm">
                        <div class="info-group">
                            <label class="info-label">Category ID</label>
                            <div class="info-value" th:text="${category.categoryId}"></div>
                        </div>
                        
                        <div class="info-group">
                            <label for="categoryName" class="info-label">Category Name</label>
                            <input type="text" class="form-control" id="categoryName" name="categoryName" 
                                   th:value="${category.categoryName}" readonly required>
                        </div>
                        
                        <div class="info-group">
                            <label for="categoryDescription" class="info-label">Description</label>
                            <textarea class="form-control" id="categoryDescription" name="categoryDescription" 
                                      rows="3" readonly required th:text="${category.categoryDescription}"></textarea>
                        </div>
                        
                        <div class="info-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="isActive" name="active"
                                       th:checked="${category.active}" disabled>
                                <label class="form-check-label" for="isActive">Active</label>
                            </div>
                        </div>
                        
                        <div class="edit-buttons">
                            <button type="button" class="btn btn-primary" onclick="enableEditing()">
                                <i class="fas fa-edit me-2"></i>Edit
                            </button>
                            <button type="submit" class="btn btn-success d-none" id="saveButton">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                            <button type="button" class="btn btn-secondary d-none" id="cancelButton" onclick="cancelEditing()">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Store original values
        let originalName = '';
        let originalDescription = '';
        let originalActive = false;

        function enableEditing() {
            // Store original values
            originalName = document.getElementById('categoryName').value;
            originalDescription = document.getElementById('categoryDescription').value;
            originalActive = document.getElementById('isActive').checked;

            // Enable form fields
            document.getElementById('categoryName').readOnly = false;
            document.getElementById('categoryDescription').readOnly = false;

            // MODIFICATION: Enable the checkbox so its value is submitted
            document.getElementById('isActive').disabled = false;

            // Show/hide buttons
            document.querySelector('.btn-primary').classList.add('d-none');
            document.getElementById('saveButton').classList.remove('d-none');
            document.getElementById('cancelButton').classList.remove('d-none');
        }

        function cancelEditing() {
            // Restore original values
            document.getElementById('categoryName').value = originalName;
            document.getElementById('categoryDescription').value = originalDescription;
            document.getElementById('isActive').checked = originalActive;

            // Disable form fields
            document.getElementById('categoryName').readOnly = true;
            document.getElementById('categoryDescription').readOnly = true;

            // MODIFICATION: Disable the checkbox again on cancel
            document.getElementById('isActive').disabled = true;

            // Show/hide buttons
            document.querySelector('.btn-primary').classList.remove('d-none');
            document.getElementById('saveButton').classList.add('d-none');
            document.getElementById('cancelButton').classList.add('d-none');
        }
    </script>

    <div th:replace="~{fragments/footer :: footer}"></div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>