<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Address Management - Bookix</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        /* Custom styles for address management */
        .address-type-badge {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 0.75rem;
            padding: 2px 8px;
        }
        .address-default-badge {
            background-color: #e8f4ff;
            border: 1px solid #b8daff;
            color: #0d6efd;
            border-radius: 4px;
            font-size: 0.75rem;
            padding: 2px 8px;
        }
        .address-saved-header {
            background-color: #d4edda;
            color: #155724;
            padding: 10px 15px;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="account-page py-5 bg-light">
    <div class="container">
        <!-- Breadcrumb navigation -->
        <nav aria-label="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item"><a th:href="@{/buyer/account-info}">Account Information</a></li>
                <li class="breadcrumb-item active" aria-current="page">Addresses</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Account Sidebar using fragment -->
            <!-- Passing 'address' to the sidebar fragment to highlight the correct link -->
            <div th:replace="~{fragments/buyer-account-sidebar :: sidebar('address')}"></div>

            <!-- Account Content - Address Management -->
            <section class="col-lg-9 account-content">
                <div class="card">
                    <!-- Card header with title and add button -->
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Addresses</h4>
                        <a th:href="@{/buyer/addresses/new}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-1"></i> Add New Address
                        </a>
                    </div>
                    <div class="card-body p-4">

                        <!-- Success message -->
                        <div th:if="${successMessage}" class="address-saved-header mb-4">
                            <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}">Address saved.</span>
                        </div>
                        
                        <!-- Error message -->
                        <div th:if="${errorMessage}" class="alert alert-danger mb-4" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}">Error occurred.</span>
                        </div>

                        <!-- No addresses message -->
                        <div th:if="${#lists.isEmpty(addresses)}" class="alert alert-info mb-4" role="alert">
                            <i class="fas fa-info-circle me-2"></i> You don't have any addresses yet. 
                            <a th:href="@{/buyer/addresses/new}" class="alert-link">Add your first address</a>.
                        </div>

                        <!-- Address list -->
                        <div class="address-list">
                            <!-- Loop through user addresses -->
                            <div th:each="address : ${addresses}" class="address-item pb-3 mb-3 border-bottom d-flex justify-content-between">
                                <div class="address-details">
                                    <p class="mb-1">
                                        <!-- Recipient information -->
                                        <strong th:text="${address.recipientName}">Recipient Name</strong> | 
                                        <span th:text="${address.recipientPhone}">Phone Number</span>
                                        
                                        <!-- Address type and default badge -->
                                        <span th:if="${address.default}" class="address-default-badge ms-2">Default address</span>
                                        <span th:if="${!address.default}" th:text="${address.addressType == 0 ? 'Residential' : 'Company'}" 
                                              class="address-type-badge ms-2">Address type</span>
                                    </p>
                                    
                                    <!-- Company name if exists -->
                                    <p th:if="${address.company != null && !address.company.isEmpty()}" 
                                       class="mb-1 text-muted small">
                                        <i class="fas fa-building me-1"></i> 
                                        <span th:text="${address.company}">Company Name</span>
                                    </p>
                                    
                                    <!-- Full address -->
                                    <p class="mb-0 text-muted small" th:text="${address.getFullAddress()}">Full Address</p>
                                </div>
                                
                                <!-- Address actions (edit/delete) -->
                                <div class="address-actions text-end flex-shrink-0">
                                    <!-- Edit button -->
                                    <a th:href="@{|/buyer/addresses/edit/${address.addressId}|}" class="text-decoration-none">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>
                                    
                                    <!-- Set as default (if not already default) -->
                                    <a th:if="${!address.default}" 
                                       th:href="@{|/buyer/addresses/set-default/${address.addressId}|}" 
                                       class="text-primary ms-2 text-decoration-none">
                                        <i class="fas fa-star me-1"></i> Set as default
                                    </a>
                                    
                                    <!-- Delete button (only for non-default addresses) -->
                                    <a th:if="${!address.default}" 
                                       th:href="@{|/buyer/addresses/delete/${address.addressId}|}" 
                                       class="text-danger ms-2 text-decoration-none"
                                       onclick="return confirm('Are you sure you want to delete this address?');">
                                        <i class="fas fa-trash-alt me-1"></i> Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>
<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script th:src="@{/js/script.js}"></script>
</body>
</html>