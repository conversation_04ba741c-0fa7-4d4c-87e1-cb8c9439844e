<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="'Print Label - Order #' + ${order.orderId}">Print Label - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Page-specific styles for print label */

    /* ===== START: CSS QUAN TRỌNG CHO VIỆC IN ===== */
    .shipping-label {
        width: 15cm;
        height: 10cm;
        border: 2px solid #000;
        padding: 0.5cm;
        margin: 20px auto;
        font-family: Arial, sans-serif;
        font-size: 12pt;
        background: white;
    }

    @media print {
        /* Ẩn các phần tử không cần thiết khi in */
        .no-print {
            display: none !important;
        }

        /* Thân trang không có nền để tiết kiệm mực */
        body {
            background-color: #fff !important;
        }

        /* Ẩn toàn bộ layout trừ phần nội dung chính */
        .sidebar-wrapper, .topbar-wrapper {
             display: none !important;
        }

        /* Cho phép phần nội dung chính chiếm toàn bộ chiều rộng trang in */
        .main-content-wrapper {
            width: 100% !important;
            flex: 0 0 100% !important;
            max-width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        /* Đảm bảo chỉ có khu vực nhãn được in */
        #print-area {
            margin: 0;
        }

        .shipping-label {
            margin: 0;
            border: 2px solid #000;
        }
    }
    /* ===== END: CSS QUAN TRỌNG CHO VIỆC IN ===== */

  </style>
</head>
<body>

<div class="topbar-wrapper no-print" th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
  <div class="row">
    <div class="col-lg-3 mb-4 sidebar-wrapper no-print">
      <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
    </div>

    <div class="col-lg-9 main-content-wrapper">
      <main>
        <div class="account-container">
          <div class="text-center mb-4 no-print">
            <h3 class="section-title">Print Preview</h3>
            <p class="text-muted">Ensure your printer is configured for 10x15cm labels.</p>
            <button onclick="window.print()" class="btn btn-primary"><i class="fas fa-print me-2"></i>Print Now</button>
            <a th:href="@{/seller/orders}" class="btn btn-secondary"><i class="fas fa-arrow-left me-2"></i>Back to Orders</a>
          </div>

          <div id="print-area">
            <div class="shipping-label" th:object="${order}">
              <div style="display: flex; justify-content: space-between; border-bottom: 2px solid #000; padding-bottom: 5px; margin-bottom: 10px;">
                <div>
                  <h5 style="margin: 0; font-weight: bold;">ReadHub Delivery</h5>
                  <p style="margin-bottom: 0; font-size: 10pt;" th:text="*{'Order Date: ' + #temporals.format(orderDate, 'dd/MM/yyyy')}"></p>
                </div>
                <div style="text-align: right;">
                  <h5 style="margin: 0; font-weight: bold;">Order #<span th:text="*{orderId}"></span></h5>
                </div>
              </div>

              <div style="border-bottom: 1px dashed #ccc; padding: 8px 0; font-size: 14pt; line-height: 1.4;">
                <strong>TO: <span th:text="*{customerOrder != null ? customerOrder.recipientName : 'N/A'}"></span></strong><br>
                <span th:text="*{customerOrder != null ? customerOrder.recipientPhone : 'N/A'}"></span><br>
                <span th:text="*{customerOrder != null ? customerOrder.shippingAddressDetail : 'N/A'}"></span><br>
                <span th:text="*{customerOrder != null ? (customerOrder.shippingWard + ', ' + customerOrder.shippingDistrict + ', ' + customerOrder.shippingProvince) : 'N/A'}"></span>
              </div>

              <div style="border-bottom: 1px dashed #ccc; padding: 8px 0; font-size: 10pt;">
                <p style="margin-bottom: 0;"><strong>Note:</strong> <span th:text="*{notes != null and !notes.isEmpty() ? notes : 'No notes'}"></span></p>
                <p style="margin-bottom: 0;"><strong>Payment:</strong> <span th:text="*{customerOrder != null ? customerOrder.paymentMethod : 'N/A'}"></span> (<span style="font-weight: bold;" th:text="*{customerOrder != null ? customerOrder.paymentStatus : 'N/A'}"></span>)</p>
              </div>

              <div style="text-align: center; padding-top: 10px;">
                <img th:src="@{'https://barcode.tec-it.com/barcode.ashx?data=' + *{orderId} + '&code=Code128'}"
                     alt="Barcode" height="50"/>
                <p style="margin-bottom: 0;" th:text="*{orderId}"></p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>