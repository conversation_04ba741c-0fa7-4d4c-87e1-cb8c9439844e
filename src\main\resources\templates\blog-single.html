<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${blog.title} + ' - Bookix Blog'">Blog Title - Bookix Blog</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>

<div th:replace="~{fragments/header :: header-content}"></div>

<main class="blog-single-page pt-2 pb-5">
    <section class="blog-single-hero mb-4" th:if="${blog.imageUrl != null}">
        <img th:src="@{${blog.imageUrl}}" th:alt="${blog.title}" class="img-fluid blog-single-hero-image w-100">
    </section>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8"> <header class="blog-single-header text-center mb-4">
                <h1 class="blog-single-title display-5" th:text="${blog.title}">Blog Title</h1>
                <p class="blog-single-meta text-muted">
                    <span class="post-date" th:text="${#temporals.format(blog.createdDate, 'dd MMM, yyyy')}">Date</span>
                    <span class="mx-1">•</span>
                    by <a href="#" class="text-muted" th:text="${blog.user.fullName}">Author Name</a>
                    <span class="mx-1">•</span>
                    <span th:text="${blog.viewsCount} + ' views'">Views Count</span>
                </p>
            </header>

                <div class="post-actions text-center mb-4"
                     th:if="${#authorization.expression('isAuthenticated()') and (#authentication.principal.username == blog.user.email or #authorization.expression('hasRole(''ADMIN'')'))}">

                    <a th:href="@{/blog/edit/{id}(id=${blog.blogId})}" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-edit me-1"></i> Edit Post
                    </a>

                    <form th:action="@{/blog/delete/{id}(id=${blog.blogId})}" method="post"
                          onsubmit="return confirm('Are you sure you want to delete this post? This action cannot be undone.');"
                          style="display: inline;">

                        <button type="submit" class="btn btn-sm btn-outline-danger">
                            <i class="fas fa-trash-alt me-1"></i> Delete Post
                        </button>
                    </form>
                </div>
                <article class="blog-single-content">
                    <div th:utext="${blog.content}">
                        <p>Default blog content placeholder. This will be replaced with the actual blog content.</p>
                    </div>
                </article>

                <section class="author-bio-section my-5 p-4 rounded">
                    <div class="d-flex align-items-center">
                        <img th:src="${blog.user.profilePicUrl != null ? blog.user.profilePicUrl : 'https://via.placeholder.com/60x60/f8f9fa/6c757d?text=User'}"
                             th:alt="${blog.user.fullName}" class="author-avatar rounded-circle me-3">
                        <div>
                            <h5 class="author-name mb-0" th:text="${blog.user.fullName}">Author Name</h5>
                        </div>
                    </div>
                </section>

                <nav class="post-navigation d-flex justify-content-between my-5 py-3 border-top border-bottom">
                    <a th:if="${previousBlog != null}" th:href="@{/blog/{id}(id=${previousBlog.blogId})}" class="prev-post-link text-decoration-none">
                        <div class="text-muted small"><i class="fas fa-chevron-left me-1"></i> Previous:</div>
                        <span class="post-nav-title" th:text="${previousBlog.title}">Previous Blog Title</span>
                    </a>
                    <div th:unless="${previousBlog != null}" class="prev-post-link text-decoration-none text-muted">
                    </div>

                    <a th:if="${nextBlog != null}" th:href="@{/blog/{id}(id=${nextBlog.blogId})}" class="next-post-link text-decoration-none text-end">
                        <div class="text-muted small">Next: <i class="fas fa-chevron-right ms-1"></i></div>
                        <span class="post-nav-title" th:text="${nextBlog.title}">Next Blog Title</span>
                    </a>
                    <div th:unless="${nextBlog != null}" class="next-post-link text-decoration-none text-end text-muted">
                    </div>
                </nav>

                <section class="comments-section mb-5" id="comments">
                    <h3 class="comments-title mb-4">Leave a Reply <span class="text-muted small">(or view existing comments below)</span></h3>
                    <form class="comment-form mb-5" th:action="@{/blog/{id}/comment(id=${blog.blogId})}" method="post">
                        <p class="comment-notes small text-muted mb-3">Your email address will not be published. Required fields are marked <span class="required">*</span></p>
                        <div class="mb-3">
                            <label for="commentText" class="form-label">Comment <span class="required">*</span></label>
                            <textarea class="form-control" id="commentText" name="comment" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-dark submit-comment-btn">Post Comment</button>
                    </form>

                    <h4 class="existing-comments-title mb-4" th:text="'Comments (' + ${blog.comments.size()} + ')'">Comments (0)</h4>
                    <div class="comment-list" th:if="${!blog.comments.isEmpty()}">
                        <div class="comment-item d-flex mb-4 pb-3 border-bottom" th:each="comment : ${blog.comments}">
                            <img th:src="${comment.user.profilePicUrl != null ? comment.user.profilePicUrl : 'https://via.placeholder.com/50x50/f8f9fa/6c757d?text=User'}"
                                 th:alt="${comment.user.fullName}" class="commenter-avatar rounded-circle me-3">
                            <div class="comment-content">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="commenter-name mb-0" th:text="${comment.user.fullName}">...</h6>
                                    <span class="comment-date text-muted small" th:text="${comment.createdDate != null ? #temporals.format(comment.createdDate, 'dd MMM yyyy - hh:mm a') : 'Date unavailable'}">...</span>
                                </div>
                                <p class="comment-text mt-1 mb-2" th:text="${comment.content}">...</p>

                                <div th:if="${currentUser != null and currentUser.userId == comment.user.userId}">
                                    <form th:action="@{/blog/{blogId}/comment/{commentId}/delete(blogId=${blog.blogId}, commentId=${comment.commentId})}"
                                          method="post"
                                          onsubmit="return confirm('Are you sure you want to delete this comment?');">
                                        <button type="submit" class="btn btn-sm btn-link text-danger p-0">
                                            <i class="fas fa-trash-alt me-1"></i>Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <div class="no-comments text-center py-4" th:if="${blog.comments.isEmpty()}">
                        <i class="far fa-comment-dots fa-3x text-muted mb-3"></i>
                        <p class="mb-0">No comments yet. Be the first to share your thoughts!</p>
                    </div>
                    <nav aria-label="Comment navigation" class="mt-4 d-flex justify-content-center">
                        <ul class="pagination">
                            <li class="page-item disabled"><a class="page-link" href="#" tabindex="-1" aria-disabled="true"><i class="fas fa-chevron-left"></i></a></li>
                            <li class="page-item active" aria-current="page"><a class="page-link" href="#">1</a></li>
                            <li class="page-item"><a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a></li>
                        </ul>
                    </nav>
                </section>
            </div>
        </div>
    </div> </main>


<div th:replace="~{fragments/footer :: footer-content}"></div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz"
        crossorigin="anonymous"></script>
<script th:src="@{/js/script.js}"></script>
</body>
</html>