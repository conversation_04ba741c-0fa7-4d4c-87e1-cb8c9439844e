<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LOGIN - READHUB</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Google Sign-In Library -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <!-- Custom CSS -->
    <style>
        body.login-page {
            background-color: #F8F5F0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 1rem;
            font-family: 'Open Sans', sans-serif;
        }

        .login-container {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
        }

        .login-logo {
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2C3E50;
            text-align: center;
            margin-bottom: 1.5rem;
        }
        .login-logo a {
            text-decoration: none;
            color: inherit;
        }

        .form-control-login {
            height: calc(1.5em + .75rem + 8px);
            border-radius: 0.25rem;
            font-size: 0.95rem;
        }
        .form-control-login:focus {
            border-color: #2C3E50;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .btn-login-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: #FFFFFF;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-login-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
            color: #FFFFFF;
        }

        .btn-google-login {
            background-color: #FFFFFF;
            border: 1px solid #ced4da;
            color: #333;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-google-login:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
        }
        .btn-google-login img,
        .btn-google-login .fab {
            width: 20px;
            margin-right: 0.75rem;
        }

        .forgot-password-link {
            font-size: 0.85rem;
            color: #555;
        }
        .forgot-password-link:hover {
            color: #2C3E50;
        }

        .or-separator {
            display: flex;
            align-items: center;
            text-align: center;
            color: #6c757d;
            margin: 1.25rem 0;
            font-size: 0.9rem;
        }
        .or-separator::before,
        .or-separator::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #dee2e6;
        }
        .or-separator:not(:empty)::before {
            margin-right: .5em;
        }
        .or-separator:not(:empty)::after {
            margin-left: .5em;
        }

        .signup-link, .seller-center-link {
            font-size: 0.9rem;
            color: #555;
        }
        .signup-link a, .seller-center-link a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: none;
        }
        .signup-link a:hover, .seller-center-link a:hover {
            text-decoration: underline;
            color: #1e2b37;
        }
        .seller-center-link {
            margin-top: 2rem;
            text-align: center;
        }
    </style>
</head>
<body class="login-page">

<main class="login-container">
    <div class="login-logo">
        <a href="/">ReadHub</a>
    </div>

    <!-- Display success messages -->
    <div th:if="${successMessage}" class="alert alert-success text-center mb-3" role="alert">
        <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
    </div>

    <!-- Display logout success message -->
    <div th:if="${param.logout}" class="alert alert-success text-center mb-3" role="alert">
        <i class="fas fa-check-circle me-2"></i> You have been successfully logged out.
    </div>

    <!-- Display login error message - only shown when redirected with error param -->
    <div th:if="${param.error}" class="alert alert-danger text-center mb-3" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i> Invalid email or password. Please try again.
    </div>

    <form th:action="@{/buyer/process_login}" method="post" id="loginForm">
        <p class="text-center signup-link mb-0">
            Login to your account to continue shopping.
        </p>

        <br>

        <div class="mb-3">
            <label for="username" class="form-label visually-hidden">Email address</label>
            <input type="email" class="form-control form-control-login" id="username" name="username" placeholder="Email address" required autofocus>
            <div class="invalid-feedback">Please enter a valid email address</div>
        </div>

        <div class="mb-2">
            <label for="password" class="form-label visually-hidden">Password</label>
            <input type="password" class="form-control form-control-login" id="password" name="password" placeholder="Password" required>
            <div class="invalid-feedback">Password is required</div>
        </div>

        <div class="mb-3 text-end">
            <a th:href="@{/password-reset/forgot}" class="forgot-password-link text-decoration-none">Forgot password?</a>
        </div>

        <button type="submit" class="btn btn-login-primary w-100 mb-3">Login with Email</button>

        <div class="or-separator">or</div>

        <!-- Google login button using Spring OAuth2 with URL from application properties -->
        <a th:href="@{/oauth2/authorization/google}" class="btn btn-google-login w-100 mb-3">
            <i class="fab fa-google"></i> Sign in with Google
        </a>
    </form>

    <p class="text-center signup-link mb-0">
        Don't have an account? <a href="signup">Sign up</a>
    </p>
</main>

<div class="seller-center-link">
    <a href="/seller/">Seller Center</a>
</div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS for form validation -->
<script th:src="@{/js/login-validation.js}"></script>
</body>
</html>