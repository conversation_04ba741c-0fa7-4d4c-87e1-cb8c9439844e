<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body.auth-page {
            background-color: #F8F5F0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 1rem;
            font-family: 'Open Sans', sans-serif;
        }

        .auth-container {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            text-align: center;
        }

        .auth-logo {
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2C3E50;
            margin-bottom: 1rem;
        }
        .auth-logo a {
            text-decoration: none;
            color: inherit;
        }

        .auth-title {
            font-family: 'Lora', serif;
            font-size: 1.75rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .auth-subtitle {
            font-size: 0.95rem;
            color: #555;
            margin-bottom: 2rem;
        }

        .form-control-auth {
            height: calc(1.5em + .75rem + 8px);
            border-radius: 0.25rem;
            font-size: 0.95rem;
            text-align: left;
        }
        .form-control-auth:focus {
            border-color: #2C3E50;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .btn-auth-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: #FFFFFF;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-auth-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
            color: #FFFFFF;
        }

        .password-requirements {
            text-align: left;
            font-size: 0.85rem;
            color: #555;
            margin-bottom: 1.5rem;
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
        }
        .password-requirements ul {
            margin-bottom: 0;
            padding-left: 1.5rem;
        }

        .back-to-login-link {
            display: block;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: #555;
        }
        .back-to-login-link a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: none;
        }
        .back-to-login-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body class="auth-page">

<main class="auth-container">
    <div class="auth-logo">
        <a href="/">ReadHub</a>
    </div>

    <h1 class="auth-title">Create New Password</h1>
    <p class="auth-subtitle">
        Enter a new password for your account.
    </p>

    <!-- Display success messages -->
    <div th:if="${successMessage}" class="alert alert-success text-center mb-3" role="alert">
        <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
    </div>

    <!-- Display error messages -->
    <div th:if="${errorMessage}" class="alert alert-danger text-center mb-3" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
    </div>

    <div class="password-requirements">
        <p><strong>Password requirements:</strong></p>
        <ul>
            <li>At least 6 characters long</li>
            <li>Must include both character and number</li>
        </ul>
    </div>

    <form th:action="@{/password-reset/reset}" method="post" id="resetPasswordForm" class="needs-validation" novalidate>
        <div class="mb-3">
            <label for="newPassword" class="form-label visually-hidden">New Password</label>
            <div class="input-group">
                <input type="password" class="form-control form-control-auth" id="newPassword" name="newPassword" 
                       placeholder="New Password" required>
                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                    <i class="fas fa-eye" id="toggleIcon"></i>
                </button>
                <div class="invalid-feedback" id="newPasswordFeedback">
                    Password must be at least 6 characters long and include at least one number and one letter.
                </div>
            </div>
            <div class="password-strength mt-2" id="passwordStrengthContainer" style="display: none;">
                <div class="progress" style="height: 5px;">
                    <div id="passwordStrength" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="d-flex justify-content-between mt-1">
                    <small>Strength: <span id="strengthLevel">Weak</span></small>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="confirmPassword" class="form-label visually-hidden">Confirm Password</label>
            <div class="input-group">
                <input type="password" class="form-control form-control-auth" id="confirmPassword" name="confirmPassword" 
                       placeholder="Confirm Password" required>
                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                    <i class="fas fa-eye" id="toggleConfirmIcon"></i>
                </button>
                <div class="invalid-feedback" id="confirmPasswordFeedback">
                    Passwords do not match
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-auth-primary w-100 mt-3" id="submitButton">Reset Password</button>
    </form>

    <div class="back-to-login-link">
        <a th:href="@{/buyer/login}"><i class="fas fa-arrow-left me-2"></i>Back to Login</a>
    </div>

</main>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="successModalLabel">Success!</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <p>Your password has been reset successfully!</p>
                <p>You will be redirected to the login page in <span id="countdown">3</span> seconds.</p>
            </div>
            <div class="modal-footer">
                <a href="/buyer/login" class="btn btn-primary w-100">Go to Login</a>
            </div>
        </div>
    </div>
</div>

<!-- Include password validation script -->
<script th:src="@{/js/password-validation.js}"></script>

<!-- Success message script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show success message if in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('success')) {
        const successModal = new bootstrap.Modal(document.getElementById('successModal'));
        successModal.show();
        
        // Countdown and redirect
        let count = 3;
        const countdownElement = document.getElementById('countdown');
        const countdownInterval = setInterval(() => {
            count--;
            countdownElement.textContent = count;
            if (count <= 0) {
                clearInterval(countdownInterval);
                window.location.href = '/buyer/login';
            }
        }, 1000);
    }
});
</script>
</body>
</html>
