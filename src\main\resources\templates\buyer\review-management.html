<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Reviews - ReadHub</title>
    <meta name="_csrf" th:content="${_csrf != null ? _csrf.token : ''}"/>
    <meta name="_csrf_header" th:content="${_csrf != null ? _csrf.headerName : 'X-CSRF-TOKEN'}"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        .review-item {
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .review-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
        }
        .rating-stars {
            color: #ffc107;
        }
        .rating-stars .far {
            color: #dee2e6;
        }
        .tab-content {
            min-height: 400px;
        }
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        .btn-review {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-review:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
            color: white;
        }
        .btn-edit-review {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-edit-review:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(40,167,69,0.3);
            color: white;
        }
    </style>
</head>
<body>

<div th:replace="~{fragments/header :: header-content}"></div>

<main class="main-content">
    <div class="container py-5">
        <div class="row">
            <div th:replace="~{fragments/buyer-account-sidebar :: sidebar('reviews')}"></div>

            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header bg-white border-0 pt-4 pb-0">
                        <h4 class="mb-0"><i class="fas fa-star me-2"></i>My Reviews</h4>
                        <p class="text-muted mb-0">Manage your product reviews and write new ones</p>
                    </div>

                    <div class="card-body">
                        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <span th:text="${successMessage}"></span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <div th:if="${error}" class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span th:text="${error}">Error message</span>
                        </div>

                        <form th:action="@{/buyer/reviews}" method="get" class="mb-4 p-3 border rounded bg-light">
                            <input type="hidden" name="tab" th:value="${activeTab}">
                            <div class="row g-3 align-items-end">
                                <div class="col-md-3">
                                    <label for="sort" class="form-label fw-bold">Sort By</label>
                                    <select id="sort" name="sort" class="form-select">
                                        <option value="newest" th:selected="${sort == 'newest'}">Newest First</option>
                                        <option value="oldest" th:selected="${sort == 'oldest'}">Oldest First</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="startDate" class="form-label fw-bold">From Date</label>
                                    <input type="date" id="startDate" name="startDate" class="form-control" th:value="${startDate}">
                                </div>
                                <div class="col-md-3">
                                    <label for="endDate" class="form-label fw-bold">To Date</label>
                                    <input type="date" id="endDate" name="endDate" class="form-control" th:value="${endDate}">
                                </div>
                                <div class="col-md-3 d-flex">
                                    <button type="submit" class="btn btn-primary w-50 me-2"><i class="fas fa-filter me-1"></i>Filter</button>
                                    <a th:href="@{/buyer/reviews(tab=${activeTab})}" class="btn btn-secondary w-50"><i class="fas fa-times me-1"></i>Clear</a>
                                </div>
                            </div>
                        </form>
                        <ul class="nav nav-tabs mb-4" id="reviewTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" th:classappend="${activeTab == 'pending'} ? 'active' : ''"
                                   th:href="@{/buyer/reviews(tab='pending', sort=${sort}, startDate=${startDate}, endDate=${endDate})}" role="tab">
                                    <i class="fas fa-clock me-2"></i> Pending Reviews
                                    <span class="badge bg-warning ms-2" th:text="${pendingCount}">0</span>
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" th:classappend="${activeTab == 'reviewed'} ? 'active' : ''"
                                   th:href="@{/buyer/reviews(tab='reviewed', sort=${sort}, startDate=${startDate}, endDate=${endDate})}" role="tab">
                                    <i class="fas fa-check-circle me-2"></i> Reviewed Items
                                    <span class="badge bg-success ms-2" th:text="${reviewedCount}">0</span>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content">
                            <div th:if="${orderItems != null && !orderItems.isEmpty()}">
                                <div th:each="item : ${orderItems}" class="review-item p-3">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <img th:src="${item.book.coverImgUrl != null ? item.book.coverImgUrl : '/images/book-placeholder.jpg'}"
                                                 alt="Book Cover" class="book-cover">
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="mb-1" th:text="${item.book.title}">Book Title</h6>
                                            <p class="text-muted mb-1" th:text="${item.book.authors}">Author</p>
                                            <p class="text-muted mb-1"><small>Order ID: <span class="fw-bold text-primary">#<span th:text="${item.order.orderId}">12345</span></span> | Order Date: <span th:text="${#temporals.format(item.order.orderDate, 'dd/MM/yyyy')}">01/01/2024</span></small></p>
                                            <p class="text-muted mb-0"><small>Quantity: <span th:text="${item.quantity}">1</span> | Price: <span th:text="${#numbers.formatDecimal(item.book.sellingPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'">0 VND</span></small></p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div th:if="${activeTab == 'reviewed'}">
                                                <button class="btn btn-edit-review btn-sm mb-2 w-100"
                                                        th:onclick="'editReview(' + ${item.orderItemId} + ')'">
                                                    <i class="fas fa-edit me-1"></i>Edit Review
                                                </button>

                                                <button class="btn btn-outline-info btn-sm mb-2 w-100"
                                                        th:onclick="'viewReview(' + ${item.orderItemId} + ')'">
                                                    <i class="fas fa-eye me-1"></i>View Review
                                                </button>

                                                <button class="btn btn-outline-danger btn-sm w-100"
                                                        th:onclick="'confirmDelete(' + ${item.review.reviewId} + ')'">
                                                    <i class="fas fa-trash-alt me-1"></i>Delete Review
                                                </button>
                                            </div>

                                            <div th:if="${activeTab == 'pending'}">
                                                <button class="btn btn-review btn-sm"
                                                        th:onclick="'writeReview(' + ${item.orderItemId} + ')'">
                                                    <i class="fas fa-star me-1"></i>Write Review
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div th:if="${orderItems == null || orderItems.isEmpty()}" class="empty-state">
                                <div th:if="${activeTab == 'reviewed'}"><i class="fas fa-star-half-alt"></i><h5>No Reviews Found</h5><p>No reviewed items match your current filters. Try adjusting the date range or clearing the filters.</p></div>
                                <div th:if="${activeTab == 'pending'}"><i class="fas fa-clock"></i><h5>No Pending Reviews Found</h5><p>No pending items match your current filters. Great job, or try clearing the filters!</p></div>
                            </div>

                            <nav th:if="${totalPages > 1}" aria-label="Review pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled'">
                                        <a class="page-link" th:href="@{/buyer/reviews(page=${currentPage - 1}, tab=${activeTab}, sort=${sort}, startDate=${startDate}, endDate=${endDate})}"><i class="fas fa-chevron-left"></i></a>
                                    </li>
                                    <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}" th:classappend="${currentPage == i} ? 'active'">
                                        <a class="page-link" th:href="@{/buyer/reviews(page=${i}, tab=${activeTab}, sort=${sort}, startDate=${startDate}, endDate=${endDate})}" th:text="${i + 1}">1</a>
                                    </li>
                                    <li class="page-item" th:classappend="${currentPage + 1 >= totalPages} ? 'disabled'">
                                        <a class="page-link" th:href="@{/buyer/reviews(page=${currentPage + 1}, tab=${activeTab}, sort=${sort}, startDate=${startDate}, endDate=${endDate})}"><i class="fas fa-chevron-right"></i></a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<div th:replace="~{fragments/footer :: footer-content}"></div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/script.js}"></script>
<script>
    function writeReview(orderItemId) { window.location.href = `/buyer/reviews/write/${orderItemId}`; }
    function editReview(orderItemId) { window.location.href = `/buyer/reviews/edit/${orderItemId}`; }
    function viewReview(orderItemId) { window.location.href = `/buyer/reviews/view/${orderItemId}`; }
    function confirmDelete(reviewId) {
        if (confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
            // This assumes you have a form with id 'deleteReviewForm' to submit for deletion.
            // You might need to add this form to your HTML if it doesn't exist.
            // For example:
            // <form id="deleteReviewForm" th:action="@{/buyer/reviews/delete}" method="post" style="display:none;">
            //     <input type="hidden" name="reviewId" id="reviewIdToDelete"/>
            //     <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}"/>
            // </form>
            document.getElementById('reviewIdToDelete').value = reviewId;
            document.getElementById('deleteReviewForm').submit();
        }
    }
</script>

</body>
</html>