<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seller Account Information - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Include Topbar -->
    <div th:replace="fragments/seller-topbar :: seller-topbar"></div>

    <div class="container my-4">
        <div class="row">
            <!-- Include Sidebar -->
            <div class="col-lg-3 mb-4">
                <div th:replace="fragments/seller-account-sidebar :: seller-sidebar"></div>
            </div>
            
            <div class="col-lg-9">
                <!-- Success message -->
                <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                
                <!-- Account Information -->
                <div class="account-container">
                    <h3 class="section-title">Account Information</h3>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="fw-bold">Full Name:</label>
                            <p th:text="${user.fullName}">John Doe</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="fw-bold">Email:</label>
                            <p th:text="${user.email}"><EMAIL></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="fw-bold">Phone Number:</label>
                            <p th:text="${user.phone != null ? user.phone : 'Not provided'}">+**********</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="fw-bold">Gender:</label>
                            <p th:text="${user.gender == 1 ? 'Male' : (user.gender == 2 ? 'Female' : 'Other')}">Male</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="fw-bold">Date of Birth:</label>
                            <p th:text="${user.dateOfBirth != null ? #temporals.format(user.dateOfBirth, 'dd MMM yyyy') : 'Not provided'}">01 Jan 1990</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="fw-bold">Account Type:</label>
                            <p>
                                <span th:each="role : ${roles}" class="badge bg-primary me-1" th:text="${role}">SELLER</span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a th:href="@{/seller/edit-info}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-edit me-2"></i> Edit Information
                        </a>
                        <a th:href="@{/seller/delete-shop}" class="btn btn-outline-danger">
                            <i class="fas fa-store-slash me-2"></i> Delete Shop
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
