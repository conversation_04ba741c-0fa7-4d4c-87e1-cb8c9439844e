<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - Bookix</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        .password-strength {
            margin-top: 5px;
        }
        .strength-text {
            font-size: 0.85rem;
            font-weight: 500;
            margin-top: 3px;
        }
        /* Custom notification styles */
        .alert-notification {
            position: relative;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 0.25rem;
            animation: fadeIn 0.5s;
        }
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .alert-dismissible {
            padding-right: 4rem;
        }
        .alert-dismissible .close {
            position: absolute;
            top: 0;
            right: 0;
            padding: 0.75rem 1.25rem;
            color: inherit;
            background: transparent;
            border: 0;
            font-size: 1.5rem;
            cursor: pointer;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="account-page py-5 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item"><a th:href="@{/buyer/account-info}">Account Information</a></li>
                <li class="breadcrumb-item active" aria-current="page">Change Password</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Account Sidebar using fragment -->
            <div th:replace="~{fragments/buyer-account-sidebar :: sidebar('password')}"></div>

            <!-- Change Password Content -->
            <section class="col-lg-9 account-content">
                <div class="card">
                    <div class="card-header bg-white py-3">
                        <h4 class="mb-0">Change Password</h4>
                    </div>
                    <div class="card-body p-4">
                        <!-- Success message -->
                        <div th:if="${successMessage}" class="alert alert-success alert-dismissible alert-notification"
                             role="alert">
                            <span th:text="${successMessage}"></span>
                            <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">&times;
                            </button>
                        </div>

                        <!-- Error message -->
                        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible alert-notification"
                             role="alert">
                            <span th:text="${errorMessage}"></span>
                            <button type="button" class="close" data-bs-dismiss="alert" aria-label="Close">&times;
                            </button>
                        </div>
                        
                        <!-- Google OAuth2 warning for password change -->
                        <div th:if="${isOAuth2User}" class="alert alert-warning mb-4">
                            <i class="fas fa-exclamation-triangle me-2"></i> You're signed in with Google. Password changes are not allowed. Please manage your password through your Google account settings.
                        </div>
                        
                        <form th:action="@{/buyer/update-password}" method="post">
                            <div class="mb-3">
                                <label for="currentPassword" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="currentPassword" name="currentPassword"
                                       th:disabled="${isOAuth2User}" required>
                                <div class="invalid-feedback">Please enter your current password</div>
                            </div>
                            <div class="mb-3">
                                <label for="newPassword" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="newPassword" name="newPassword"
                                       th:disabled="${isOAuth2User}" required>
                                <div class="form-text">Password must be at least 6 characters long and include at least
                                    one number and one letter.
                                </div>
                                <div class="invalid-feedback">Password must meet the requirements</div>
                            </div>
                            <div class="mb-4">
                                <label for="confirmPassword" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
                                       th:disabled="${isOAuth2User}" required>
                                <div class="invalid-feedback">Passwords do not match</div>
                            </div>
                            <button type="submit" class="btn btn-primary" th:disabled="${isOAuth2User}">Change Password</button>
                            <a th:href="@{/buyer/account-info}" class="btn btn-secondary ms-2">Cancel</a>
                        </form>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/password-change-validation.js}"></script>
</body>
</html>
