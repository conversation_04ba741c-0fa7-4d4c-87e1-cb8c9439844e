<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Address Management - Bookix</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Additional styles for new components */
        .notification-card {
            border-left: 4px solid #dc3545;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }

        .quick-action-btn {
            width: 100%;
            margin-bottom: 1rem;
            text-align: left;
            padding: 1rem;
        }

        .quick-action-btn i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header}"></div>

<main class="account-page py-5">
    <div class="container">
        <!-- Breadcrumb navigation -->
        <div class="row">
            <!-- Account Sidebar -->
            <div class="col-lg-3 mb-4">
                <div th:replace="~{fragments/seller-account-sidebar :: seller-sidebar}"></div>
            </div>

            <!-- Account Content - Address Management -->
            <section class="col-lg-9 account-content">
                <div class="card">
                    <!-- Card header with title and add button -->
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Addresses</h4>
                        <a th:href="@{/seller/addresses/new}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-plus me-1"></i> Add New Address
                        </a>
                    </div>
                    <div class="card-body p-4">

                        <!-- Success message -->
                        <div th:if="${successMessage}" class="address-saved-header mb-4">
                            <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}">Address saved.</span>
                        </div>

                        <!-- Error message -->
                        <div th:if="${errorMessage}" class="alert alert-danger mb-4" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}">Error occurred.</span>
                        </div>

                        <!-- No addresses message -->
                        <div th:if="${#lists.isEmpty(addresses)}" class="alert alert-info mb-4" role="alert">
                            <i class="fas fa-info-circle me-2"></i> You don't have any addresses yet.
                            <a th:href="@{/seller/addresses/new}" class="alert-link">Add your first address</a>.
                        </div>

                        <!-- Address list -->
                        <div class="address-list">
                            <!-- Loop through user addresses -->
                            <div th:each="address : ${addresses}" class="address-item pb-3 mb-3 border-bottom d-flex justify-content-between">
                                <div class="address-details">
                                    <p class="mb-1">
                                        <!-- Recipient information -->
                                        <strong th:text="${address.recipientName}">Recipient Name</strong> |
                                        <span th:text="${address.recipientPhone}">Phone Number</span>

                                        <!-- Address type and default badge -->
                                        <span th:if="${address.default}" class="address-default-badge ms-2">Default address</span>
                                        <span th:if="${!address.default}" th:text="${address.addressType == 0 ? 'Residential' : 'Company'}"
                                              class="address-type-badge ms-2">Address type</span>
                                    </p>

                                    <!-- Company name if exists -->
                                    <p th:if="${address.company != null && !address.company.isEmpty()}"
                                       class="mb-1 text-muted small">
                                        <i class="fas fa-building me-1"></i>
                                        <span th:text="${address.company}">Company Name</span>
                                    </p>

                                    <!-- Full address -->
                                    <p class="mb-0 text-muted small" th:text="${address.getFullAddress()}">Full Address</p>
                                </div>

                                <!-- Address actions (edit/delete) -->
                                <div class="address-actions text-end flex-shrink-0">
                                    <!-- Edit button -->
                                    <a th:href="@{|/seller/addresses/edit/${address.addressId}|}" class="text-decoration-none">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </a>

                                    <!-- Set as default (if not already default) -->
                                    <a th:if="${!address.default}"
                                       th:href="@{|/seller/addresses/set-default/${address.addressId}|}"
                                       class="text-primary ms-2 text-decoration-none">
                                        <i class="fas fa-star me-1"></i> Set as default
                                    </a>

                                    <!-- Delete button (only for non-default addresses) -->
                                    <a th:if="${!address.default}"
                                       th:href="@{|/seller/addresses/delete/${address.addressId}|}"
                                       class="text-danger ms-2 text-decoration-none"
                                       onclick="return confirm('Are you sure you want to delete this address?');">
                                        <i class="fas fa-trash-alt me-1"></i> Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script th:src="@{/js/script.js}"></script>
</body>
</html>