<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify OTP - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body.auth-page {
            background-color: #F8F5F0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 1rem;
            font-family: 'Open Sans', sans-serif;
        }

        .auth-container {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px;
            text-align: center;
        }

        .auth-logo {
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2C3E50;
            margin-bottom: 1rem;
        }
        .auth-logo a {
            text-decoration: none;
            color: inherit;
        }

        .auth-title {
            font-family: 'Lora', serif;
            font-size: 1.75rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .auth-subtitle {
            font-size: 0.95rem;
            color: #555;
            margin-bottom: 2rem;
        }

        .form-control-auth {
            height: calc(1.5em + .75rem + 8px);
            border-radius: 0.25rem;
            font-size: 0.95rem;
            text-align: left;
        }
        .form-control-auth:focus {
            border-color: #2C3E50;
            box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
        }

        .btn-auth-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
            color: #FFFFFF;
            font-weight: 600;
            padding: 0.65rem;
        }
        .btn-auth-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
            color: #FFFFFF;
        }

        .otp-info {
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #555;
        }

        .back-to-login-link {
            display: block;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: #555;
        }
        .back-to-login-link a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: none;
        }
        .back-to-login-link a:hover {
            text-decoration: underline;
        }
        
        .resend-otp {
            margin-top: 1rem;
            font-size: 0.9rem;
        }
        .resend-otp a {
            color: #2C3E50;
            font-weight: 600;
            text-decoration: none;
        }
        .resend-otp a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body class="auth-page">

<main class="auth-container">
    <div class="auth-logo">
        <a href="/">ReadHub</a>
    </div>

    <h1 class="auth-title">Verify OTP</h1>
    <p class="auth-subtitle">
        Enter the verification code sent to your email.
    </p>

    <!-- Display success messages -->
    <div th:if="${successMessage}" class="alert alert-success text-center mb-3" role="alert">
        <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
    </div>

    <!-- Display error messages -->
    <div th:if="${errorMessage}" class="alert alert-danger text-center mb-3" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
    </div>

    <div class="otp-info">
        <p>We've sent a 6-digit code to <strong th:text="${maskedEmail ?: 'your email'}">your email</strong></p>
        <p>The code will expire in <strong>5 minutes</strong>.</p>
    </div>

    <form th:action="@{/password-reset/verify-otp}" method="post">
        <div class="mb-3">
            <label for="otp" class="form-label visually-hidden">OTP Code</label>
            <input type="text" class="form-control form-control-auth" id="otp" name="otp" 
                   placeholder="Enter 6-digit code" required autofocus 
                   pattern="[0-9]{6}" maxlength="6" minlength="6"
                   inputmode="numeric">
            <div class="invalid-feedback">Please enter a valid 6-digit code</div>
        </div>

        <button type="submit" class="btn btn-auth-primary w-100 mt-2">Verify Code</button>
    </form>

    <div class="resend-otp">
        <p>Didn't receive a code? <a th:href="@{/password-reset/resend-otp}">Resend OTP</a></p>
    </div>

    <div class="back-to-login-link">
        <a th:href="@{/buyer/login}"><i class="fas fa-arrow-left me-2"></i>Back to Login</a>
    </div>

</main>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
