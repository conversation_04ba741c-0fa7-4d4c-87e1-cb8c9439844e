<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cancelled Orders - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Additional styles for new components */
        .notification-card {
            border-left: 4px solid;
            transition: transform 0.3s ease;
        }
        .notification-card:hover {
            transform: translateY(-3px);
        }
        .notification-card.warning {
            border-color: #f39c12;
        }
        .notification-card.danger {
            border-color: #e74c3c;
        }
        .notification-card.info {
            border-color: #3498db;
        }
        .notification-card.success {
            border-color: #2ecc71;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .activity-item:hover {
            background-color: #f8f9fa;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        .activity-icon.order {
            background-color: rgba(44, 62, 80, 0.1);
            color: #2C3E50;
        }
        .activity-icon.review {
            background-color: rgba(241, 196, 15, 0.1);
            color: #f39c12;
        }
        .activity-icon.message {
            background-color: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }
        .activity-icon.stock {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
        }

        .chart-container {
            position: relative;
            height: 220px;
            margin-bottom: 1rem;
        }

        .quick-action-btn {
            width: 100%;
            margin-bottom: 0.75rem;
            text-align: left;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .quick-action-btn i {
            margin-right: 0.75rem;
            font-size: 1.1rem;
        }

        .low-stock-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .low-stock-item:hover {
            background-color: #f8f9fa;
        }
        .low-stock-item:last-child {
            border-bottom: none;
        }
        .stock-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .badge-danger {
            background-color: #e74c3c;
            color: white;
        }
        .badge-warning {
            background-color: #f39c12;
            color: white;
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        .overview-header h3 {
            margin-bottom: 0;
        }
        .period-badge {
            background-color: #ecf0f1;
            color: #2C3E50;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
<!-- Include Topbar -->
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
    <div class="row">
        <!-- Include Sidebar -->
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title mb-0">
                        <i class="fas fa-times-circle text-danger me-2"></i>Cancelled Orders
                    </h3>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a th:href="@{/seller/dashboard}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a th:href="@{/seller/orders}">Orders</a></li>
                            <li class="breadcrumb-item active">Cancellations</li>
                        </ol>
                    </nav>
                </div>

                <!-- Search and Filter Form -->
                <div class="account-container" style="padding-bottom: 1rem; margin-bottom: 0;">
                    <form th:action="@{/seller/orders/cancellations}" method="get">
                        <div class="row g-2 align-items-end">
                            <div class="col-lg-4 col-md-12">
                                <label for="keyword" class="form-label">Search</label>
                                <input type="text" id="keyword" name="keyword" class="form-control" placeholder="Order ID, Buyer Name..." th:value="${keyword}">
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <label for="startDate" class="form-label">From Date</label>
                                <input type="date" id="startDate" name="startDate" class="form-control" th:value="${startDate}">
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <label for="endDate" class="form-label">To Date</label>
                                <input type="date" id="endDate" name="endDate" class="form-control" th:value="${endDate}">
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <a th:href="@{/seller/orders/cancellations}" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Orders Table -->
                <div class="account-container" style="border-top-left-radius: 0; border-top-right-radius: 0; padding-top: 1rem;">
                    <div th:if="${orderPage.content.empty}" class="text-center py-5">
                        <i class="fas fa-times-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No cancelled orders found</h5>
                        <p class="text-muted">There are no cancelled orders matching your criteria.</p>
                        <a th:href="@{/seller/orders}" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to All Orders
                        </a>
                    </div>

                    <div th:unless="${orderPage.content.empty}">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <p class="text-muted mb-0">
                                Showing <span th:text="${orderPage.numberOfElements}">0</span> of 
                                <span th:text="${orderPage.totalElements}">0</span> cancelled orders
                            </p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th>
                                        <a th:href="@{/seller/orders/cancellations(sortField='orderId', sortDir=${sortField == 'orderId' ? reverseSortDir : 'asc'}, keyword=${keyword}, startDate=${startDate}, endDate=${endDate})}">
                                            Order
                                            <i th:if="${sortField == 'orderId'}" th:class="${sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                        </a>
                                    </th>
                                    <th>
                                        <a th:href="@{/seller/orders/cancellations(sortField='orderDate', sortDir=${sortField == 'orderDate' ? reverseSortDir : 'asc'}, keyword=${keyword}, startDate=${startDate}, endDate=${endDate})}">
                                            Date
                                            <i th:if="${sortField == 'orderDate'}" th:class="${sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                        </a>
                                    </th>
                                    <th>Buyer</th>
                                    <th>
                                        <a th:href="@{/seller/orders/cancellations(sortField='totalAmount', sortDir=${sortField == 'totalAmount' ? reverseSortDir : 'asc'}, keyword=${keyword}, startDate=${startDate}, endDate=${endDate})}">
                                            Total
                                            <i th:if="${sortField == 'totalAmount'}" th:class="${sortDir == 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'}"></i>
                                        </a>
                                    </th>
                                    <th>Cancellation Date</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:each="order : ${orderPage.content}" th:object="${order}">
                                    <td>
                                        <a class="fw-bold text-decoration-none" th:href="@{/seller/orders/{id}(id=*{orderId})}" th:text="*{'#' + orderId}"></a>
                                    </td>
                                    <td th:text="${#temporals.format(order.orderDate, 'dd/MM/yyyy HH:mm')}"></td>
                                    <td>
                                        <div>
                                            <strong th:text="*{customerOrder.user.fullName}"></strong>
                                        </div>
                                        <small class="text-muted" th:text="*{customerOrder.user.email}"></small>
                                    </td>
                                    <td>
                                        <strong th:text="${'$' + #numbers.formatDecimal(order.totalAmount, 0, 'COMMA', 2, 'POINT')}"></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times-circle me-1"></i>Cancelled
                                        </span>
                                        <br>
                                        <small class="text-muted" th:text="${#temporals.format(order.updatedAt, 'dd/MM/yyyy HH:mm')}"></small>
                                    </td>
                                    <td class="text-center">
                                        <a th:href="@{/seller/orders/{id}(id=*{orderId})}" class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div th:if="${orderPage.totalPages > 1}" class="d-flex justify-content-center mt-4">
                            <nav aria-label="Page navigation">
                                <ul class="pagination">
                                    <li class="page-item" th:classappend="${orderPage.first} ? 'disabled'">
                                        <a class="page-link" th:href="@{/seller/orders/cancellations(page=${orderPage.number - 1}, size=${orderPage.size}, keyword=${keyword}, startDate=${startDate}, endDate=${endDate})}">Previous</a>
                                    </li>
                                    <li th:each="pageNum : ${#numbers.sequence(0, orderPage.totalPages - 1)}" 
                                        class="page-item" th:classappend="${pageNum == orderPage.number} ? 'active'">
                                        <a class="page-link" th:href="@{/seller/orders/cancellations(page=${pageNum}, size=${orderPage.size}, keyword=${keyword}, startDate=${startDate}, endDate=${endDate})}" th:text="${pageNum + 1}"></a>
                                    </li>
                                    <li class="page-item" th:classappend="${orderPage.last} ? 'disabled'">
                                        <a class="page-link" th:href="@{/seller/orders/cancellations(page=${orderPage.number + 1}, size=${orderPage.size}, keyword=${keyword}, startDate=${startDate}, endDate=${endDate})}">Next</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>
