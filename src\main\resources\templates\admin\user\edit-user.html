<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit User - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
    </style>
</head>
<body>

<div th:replace="~{fragments/admin-topbar :: admin-topbar}"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="~{fragments/admin-sidebar :: admin-sidebar(activeMenu='users')}"></div>
        </div>

        <div class="col-lg-9">
            <main class="py-4" th:if="${user != null}">
                <h3 class="section-title">Edit User Account</h3>
                <div th:if="${warningMessage}" class="alert alert-warning" role="alert" th:text="${warningMessage}"></div>

                <div class="card shadow-sm">
                    <div class="card-body p-4">
                        <form th:action="@{'/admin/users/update/' + ${user.userId}}" method="post">
                            <fieldset disabled>
                                <div class="mb-3"><label class="form-label">Full Name</label><input type="text" class="form-control" th:value="*{user.fullName}"></div>
                                <div class="mb-3"><label class="form-label">Email</label><input type="email" class="form-control" th:value="*{user.email}"></div>
                            </fieldset>
                            <hr>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Roles</label>
                                <small th:if="${user.userId == loggedInAdminId}" class="form-text text-muted d-block mb-2">
                                    You cannot modify your own roles.
                                </small>
                                <div th:each="role : ${allRoles}" class="form-check">
                                    <input class="form-check-input" type="checkbox" name="roles"
                                           th:value="${role.roleName}"
                                           th:id="'role_' + ${role.roleId}"
                                           th:checked="${currentUserRoles.contains(role.roleName)}"
                                           th:disabled="${user.userId == loggedInAdminId}"> <label class="form-check-label" th:for="'role_' + ${role.roleId}" th:text="${role.roleName}"></label>
                                </div>
                            </div>
                            <hr>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Account Status</label>
                                <small th:if="${user.userId == loggedInAdminId}" class="form-text text-muted d-block mb-2">
                                    You cannot block your own account.
                                </small>
                                <div class="form-check form-switch fs-5">
                                    <input class="form-check-input" type="checkbox" role="switch" id="statusSwitch" name="isActive" value="true"
                                           th:checked="${user.isActive}"
                                           th:disabled="${user.userId == loggedInAdminId}"> <label class="form-check-label" for="statusSwitch" th:text="${user.isActive ? 'Active' : 'Blocked'}"></label>
                                </div>
                            </div>

                            <a th:href="@{/admin/users}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary" th:disabled="${user.userId == loggedInAdminId}">Save Changes</button>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>