/* Custom admin styles - Enhancements for category management */

/* Improved layout and spacing */
.admin-main-content {
    min-height: 100vh;
    background-color: #f8f9fa;
}

/* Card enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Table styling */
.admin-table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.admin-table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    border-top: none;
    padding: 0.75rem 1rem;
}

.admin-table td {
    vertical-align: middle;
    padding: 0.75rem 1rem;
}

.admin-table tbody tr {
    transition: background-color 0.2s ease;
}

.admin-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Category status badges */
.badge {
    font-weight: 500;
    padding: 0.4em 0.6em;
    font-size: 80%;
}

/* Form controls */
.form-control:focus {
    border-color: #3A50A0;
    box-shadow: 0 0 0 0.2rem rgba(58, 80, 160, 0.25);
}

.form-check-input:checked {
    background-color: #3A50A0;
    border-color: #3A50A0;
}

.form-switch .form-check-input:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%233A50A0'/%3e%3c/svg%3e");
}

/* Button styling */
.btn-primary {
    background-color: #3A50A0;
    border-color: #3A50A0;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #344790;
    border-color: #344790;
}

.btn-outline-primary {
    color: #3A50A0;
    border-color: #3A50A0;
}

.btn-outline-primary:hover {
    background-color: #3A50A0;
    border-color: #3A50A0;
}

/* Modal enhancements */
.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Pagination styling */
.pagination .page-link {
    color: #3A50A0;
}

.pagination .page-item.active .page-link {
    background-color: #3A50A0;
    border-color: #3A50A0;
}

/* Sticky messages */
.sticky-alert {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: none;
}

/* For mobile responsiveness */
@media (max-width: 767.98px) {
    .admin-table {
        font-size: 0.85rem;
    }
    
    .admin-table td, .admin-table th {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Export button styles */
.btn-outline-primary {
    transition: all 0.2s ease-in-out;
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.25);
}

.btn-outline-primary i {
    margin-right: 0.5rem;
}

/* Toast container styles */
.toast-container {
    z-index: 1055 !important;
}

.toast {
    min-width: 300px;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.toast-body {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Export button loading state */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
