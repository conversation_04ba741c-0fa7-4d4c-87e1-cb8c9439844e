<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADMIN LOGIN - READHUB</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS (shared style.css + login specific) -->
    <style>
        /* Login Page Specific Styles */
        body.login-page {
            background-color: #F8F5F0; /* Light beige background for the whole page */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 1rem;
            font-family: 'Open Sans', sans-serif;
        }

        .login-container {
            background-color: #FFFFFF;
            padding: 2.5rem;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 420px; /* Max width for the login box */
        }

        .login-logo {
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #2C3E50; /* Dark Teal/Slate */
            text-align: center;
            margin-bottom: 1.5rem;
        }
        .login-logo a {
            text-decoration: none;
            color: inherit;
        }
        
        .admin-tag {
            color: #3A50A0;
            font-size: 1.2rem;
            font-weight: 600;
            display: block;
            margin-top: -0.5rem;
            margin-bottom: 1rem;
        }

        .form-control-login {
            height: calc(1.5em + .75rem + 8px); /* Slightly taller inputs */
            border-radius: 0.25rem;
        }
        .login-card .btn-primary {
            background-color: #3A50A0; /* Bookix primary accent */
            border-color: #3A50A0;
            font-weight: 500;
            padding: 0.75rem;
        }
        .form-control {
            border-left: none;
        }
        .form-control:focus {
            border-color: #86b7fe; /* Bootstrap focus color */
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); /* Bootstrap focus shadow */
            border-left: none; /* Keep left border off on focus */
        }
        .input-group .form-control:focus {
            border-left: none !important; /* Explicitly ensure no left border on focus within input-group */
        }
    </style>
</head>
<body class="login-page">

<main class="login-container">
    <div class="login-logo">
        <a href="/">ReadHub</a>
        <span class="admin-tag text-center">Admin Panel</span>
    </div>

    <!-- Login Form -->
    <form th:action="@{/admin/process_login}" method="post" class="mt-4">
        <!-- Display error message if there is one -->
        <div th:if="${param.error}" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>Invalid email or password. Please try again.
        </div>
        <!-- Display logout message if there is one -->
        <div th:if="${param.logout}" class="alert alert-success" role="alert">
            <i class="fas fa-check-circle me-2"></i>You have been logged out successfully.
        </div>
        
        <!-- Email input -->
        <div class="mb-4">
            <label for="username" class="form-label fw-medium">Email Address</label>
            <div class="input-group mb-3">
                <span class="input-group-text bg-white border-end-0">
                    <i class="fas fa-envelope text-muted"></i>
                </span>
                <input type="email" class="form-control border-start-0 form-control-login" 
                    id="username" name="username" placeholder="Enter your email" required autofocus>
            </div>
        </div>
        
        <!-- Password input -->
        <div class="mb-4">
            <label for="password" class="form-label fw-medium">Password</label>
            <div class="input-group mb-3">
                <span class="input-group-text bg-white border-end-0">
                    <i class="fas fa-lock text-muted"></i>
                </span>
                <input type="password" class="form-control border-start-0 form-control-login" 
                    id="password" name="password" placeholder="Enter your password" required>
            </div>
        </div>
        
        <!-- Submit button -->
        <div class="d-grid gap-2 mt-4">
            <button type="submit" class="btn btn-primary btn-lg py-2">Sign In</button>
        </div>
    </form>
</main>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>