<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <title th:text="${shop.shopName} + ' - ReadHub'">Shop Name - ReadHub</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
  <link rel="stylesheet" th:href="@{/css/style.css}">
  <style>
    .shop-banner { background-color: #f8f9fa; border-radius: 8px; padding: 2rem; }
    .shop-logo { width: 100px; height: 100px; border-radius: 50%; object-fit: cover; border: 3px solid #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
    .search-filter-container { background-color: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem; box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08); }
  </style>
</head>
<body>

<div th:replace="~{fragments/header :: header-content}"></div>

<main class="py-5">
  <div class="container">
    <div class="shop-banner d-flex align-items-center mb-5">
      <img th:src="${shop.logoUrl ?: '/images/default-shop-logo.png'}" alt="Shop Logo" class="shop-logo me-4">
      <div>
        <h1 class="display-5" th:text="${shop.shopName}">Shop Name</h1>
        <p class="lead text-muted" th:text="${shop.description}">Shop description goes here.</p>
        <p class="small text-muted mb-0"><i class="fas fa-calendar-alt me-2"></i> Joined on <span th:text="${#temporals.format(shop.registrationDate, 'dd MMMM, yyyy')}"></span></p>
      </div>
    </div>

    <div class="search-filter-container">
      <form th:action="@{/shops/{shopId}(shopId=${shop.shopId})}" method="get" class="row g-3 align-items-center" id="filterForm">
        <div class="col-md-6">
          <div class="input-group">
            <input type="text" class="form-control" name="searchQuery" placeholder="Search products in this shop..." th:value="${searchQuery}">
            <button class="btn btn-primary" type="submit"><i class="fas fa-search"></i></button>
          </div>
        </div>
        <div class="col-md-5">
          <div class="input-group">
            <label class="input-group-text" for="sortField">Sort by</label>
            <select class="form-select" id="sortField" name="sortField" onchange="this.form.submit()">
              <option value="dateAdded" th:selected="${sortField == 'dateAdded'}">Latest</option>
              <option value="title" th:selected="${sortField == 'title'}">Title</option>
              <option value="sellingPrice" th:selected="${sortField == 'sellingPrice'}">Price</option>
            </select>
            <input type="hidden" name="sortDir" id="sortDirInput" th:value="${sortDir}">
            <button type="button" class="btn btn-outline-secondary" onclick="toggleSortDirection()" title="Toggle sort direction">
              <i class="fas" th:classappend="${sortDir == 'asc' ? 'fa-sort-amount-up' : 'fa-sort-amount-down'}"></i>
              <span th:text="${sortDir == 'asc' ? 'A-Z' : 'Z-A'}"></span>
            </button>
          </div>
        </div>
        <div class="col-md-1">
          <a th:href="@{/shops/{shopId}(shopId=${shop.shopId})}" class="btn btn-outline-secondary w-100" title="Clear Filters">
            <i class="fas fa-times"></i>
          </a>
        </div>
      </form>
    </div>

    <h3 class="section-title">Products from this Shop (<span th:text="${bookPage.totalElements}">0</span>)</h3>

    <div th:if="${bookPage.empty}" class="text-center py-5">
      <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
      <p>No products found for your criteria.</p>
    </div>

    <div class="row" th:if="${!bookPage.empty}">
      <div class="col-lg-3 col-md-4 col-6 mb-4" th:each="book : ${bookPage.content}">
        <div class="book-card">
          <div class="position-relative">
            <a th:href="@{/product-detail(book_id=${book.book_id})}">
              <img th:src="${book.coverImgUrl != null ? book.coverImgUrl : '/images/book-placeholder.jpg'}" th:alt="${book.title}" class="book-cover img-fluid">
            </a>
            <!-- Sale badge if discount is available -->
            <span class="badge bg-danger sale-badge"
                  th:if="${book.originalPrice != null && book.sellingPrice != null && book.originalPrice.compareTo(book.sellingPrice) > 0}">Sale!</span>
          </div>
          <div class="book-info">
            <h6 class="book-title"><a th:href="@{/product-detail(book_id=${book.book_id})}" th:text="${book.title}">Book Title</a></h6>
            <div th:replace="~{fragments/book-rating :: star-rating(${book.averageRating})}"></div>
            <div th:replace="~{fragments/book-price :: price-display(${book.originalPrice}, ${book.sellingPrice})}"></div>
          </div>
        </div>
      </div>
    </div>

    <nav th:if="${totalPages > 1}" class="mt-4">
      <ul class="pagination justify-content-center">
        <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
          <a class="page-link" th:href="@{/shops/{shopId}(shopId=${shop.shopId}, page=${currentPage - 1}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}">&laquo;</a>
        </li>
        <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}" th:classappend="${currentPage == i ? 'active' : ''}">
          <a class="page-link" th:href="@{/shops/{shopId}(shopId=${shop.shopId}, page=${i}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}" th:text="${i + 1}"></a>
        </li>
        <li class="page-item" th:classappend="${currentPage >= totalPages - 1 ? 'disabled' : ''}">
          <a class="page-link" th:href="@{/shops/{shopId}(shopId=${shop.shopId}, page=${currentPage + 1}, searchQuery=${searchQuery}, sortField=${sortField}, sortDir=${sortDir})}">&raquo;</a>
        </li>
      </ul>
    </nav>
  </div>
</main>

<div th:replace="~{fragments/footer :: footer-content}"></div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
  function toggleSortDirection() {
      const sortDirInput = document.getElementById('sortDirInput');
      const currentDirection = sortDirInput.value;
      // Chuyển đổi giữa 'asc' và 'desc'
      sortDirInput.value = currentDirection === 'asc' ? 'desc' : 'asc';
      // Gửi form đi
      document.getElementById('filterForm').submit();
  }
</script>

</body>
</html>