<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Available Promotions - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <style>
        .promotion-card {
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .promotion-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .promotion-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px 12px 0 0;
        }
        
        .promotion-code {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }
        
        .promotion-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin: 0;
        }
        
        .promotion-body {
            padding: 1.5rem;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .discount-badge {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .discount-badge.percentage {
            background: #17a2b8;
        }
        
        .promotion-description {
            color: #666;
            margin-bottom: 1rem;
            flex-grow: 1;
        }
        
        .promotion-details {
            font-size: 0.9rem;
            color: #555;
        }
        
        .promotion-details .detail-item {
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .usage-stats {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .usage-progress {
            margin-top: 0.5rem;
        }
        
        .validity-period {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 0.75rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .btn-view-details {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-view-details:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
            color: white;
        }
        
        .promotion-unavailable {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .unavailable-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-weight: bold;
            color: #dc3545;
        }
        
        .sort-controls {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .stats-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="account-page py-5 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Available Promotions</li>
            </ol>
        </nav>

        <div class="row">
            <!-- Account Sidebar using fragment -->
            <div th:replace="~{fragments/buyer-account-sidebar :: sidebar('promotions')}"></div>

            <!-- Promotions Content -->
            <section class="col-lg-9 account-content">
                <div class="card">
                    <div class="card-header bg-white py-3">
                        <h4 class="mb-0">
                            <i class="fas fa-tags me-2"></i>Available Promotions
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <!-- Stats Summary -->
                        <div class="stats-summary">
                            <div class="row text-center">
                                <div class="col-md-6">
                                    <h3 class="text-primary" th:text="${totalElements}">0</h3>
                                    <p class="mb-0">Active Promotions</p>
                                </div>
                                <div class="col-md-6">
                                    <h3 class="text-success" th:text="${promotions != null ? promotions.numberOfElements : 0}">0</h3>
                                    <p class="mb-0">Available for You</p>
                                </div>
                            </div>
                        </div>

        <!-- Sort Controls -->
        <div class="sort-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Sort & Filter
                    </h5>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2 justify-content-md-end">
                        <select class="form-select" id="sortSelect" style="width: auto;">
                            <option value="startDate,desc" th:selected="${sortBy == 'startDate' && sortDir == 'desc'}">Newest First</option>
                            <option value="startDate,asc" th:selected="${sortBy == 'startDate' && sortDir == 'asc'}">Oldest First</option>
                            <option value="endDate,asc" th:selected="${sortBy == 'endDate' && sortDir == 'asc'}">Ending Soon</option>
                            <option value="discountValue,desc" th:selected="${sortBy == 'discountValue' && sortDir == 'desc'}">Highest Discount</option>
                        </select>
                        <button class="btn btn-outline-primary" onclick="applySort()">
                            <i class="fas fa-sort"></i> Apply
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <span th:text="${errorMessage}">Error message</span>
        </div>

        <!-- Promotions Grid -->
        <div class="row" th:if="${promotions != null && !promotions.empty}">
            <div class="col-lg-4 col-md-6 mb-4" th:each="promotion : ${promotions.content}">
                <div class="card promotion-card position-relative"
                     th:classappend="${usageStats != null && usageStats[promotion.promotionId] != null && !usageStats[promotion.promotionId].canBeUsed} ? 'promotion-unavailable' : ''">

                    <!-- Unavailable Overlay -->
                    <div class="unavailable-overlay" th:if="${usageStats != null && usageStats[promotion.promotionId] != null && !usageStats[promotion.promotionId].canBeUsed}">
                        <span>Not Available</span>
                    </div>
                    
                    <!-- Promotion Header -->
                    <div class="promotion-header">
                        <div class="promotion-code" th:text="${promotion.code}">PROMO10</div>
                        <h5 class="promotion-title" th:text="${promotion.name}">Promotion Title</h5>
                    </div>
                    
                    <!-- Promotion Body -->
                    <div class="promotion-body">
                        <!-- Discount Badge -->
                        <div class="discount-badge" 
                             th:classappend="${promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'} ? 'percentage' : ''">
                            <span th:if="${promotion.promotionType.name() == 'PERCENTAGE_DISCOUNT'}" 
                                  th:text="${promotion.discountValue} + '% OFF'">10% OFF</span>
                            <span th:if="${promotion.promotionType.name() == 'FIXED_AMOUNT_DISCOUNT'}"
                                  th:text="${#numbers.formatDecimal(promotion.discountValue, 0, 'COMMA', 0, 'POINT')} + ' VND OFF'">${promotion.discountValue} VND OFF</span>
                        </div>
                        
                        <!-- Description -->
                        <div class="promotion-description" th:text="${promotion.description}">
                            Promotion description goes here...
                        </div>
                        
                        <!-- Validity Period -->
                        <div class="validity-period">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <strong>Valid:</strong> 
                            <span th:text="${#temporals.format(promotion.startDate, 'MMM dd, yyyy')}">Jan 01, 2024</span>
                            - 
                            <span th:text="${#temporals.format(promotion.endDate, 'MMM dd, yyyy')}">Dec 31, 2024</span>
                        </div>
                        
                        <!-- Usage Statistics -->
                        <div class="usage-stats" th:if="${usageStats != null && usageStats[promotion.promotionId] != null}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span><strong>Your Usage:</strong></span>
                                <span class="badge bg-primary" th:text="${usageStats[promotion.promotionId].userUsageCount}">0</span>
                            </div>

                            <div th:if="${promotion.usageLimitPerUser != null}" class="usage-progress">
                                <div class="d-flex justify-content-between small text-muted">
                                    <span>Remaining uses for you</span>
                                    <span th:text="${usageStats[promotion.promotionId].remainingUsesForUser}">5</span>
                                </div>
                                <div class="progress mt-1" style="height: 6px;">
                                    <div class="progress-bar"
                                         th:style="'width: ' + ${(usageStats[promotion.promotionId].userUsageCount * 100.0 / promotion.usageLimitPerUser)} + '%'"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Default Usage Statistics when data not available -->
                        <div class="usage-stats" th:if="${usageStats == null || usageStats[promotion.promotionId] == null}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span><strong>Your Usage:</strong></span>
                                <span class="badge bg-primary">0</span>
                            </div>
                            <div class="text-muted small">Usage data loading...</div>
                        </div>
                        
                        <!-- Promotion Details -->
                        <div class="promotion-details">
                            <div class="detail-item" th:if="${promotion.minOrderValue != null}">
                                <span><i class="fas fa-shopping-cart me-1"></i>Min. Order:</span>
                                <span th:text="${#numbers.formatDecimal(promotion.minOrderValue, 0, 'COMMA', 0, 'POINT')} + ' VND'">${promotion.minOrderValue} VND</span>
                            </div>
                            <div class="detail-item" th:if="${promotion.maxDiscountAmount != null}">
                                <span><i class="fas fa-tag me-1"></i>Max. Discount:</span>
                                <span th:text="${#numbers.formatDecimal(promotion.maxDiscountAmount, 0, 'COMMA', 0, 'POINT')} + ' VND'">${promotion.maxDiscountAmount} VND</span>
                            </div>
                        </div>
                        
                        <!-- View Details Button -->
                        <div class="mt-auto pt-3">
                            <a th:href="@{/buyer/promotions/{id}(id=${promotion.promotionId})}" 
                               class="btn btn-view-details w-100">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- No Promotions Message -->
        <div th:if="${promotions == null || promotions.empty}" class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No Active Promotions</h4>
            <p class="text-muted">Check back later for amazing deals and discounts!</p>
        </div>

        <!-- Pagination -->
        <nav th:if="${totalPages > 1}" aria-label="Promotions pagination">
            <ul class="pagination justify-content-center">
                <li class="page-item" th:classappend="${currentPage == 0} ? 'disabled'">
                    <a class="page-link" th:href="@{/buyer/promotions(page=${currentPage - 1}, sortBy=${sortBy}, sortDir=${sortDir})}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                
                <li th:each="pageNum : ${#numbers.sequence(0, totalPages - 1)}" 
                    class="page-item" 
                    th:classappend="${pageNum == currentPage} ? 'active'">
                    <a class="page-link" 
                       th:href="@{/buyer/promotions(page=${pageNum}, sortBy=${sortBy}, sortDir=${sortDir})}"
                       th:text="${pageNum + 1}">1</a>
                </li>
                
                <li class="page-item" th:classappend="${currentPage == totalPages - 1} ? 'disabled'">
                    <a class="page-link" th:href="@{/buyer/promotions(page=${currentPage + 1}, sortBy=${sortBy}, sortDir=${sortDir})}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
                        </nav>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JavaScript -->
<script th:src="@{/js/scripts.js}"></script>

<script>
function applySort() {
    const sortSelect = document.getElementById('sortSelect');
    const [sortBy, sortDir] = sortSelect.value.split(',');

    const url = new URL(window.location);
    url.searchParams.set('sortBy', sortBy);
    url.searchParams.set('sortDir', sortDir);
    url.searchParams.set('page', '0'); // Reset to first page

    window.location.href = url.toString();
}
</script>

<!-- Chat Widget - Buyers Only -->
<div th:replace="~{fragments/chat-widget :: buyer-chat-widget}"></div>

</body>
</html>
