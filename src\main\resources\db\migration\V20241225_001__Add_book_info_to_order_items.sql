USE isp392;
-- Update existing order_items with current book information
UPDATE oi
SET 
    oi.book_title = b.title,
    oi.book_authors = b.authors,
    oi.book_image_url = b.cover_img_url
FROM order_items oi
INNER JOIN books b ON oi.book_id = b.book_id
WHERE oi.book_title IS NULL;

-- Make book_title NOT NULL after populating existing data
ALTER TABLE order_items
ALTER COLUMN book_title NVARCHAR(500) NOT NULL;