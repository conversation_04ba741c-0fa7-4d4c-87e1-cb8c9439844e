<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/header :: header-content}"></div>

<main class="account-page py-5 bg-light">
    <div class="container">
        <div class="row">
            <!-- Sidebar -->
            <div th:replace="fragments/buyer-account-sidebar :: sidebar('orders')"></div>
            
            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="card rounded-4 shadow-sm">
                    <div class="card-header bg-white border-0 pt-4">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a th:href="@{/}">Home</a></li>
                                <li class="breadcrumb-item"><a th:href="@{/buyer/orders}">Order History</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Order Details #<span th:text="${order.orderId}"></span></li>
                            </ol>
                        </nav>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-7">
                                <div class="account-container rounded-4 p-4">
                                    <h5 class="mb-3"><i class="fas fa-shipping-fast me-2"></i>Shipping Information</h5>
                                    <hr class="my-3">
                                    <div class="detail-row mb-2" th:if="${order.customerOrder}"><span class="label">Recipient:</span> <span class="value" th:text="${order.customerOrder.recipientName}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.customerOrder}"><span class="label">Phone Number:</span> <span class="value" th:text="${order.customerOrder.recipientPhone}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.customerOrder}"><span class="label">Address:</span> <span class="value text-end" th:text="${order.customerOrder.shippingAddressDetail + ', ' + order.customerOrder.shippingWard + ', ' + order.customerOrder.shippingDistrict + ', ' + order.customerOrder.shippingProvince}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.customerOrder}"><span class="label">Address Type:</span> <span class="value" th:text="${order.customerOrder.shippingAddressType == 0 ? 'Home' : 'Company'}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.customerOrder}"><span class="label">Shipping Company:</span> <span class="value" th:text="${order.customerOrder.shippingCompany}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.notes}"><span class="label">Notes:</span> <span class="value" th:text="${order.notes}"></span></div>
                                </div>

                                <div class="account-container rounded-4 p-4 mt-4">
                                    <h5 class="mb-3"><i class="fas fa-credit-card me-2"></i>Payment Information</h5>
                                    <hr class="my-3">
                                    <div class="detail-row mb-2" th:if="${order.customerOrder}"><span class="label">Payment Method:</span> <span class="value" th:text="${order.customerOrder.paymentMethod}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.customerOrder}"><span class="label">Payment Status:</span> <span class="value" th:text="${order.customerOrder.paymentStatus}"></span></div>
                                </div>
                            </div>

                            <div class="col-md-5">
                                <div class="account-container rounded-4 p-4">
                                    <h5 class="mb-3"><i class="fas fa-receipt me-2"></i>Order Information</h5>
                                    <hr class="my-3">
                                    <div class="detail-row mb-2"><span class="label">Order Date:</span> <span class="value" th:text="${#temporals.format(order.orderDate, 'dd/MM/yyyy HH:mm')}"></span></div>
                                    <div class="detail-row mb-2">
                                        <span class="label">Status:</span> 
                                        <span th:class="'order-status status-' + ${#strings.toLowerCase(order.orderStatus)}"
                                              th:text="${order.orderStatus}">
                                        </span>
                                    </div>
                                    <div class="detail-row mb-2" th:if="${order.updatedAt}"><span class="label">Last Updated:</span> <span class="value" th:text="${#temporals.format(order.updatedAt, 'dd/MM/yyyy HH:mm')}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.cancelledAt}"><span class="label">Cancellation Date:</span> <span class="value" th:text="${#temporals.format(order.cancelledAt, 'dd/MM/yyyy HH:mm')}"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.cancellationReason}"><span class="label">Cancellation Reason:</span> <span class="value" th:text="${order.cancellationReason}"></span></div>
                                </div>

                                <div class="account-container rounded-4 p-4 mt-4">
                                    <h5 class="mb-3"><i class="fas fa-dollar-sign me-2"></i>Cost Summary</h5>
                                    <hr class="my-3">
                                    <div class="detail-row mb-2"><span class="label">Subtotal:</span> <span class="value" th:text="${#numbers.formatDecimal(order.subTotal, 0, 'COMMA', 0, 'POINT')} + ' VND'"></span></div>
                                    <div class="detail-row mb-2"><span class="label">Shipping Fee:</span> <span class="value" th:text="${#numbers.formatDecimal(order.shippingFee, 0, 'COMMA', 0, 'POINT')} + ' VND'"></span></div>
                                    <div class="detail-row mb-2" th:if="${order.discountAmount != null && order.discountAmount > 0}">
                                        <span class="label">Discount<span th:if="${order.discountCode}" th:text="' (' + ${order.discountCode} + ')'"></span>:</span>
                                        <span class="value text-danger" th:text="${'-' + #numbers.formatDecimal(order.discountAmount, 0, 'COMMA', 0, 'POINT')} + ' VND'"></span>
                                    </div>
                                    <hr class="my-2">
                                    <div class="detail-row fs-5"><span class="label">Total Payment:</span> <span class="value text-primary" th:text="${#numbers.formatDecimal(order.totalAmount, 0, 'COMMA', 0, 'POINT')} + ' VND'"></span></div>
                                </div>
                            </div>
                        </div>

                        <div class="account-container rounded-4 p-4 mt-4">
                            <h5 class="section-title mb-3">Products in Order</h5>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th class="border-top-0">Product</th>
                                            <th class="border-top-0">Price</th>
                                            <th class="border-top-0">Quantity</th>
                                            <th class="text-end border-top-0">Subtotal</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="item : ${order.orderItems}">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img th:src="${item.bookImageUrl != null ? item.bookImageUrl : '/images/book-placeholder.jpg'}"
                                                         alt="Book cover" class="rounded-3 me-3" style="width: 60px; height: auto;">
                                                    <div>
                                                        <h6 class="mb-1" th:text="${item.bookTitle != null ? item.bookTitle : 'Product no longer available'}"></h6>
                                                        <small class="text-muted" th:if="${item.bookAuthors != null}"
                                                               th:text="${'Author: ' + item.bookAuthors}"></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td th:text="${#numbers.formatDecimal(item.unitPrice, 0, 'COMMA', 0, 'POINT')} + ' VND'"></td>
                                            <td th:text="${item.quantity}"></td>
                                            <td class="text-end">
                                                <span th:text="${#numbers.formatDecimal(item.subtotal, 0, 'COMMA', 0, 'POINT')} + ' VND'"></span>
                                                <!-- Rebuy button for individual item -->
                                                <div class="mt-2" th:if="${order.orderStatus != null && order.orderStatus.name() == 'DELIVERED'}">
                                                    <!-- Check if book is still available -->
                                                    <div th:if="${item.book != null && item.book.active && item.book.stockQuantity > 0}">
                                                        <form th:action="@{'/buyer/orders/' + ${order.orderId} + '/items/' + ${item.book.bookId} + '/rebuy'}"
                                                              method="post" class="d-inline-block">
                                                            <input type="hidden" name="quantity" th:value="${item.quantity}"/>
                                                            <button type="submit" class="btn btn-sm btn-outline-success rounded-3">
                                                                <i class="fas fa-sync-alt"></i> Rebuy Item
                                                            </button>
                                                        </form>
                                                    </div>
                                                    <!-- Show unavailable message if book is not available -->
                                                    <div th:if="${item.book == null || !item.book.active || item.book.stockQuantity <= 0}">
                                                        <button type="button" class="btn btn-sm btn-outline-secondary rounded-3" disabled>
                                                            <i class="fas fa-ban"></i> Unavailable
                                                        </button>
                                                        <small class="text-muted d-block">
                                                            <span th:if="${item.book == null}">Book no longer exists</span>
                                                            <span th:if="${item.book != null && !item.book.active}">Book is inactive</span>
                                                            <span th:if="${item.book != null && item.book.active && item.book.stockQuantity <= 0}">Out of stock</span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-4 d-flex justify-content-between">
                                <a th:href="@{/buyer/orders}" class="btn btn-outline-secondary rounded-3">
                                    <i class="fas fa-arrow-left"></i> Back to Order History
                                </a>
                                <div class="d-flex gap-2">
                                    <!-- Cancel button only for PROCESSING orders (before shipping) -->
                                    <button th:if="${order.orderStatus != null && order.orderStatus.name() == 'PROCESSING'}"
                                            type="button"
                                            class="btn btn-outline-danger rounded-3 cancel-order-btn"
                                            th:data-order-id="${order.orderId}"
                                            th:data-order-title="${order.orderItems[0].bookTitle + (order.orderItems.size() > 1 ? ' và ' + (order.orderItems.size() - 1) + ' sản phẩm khác' : '')}">
                                        <i class="fas fa-times"></i> Cancel Order
                                    </button>

                                    <!-- Rebuy button for DELIVERED and CANCELLED orders -->
                                    <button th:if="${order.orderStatus != null && (order.orderStatus.name() == 'DELIVERED' || order.orderStatus.name() == 'CANCELLED')}"
                                            type="button"
                                            class="btn btn-primary rounded-3 rebuy-order-btn"
                                            th:data-order-id="${order.orderId}">
                                        <i class="fas fa-sync-alt"></i> Mua lại
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Footer -->
<div th:replace="~{fragments/footer :: footer-content}"></div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script th:src="@{/js/script.js}"></script>
<!-- Initialize Bootstrap components -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        dropdownElementList.forEach(function(dropdownToggleEl) {
            new bootstrap.Dropdown(dropdownToggleEl);
        });

        let currentOrderId = null;

        // Cancel order functionality
        document.querySelectorAll('.cancel-order-btn').forEach(button => {
            button.addEventListener('click', function() {
                currentOrderId = this.getAttribute('data-order-id');
                const orderTitle = this.getAttribute('data-order-title');

                // Set order title in modal
                document.getElementById('cancelOrderTitle').textContent = orderTitle;

                // Reset form
                document.getElementById('cancellationReason').value = '';
                document.getElementById('customReason').value = '';
                document.getElementById('customReasonDiv').style.display = 'none';

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('cancelOrderModal'));
                modal.show();
            });
        });

        // Handle reason dropdown change
        document.getElementById('cancellationReason').addEventListener('change', function() {
            const customReasonDiv = document.getElementById('customReasonDiv');
            if (this.value === 'Khác') {
                customReasonDiv.style.display = 'block';
                document.getElementById('customReason').required = true;
            } else {
                customReasonDiv.style.display = 'none';
                document.getElementById('customReason').required = false;
            }
        });

        // Handle confirm cancel button
        document.getElementById('confirmCancelOrder').addEventListener('click', function() {
            const reasonSelect = document.getElementById('cancellationReason');
            const customReason = document.getElementById('customReason');

            if (!reasonSelect.value) {
                alert('Vui lòng chọn lý do hủy đơn hàng');
                return;
            }

            let finalReason = reasonSelect.value;
            if (reasonSelect.value === 'Khác') {
                if (!customReason.value.trim()) {
                    alert('Vui lòng nhập chi tiết lý do hủy đơn hàng');
                    return;
                }
                finalReason = customReason.value.trim();
            }

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('cancelOrderModal'));
            modal.hide();

            // Cancel order
            cancelOrder(currentOrderId, finalReason);
        });

        // Rebuy order functionality
        document.querySelectorAll('.rebuy-order-btn').forEach(button => {
            button.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                rebuyOrder(orderId);
            });
        });
    });

    function cancelOrder(orderId, reason) {
        // Show loading state
        const button = document.querySelector(`[data-order-id="${orderId}"].cancel-order-btn`);
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang hủy...';
        button.disabled = true;

        fetch('/buyer/cancel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `orderId=${orderId}&reason=${encodeURIComponent(reason)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Đơn hàng đã được hủy thành công!');
                location.reload(); // Refresh page to show updated status
            } else {
                alert('Lỗi: ' + data.message);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi hủy đơn hàng');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    function rebuyOrder(orderId) {
        // Show loading state
        const button = document.querySelector(`[data-order-id="${orderId}"].rebuy-order-btn`);
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
        button.disabled = true;

        fetch('/buyer/rebuy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `orderId=${orderId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Sản phẩm đã được thêm vào giỏ hàng!');
                if (data.redirectUrl) {
                    window.location.href = data.redirectUrl;
                }
            } else {
                alert('Lỗi: ' + data.message);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi mua lại đơn hàng');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
</script>

<!-- Cancel Order Modal -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1" aria-labelledby="cancelOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelOrderModalLabel">
                    <i class="fas fa-times-circle text-danger me-2"></i>Hủy đơn hàng
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Lưu ý:</strong> Sau khi hủy đơn hàng, số lượng sản phẩm sẽ được hoàn lại vào kho và bạn không thể hoàn tác thao tác này.
                </div>

                <div class="mb-3">
                    <strong>Đơn hàng:</strong> <span id="cancelOrderTitle"></span>
                </div>

                <div class="mb-3">
                    <label for="cancellationReason" class="form-label">
                        <i class="fas fa-list me-1"></i>Lý do hủy đơn hàng <span class="text-danger">*</span>
                    </label>
                    <select class="form-select" id="cancellationReason" required>
                        <option value="">-- Chọn lý do hủy đơn hàng --</option>
                        <option value="Đặt sai sản phẩm">Đặt sai sản phẩm</option>
                        <option value="Muốn áp dụng mã giảm giá">Muốn áp dụng mã giảm giá</option>
                        <option value="Không còn nhu cầu">Không còn nhu cầu</option>
                        <option value="Sai địa chỉ giao hàng">Sai địa chỉ giao hàng</option>
                        <option value="Tìm được giá tốt hơn">Tìm được giá tốt hơn</option>
                        <option value="Thay đổi ý định">Thay đổi ý định</option>
                        <option value="Đặt trùng đơn hàng">Đặt trùng đơn hàng</option>
                        <option value="Khác">Khác (vui lòng ghi rõ)</option>
                    </select>
                </div>

                <div class="mb-3" id="customReasonDiv" style="display: none;">
                    <label for="customReason" class="form-label">
                        <i class="fas fa-edit me-1"></i>Chi tiết lý do
                    </label>
                    <textarea class="form-control" id="customReason" rows="3"
                              placeholder="Vui lòng nhập chi tiết lý do hủy đơn hàng..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Hủy bỏ
                </button>
                <button type="button" class="btn btn-danger" id="confirmCancelOrder">
                    <i class="fas fa-check me-1"></i>Xác nhận hủy đơn
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Chat Widget - Buyers Only -->
<div th:replace="~{fragments/chat-widget :: buyer-chat-widget}"></div>

</body>
</html>