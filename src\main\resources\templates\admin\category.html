<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="_csrf" th:content="${_csrf.token}" th:if="${_csrf}" />
    <meta name="_csrf_header" th:content="${_csrf.headerName}" th:if="${_csrf}" />
    <title>Category Management - ReadHub</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
        }
        .table th {
            font-weight: 600;
            color: #2C3E50;
        }
        .category-description {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .action-button {
            margin-right: 5px;
        }
        .sort-icon {
            margin-left: 5px;
        }
        .sticky-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }
        .filter-section {
            background-color: #fff;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .status-filter-buttons {
            display: flex;
            gap: 0.5rem;
        }
        .table-responsive {
            background-color: #fff;
            border-radius: 8px;
            padding: 1rem;
        }
        .pagination {
            margin-bottom: 0;
        }
        .truncate-text {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
        }
    </style>
</head>
<body>
<div th:replace="fragments/admin-topbar :: admin-topbar"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/admin-sidebar :: admin-sidebar(activeMenu='categories')"></div>
        </div>

        <div class="col-lg-9">
            <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show sticky-alert" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <span th:text="${successMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show sticky-alert" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="section-title">Category Management</h3>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus me-2"></i>Add New Category
                </button>
            </div>

            <div class="filter-section mb-4">
                <div class="row g-3">
                    <div class="col-md-6">
                        <form class="d-flex" th:action="@{/admin/categories}" method="get">
                            <input class="form-control me-2" type="search"
                                   placeholder="Search categories..."
                                   name="search"
                                   th:value="${param.search}"
                                   aria-label="Search">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>

                    <div class="col-md-6">
                        <div class="status-filter-buttons">
                            <a th:href="@{/admin/categories(status='all', search=${param.search}, sort=${param.sort}, direction=${param.direction})}"
                               class="btn" th:classappend="${param.status == null || param.status == 'all'} ? 'btn-primary' : 'btn-outline-primary'">
                                All
                            </a>
                            <a th:href="@{/admin/categories(status='active', search=${param.search}, sort=${param.sort}, direction=${param.direction})}"
                               class="btn" th:classappend="${param.status == 'active'} ? 'btn-primary' : 'btn-outline-primary'">
                                Active
                            </a>
                            <a th:href="@{/admin/categories(status='inactive', search=${param.search}, sort=${param.sort}, direction=${param.direction})}"
                               class="btn" th:classappend="${param.status == 'inactive'} ? 'btn-primary' : 'btn-outline-primary'">
                                Inactive
                            </a>

                            <div class="btn-group ms-auto">
                                <a th:href="@{/admin/categories(status=${param.status}, search=${param.search}, sort='categoryName', direction='asc')}"
                                   class="btn" th:classappend="${param.direction == 'asc'} ? 'btn-primary' : 'btn-outline-primary'">
                                    <i class="fas fa-sort-alpha-down"></i>
                                </a>
                                <a th:href="@{/admin/categories(status=${param.status}, search=${param.search}, sort='categoryName', direction='desc')}"
                                   class="btn" th:classappend="${param.direction == 'desc'} ? 'btn-primary' : 'btn-outline-primary'">
                                    <i class="fas fa-sort-alpha-up"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <form id="bulkActionForm" th:action="@{/admin/categories/bulk-action}" method="post">
                    <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" th:if="${_csrf}"/>
                    <table class="table table-hover align-middle">
                        <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>Category Name</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th width="150">Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr th:if="${categories.empty}">
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-2"></i>No categories found
                                </div>
                            </td>
                        </tr>
                        <tr th:each="category : ${categories}">
                            <td>
                                <input type="checkbox" class="form-check-input category-checkbox"
                                       th:value="${category.categoryId}" name="selectedIds">
                            </td>
                            <td th:text="${category.categoryName}">Category Name</td>
                            <td>
                                <span class="truncate-text" th:text="${category.categoryDescription}">Description</span>
                            </td>
                            <td>
                                <span th:if="${category.active}" class="badge bg-success">Active</span>
                                <span th:unless="${category.active}" class="badge bg-secondary">Inactive</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a th:href="@{/admin/categories/{id}(id=${category.categoryId})}"
                                       class="btn btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-primary" title="Edit"
                                            th:onclick="'window.location.href=\'' + @{/admin/categories/{id}(id=${category.categoryId})} + '\''">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            title="Delete Category Permanently"
                                            th:data-id="${category.categoryId}"
                                            onclick="confirmDelete(this.getAttribute('data-id'))">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>

                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button"
                                    id="bulkActionDropdown" data-bs-toggle="dropdown"
                                    aria-expanded="false" disabled>
                                Bulk Actions
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="bulkActionDropdown">
                                <li><a class="dropdown-item" href="#" onclick="submitBulkAction('activate')">
                                    <i class="fas fa-eye me-2"></i>Activate Selected
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="submitBulkAction('deactivate')">
                                    <i class="fas fa-eye-slash me-2"></i>Deactivate Selected
                                </a></li>
                            </ul>
                        </div>

                        <nav th:if="${totalPages > 1}" aria-label="Category pagination">
                            <ul class="pagination pagination-sm">
                                <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/admin/categories(page=${currentPage - 1}, size=10, search=${param.search}, status=${param.status}, sort=${param.sort}, direction=${param.direction})}">&laquo;</a>
                                </li>
                                <li class="page-item" th:each="pageNum : ${#numbers.sequence(0, totalPages - 1)}"
                                    th:classappend="${pageNum == currentPage ? 'active' : ''}">
                                    <a class="page-link" th:href="@{/admin/categories(page=${pageNum}, size=10, search=${param.search}, status=${param.status}, sort=${param.sort}, direction=${param.direction})}"
                                       th:text="${pageNum + 1}">1</a>
                                </li>
                                <li class="page-item" th:classappend="${currentPage == totalPages - 1 ? 'disabled' : ''}">
                                    <a class="page-link" th:href="@{/admin/categories(page=${currentPage + 1}, size=10, search=${param.search}, status=${param.status}, sort=${param.sort}, direction=${param.direction})}">&raquo;</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form th:action="@{/admin/categories}" method="post">
                <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" th:if="${_csrf}"/>
                <div class="modal-header">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" name="categoryName" required>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="categoryDescription" name="categoryDescription" rows="3"></textarea>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="categoryActive" name="active" checked>
                        <label class="form-check-label" for="categoryActive">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select All Functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                document.querySelectorAll('.category-checkbox').forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionButtonState();
            });
        }

        // Update Bulk Action Button State
        function updateBulkActionButtonState() {
            const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
            const bulkActionDropdown = document.getElementById('bulkActionDropdown');
            if (bulkActionDropdown) {
                bulkActionDropdown.disabled = checkedBoxes.length === 0;
            }
        }

        // Add change event listener to all category checkboxes
        document.querySelectorAll('.category-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActionButtonState);
        });
    });

    // Function to submit bulk actions
    function submitBulkAction(action) {
        const form = document.getElementById('bulkActionForm');
        if (!form) return;

        // Check if any checkbox is selected
        const selected = form.querySelectorAll('input[name="selectedIds"]:checked').length;
        if (selected === 0) {
            alert('Please select at least one category.');
            return;
        }

        // Add the action to the form and submit
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = action;
        form.appendChild(actionInput);
        form.submit();
    }

    // Function to handle single item deletion
    function confirmDelete(id) {
        if (confirm('WARNING: Are you sure you want to permanently delete this category? This action cannot be undone.')) {
            // Create a form dynamically to submit a POST request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/categories/${id}/delete`;

            // Get CSRF token from meta tags if they exist
            const csrfToken = document.querySelector('meta[name="_csrf"]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = document.querySelector('meta[name="_csrf_header"]').content;
                csrfInput.value = csrfToken.content;
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
<div th:replace="~{fragments/footer :: footer}"></div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
</body>
</html>