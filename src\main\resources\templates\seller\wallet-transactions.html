<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction History - ReadHub</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Page-specific styles for wallet transactions */
        .balance-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .transaction-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
        }

        .transaction-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }

        .transaction-icon.credit {
            background: #28a745;
        }

        .transaction-icon.debit {
            background: #dc3545;
        }

        .transaction-amount.credit {
            color: #28a745;
            font-weight: bold;
        }

        .transaction-amount.debit {
            color: #dc3545;
            font-weight: bold;
        }

        .filter-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }
    </style>
</head>
<body>

<div class="topbar-wrapper" th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-3 mb-4 sidebar-wrapper">
            <div th:replace="fragments/seller-sidebar :: seller-sidebar"></div>
        </div>

        <div class="col-lg-9 main-content-wrapper">
            <main class="py-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title mb-0">
                        <i class="fas fa-history text-primary me-2"></i>Transaction History
                    </h3>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a th:href="@{/seller/dashboard}">Dashboard</a></li>
                            <li class="breadcrumb-item"><a th:href="@{/seller/wallet}">My Wallet</a></li>
                            <li class="breadcrumb-item active">Transaction History</li>
                        </ol>
                    </nav>
                </div>

                <!-- Error Message -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span th:text="${errorMessage}"></span>
                </div>

                <!-- Balance Summary -->
                <div class="balance-summary">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-1">
                                <i class="fas fa-wallet me-2"></i>
                                Current Balance
                            </h4>
                            <h2 class="mb-0" th:text="${transactionHistory != null ? transactionHistory.formattedBalance : '0 VND'}">0 VND</h2>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a th:href="@{/seller/wallet}" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> Back to Wallet
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Filter Options -->
                <div class="filter-card">
                    <form th:action="@{/seller/wallet/transactions}" method="get" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="typeFilter" class="form-label">Transaction Type</label>
                            <select class="form-select" id="typeFilter" name="type">
                                <option value="">All Transactions</option>
                                <option th:each="type : ${transactionTypes}"
                                        th:value="${type.name()}"
                                        th:text="${type.name()}"
                                        th:selected="${selectedType == type.name()}">Type</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="sizeFilter" class="form-label">Items per page</label>
                            <select class="form-select" id="sizeFilter" name="size">
                                <option value="10" th:selected="${transactionHistory.transactions.size == 10}">10</option>
                                <option value="20" th:selected="${transactionHistory.transactions.size == 20}">20</option>
                                <option value="50" th:selected="${transactionHistory.transactions.size == 50}">50</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-1"></i> Apply Filter
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Transaction List -->
                <div class="card">
                    <div class="card-header bg-white py-3">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    Transaction History
                                    <span class="badge bg-secondary ms-2" th:text="${transactionHistory != null ? transactionHistory.totalElements : 0}">0</span>
                                </h5>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div th:if="${transactionHistory == null || transactionHistory.transactions.empty}" class="text-center py-5 text-muted">
                            <i class="fas fa-receipt fa-4x mb-3"></i>
                            <h5>No transactions found</h5>
                            <p class="mb-0">Your wallet transactions will appear here when you make purchases or receive refunds.</p>
                        </div>

                        <div th:unless="${transactionHistory == null || transactionHistory.transactions.empty}">
                            <div th:each="transaction : ${transactionHistory.transactions.content}" class="transaction-item">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <div class="transaction-icon"
                                             th:classappend="${transaction.transactionType.name().toLowerCase()}">
                                            <i th:class="${transaction.transactionType.name() == 'CREDIT'} ? 'fas fa-plus' : 'fas fa-minus'"></i>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="fw-bold mb-1" th:text="${transaction.description}">Transaction Description</div>
                                        <div class="row text-muted small">
                                            <div class="col-md-6">
                                                <i class="fas fa-calendar me-1"></i>
                                                <span th:text="${#temporals.format(transaction.createdAt, 'dd/MM/yyyy HH:mm')}">Date</span>
                                            </div>
                                            <div class="col-md-6" th:if="${transaction.referenceType}">
                                                <i class="fas fa-tag me-1"></i>
                                                <span th:text="${transaction.referenceType.name()}">Reference Type</span>
                                                <span th:if="${transaction.referenceId}" class="ms-1">
                                                    #<span th:text="${transaction.referenceId}">ID</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-auto text-end">
                                        <div class="transaction-amount fw-bold"
                                             th:classappend="${transaction.transactionType.name().toLowerCase()}">
                                            <span th:if="${transaction.transactionType.name() == 'CREDIT'}">+</span>
                                            <span th:if="${transaction.transactionType.name() == 'DEBIT'}">-</span>
                                            <span th:text="${#numbers.formatDecimal(transaction.amount, 0, 'COMMA', 0, 'POINT')} + ' VND'">Amount</span>
                                        </div>
                                        <div class="text-muted small">
                                            Balance: <span th:text="${#numbers.formatDecimal(transaction.balanceAfter, 0, 'COMMA', 0, 'POINT')} + ' VND'">Balance</span>
                                        </div>
                                        <div class="mt-1">
                                            <span class="badge bg-success" th:if="${transaction.status.name() == 'COMPLETED'}">Completed</span>
                                            <span class="badge bg-warning" th:if="${transaction.status.name() == 'PENDING'}">Pending</span>
                                            <span class="badge bg-danger" th:if="${transaction.status.name() == 'FAILED'}">Failed</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div th:if="${transactionHistory != null && transactionHistory.totalPages > 1}" class="pagination-wrapper">
                    <nav aria-label="Transaction pagination">
                        <ul class="pagination">
                            <li class="page-item" th:classappend="${transactionHistory == null || !transactionHistory.hasPrevious} ? 'disabled'">
                                <a class="page-link"
                                   th:href="@{/seller/wallet/transactions(page=${transactionHistory != null ? transactionHistory.transactions.number - 1 : 0}, size=${transactionHistory != null ? transactionHistory.transactions.size : 10}, type=${selectedType})}">
                                    Previous
                                </a>
                            </li>

                            <li th:each="pageNum : ${transactionHistory != null ? #numbers.sequence(0, transactionHistory.totalPages - 1) : #numbers.sequence(0, 0)}"
                                class="page-item"
                                th:classappend="${transactionHistory != null && pageNum == transactionHistory.transactions.number} ? 'active'">
                                <a class="page-link"
                                   th:href="@{/seller/wallet/transactions(page=${pageNum}, size=${transactionHistory != null ? transactionHistory.transactions.size : 10}, type=${selectedType})}"
                                   th:text="${pageNum + 1}">1</a>
                            </li>

                            <li class="page-item" th:classappend="${transactionHistory == null || !transactionHistory.hasNext} ? 'disabled'">
                                <a class="page-link"
                                   th:href="@{/seller/wallet/transactions(page=${transactionHistory != null ? transactionHistory.transactions.number + 1 : 0}, size=${transactionHistory != null ? transactionHistory.transactions.size : 10}, type=${selectedType})}">
                                    Next
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </main>
        </div>
    </div>
</div>

<div th:replace="~{fragments/footer :: footer}"></div>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

</body>
</html>
