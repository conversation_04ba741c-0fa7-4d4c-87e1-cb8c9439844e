-- Create system_settings table for storing application configuration
-- This table stores key-value pairs for system-wide settings like hero section content, contact info, etc.
USE isp392


CREATE TABLE system_settings (
    setting_key NVARCHAR(100) NOT NULL PRIMARY KEY,
    setting_value NVARCHAR(MAX) NULL
);

-- Create index for better performance
CREATE INDEX IX_system_settings_setting_key ON system_settings(setting_key);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value) VALUES
('hero_background_image', '/images/hero-bg.jpg'),
('hero_title', 'Welcome to ReadHub'),
('hero_description', 'Discover your next favorite book from our vast collection'),
('hero_button_text', 'Shop Now'),
('hero_button_link', '/books'),
('contact_email', '<EMAIL>'),
('contact_province', 'Ho Chi Minh City'),
('contact_district', 'District 1'),
('contact_ward', 'Ben Nghe Ward'),
('social_facebook', 'https://facebook.com/readhub'),
('social_instagram', 'https://instagram.com/readhub'),
('social_zalo', 'https://zalo.me/readhub');

-- Add comments for documentation
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'System-wide configuration settings stored as key-value pairs', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'system_settings';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Unique identifier for the setting (e.g., hero_title, contact_email)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'system_settings', 
    @level2type = N'COLUMN', @level2name = N'setting_key';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'The value of the setting (can be text, URLs, HTML content, etc.)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'system_settings', 
    @level2type = N'COLUMN', @level2name = N'setting_value';
