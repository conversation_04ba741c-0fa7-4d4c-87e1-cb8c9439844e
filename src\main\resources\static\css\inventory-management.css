/* Inventory Management Styles */

.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

.product-image-placeholder {
    width: 50px;
    height: 50px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.product-info h6 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.stock-input-group {
    position: relative;
}

.stock-input {
    width: 80px;
    text-align: center;
    font-weight: 600;
}

.stock-input.changed {
    border-color: #ffc107;
    background-color: #fff3cd;
}

.stock-input.error {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.update-stock-btn {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.update-stock-btn.enabled {
    opacity: 1;
}

.reset-stock-btn {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.reset-stock-btn.enabled {
    opacity: 1;
}

/* Statistics Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-title i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* Table Styles */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Checkbox Styles */
.product-checkbox {
    transform: scale(1.2);
}

#headerCheckbox, #selectAll {
    transform: scale(1.2);
}

/* Filter and Sort Controls */
.form-select {
    font-size: 0.875rem;
}

.input-group .form-control {
    border-right: none;
}

.input-group .btn-outline-secondary {
    border-left: none;
    background-color: #fff;
}

/* Modal Styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

#selectedProductsList {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
}

.selected-product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.selected-product-info {
    flex-grow: 1;
}

.selected-product-stock {
    width: 80px;
    margin-left: 1rem;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Success/Error Messages */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn-toolbar {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-group {
        width: 100%;
    }
    
    .btn-group .btn {
        flex: 1;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .product-info h6 {
        font-size: 0.8rem;
    }
    
    .stock-input {
        width: 60px;
        font-size: 0.8rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
    
    .pagination {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* Animation for stock changes */
@keyframes stockChange {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.stock-input.animate {
    animation: stockChange 0.3s ease-in-out;
}

/* Bulk update progress */
.bulk-update-progress {
    margin-top: 1rem;
}

.progress {
    height: 0.5rem;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-in-stock {
    background-color: #28a745;
}

.status-low-stock {
    background-color: #ffc107;
}

.status-out-of-stock {
    background-color: #dc3545;
}
