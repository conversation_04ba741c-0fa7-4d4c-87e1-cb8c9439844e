<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - ReadHub Seller</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Open+Sans:wght@400;600&display=swap"
          rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/seller-style.css}">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #F8F5F0;
            color: #333;
        }
        .navbar-brand {
            font-family: 'Lora', serif;
            font-weight: 700;
            font-size: 1.8rem;
            color: #2C3E50;
        }
        .section-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #2C3E50;
            border-bottom: 2px solid #2C3E50;
            padding-bottom: 0.5rem;
        }
        .account-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .default-profile {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #E9ECEF;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #6c757d;
            border: 3px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-primary:hover {
            background-color: #1e2b37;
            border-color: #1e2b37;
        }
        .btn-outline-primary {
            color: #2C3E50;
            border-color: #2C3E50;
        }
        .btn-outline-primary:hover {
            background-color: #2C3E50;
            border-color: #2C3E50;
        }
        .account-sidebar {
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
        }
        .account-sidebar .list-group-item {
            border: none;
            border-left: 4px solid transparent;
            font-weight: 600;
        }
        .account-sidebar .list-group-item.active {
            background-color: #f8f9fa;
            color: #2C3E50;
            border-left: 4px solid #2C3E50;
        }
        .account-sidebar .list-group-item i {
            margin-right: 10px;
            color: #6c757d;
        }
        .account-sidebar .list-group-item.active i {
            color: #2C3E50;
        }
        .stat-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #2C3E50;
        }
        .stat-card h4 {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-card p {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0;
            color: #2C3E50;
        }
        .stat-card i {
            font-size: 2.5rem;
            color: #e9ecef;
            position: absolute;
            right: 1.5rem;
        }

        /* Additional styles for new components */
        .notification-card {
            border-left: 4px solid #dc3545;
        }

        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 2rem;
        }

        .quick-action-btn {
            width: 100%;
            margin-bottom: 1rem;
            text-align: left;
            padding: 1rem;
        }

        .quick-action-btn i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>

<!-- Seller Topbar -->
<div th:replace="fragments/seller-topbar :: seller-topbar"></div>

<div class="container my-4">
    <div class="row">
        <div class="col-lg-3 mb-4">
            <div th:replace="fragments/seller-account-sidebar :: seller-sidebar"></div>
        </div>

        <!-- Change Password Content -->
        <div class="col-lg-9">
            <!-- Success and Error Messages -->
            <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i> <span th:text="${successMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i> <span th:text="${errorMessage}"></span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>

            <!-- Password Change Form -->
            <div class="account-container">
                <h3 class="section-title">Change Password</h3>

                <!-- Google OAuth2 warning for password change -->
                <div th:if="${isOAuth2User}" class="alert alert-warning mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i> You're signed in with Google. Password changes are not allowed. Please manage your password through your Google account settings.
                </div>

                <form th:action="@{/seller/update-password}" method="post">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label fw-bold">Current Password</label>
                        <input type="password" class="form-control" id="currentPassword" name="currentPassword"
                               th:disabled="${isOAuth2User}" required>
                        <div class="invalid-feedback">Please enter your current password</div>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label fw-bold">New Password</label>
                        <input type="password" class="form-control" id="newPassword" name="newPassword"
                               th:disabled="${isOAuth2User}" required>
                        <div class="form-text">Password must be at least 6 characters long and include at least
                            one number and one letter.
                        </div>
                        <div class="invalid-feedback">Password must meet the requirements</div>
                    </div>
                    <div class="mb-4">
                        <label for="confirmPassword" class="form-label fw-bold">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword"
                               th:disabled="${isOAuth2User}" required>
                        <div class="invalid-feedback">Passwords do not match</div>
                    </div>
                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary" th:disabled="${isOAuth2User}">
                            <i class="fas fa-key me-2"></i> Change Password
                        </button>
                        <a th:href="@{/seller/account}" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-arrow-left me-2"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script th:src="@{/js/seller.js}"></script>
<!-- Password edit validation - reusing existing file -->
<script th:src="@{/js/password-change-validation.js}"></script>
</body>
</html>